"""
Robustness testing framework for strategy validation and stress testing.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from decimal import Decimal
import random
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
from dataclasses import dataclass

from app.core.config import settings
from app.schemas.backtest import MonteCarloConfig, WalkForwardConfig, StressTestConfig
from app.engine.performance import PerformanceAnalytics

logger = logging.getLogger(__name__)


@dataclass
class MonteCarloResult:
    """Monte Carlo simulation result."""
    run_id: int
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    final_equity: float
    parameters_used: Dict[str, Any]


@dataclass
class WalkForwardResult:
    """Walk-forward analysis result."""
    period_id: int
    training_start: datetime
    training_end: datetime
    test_start: datetime
    test_end: datetime
    in_sample_metrics: Dict[str, float]
    out_of_sample_metrics: Dict[str, float]
    parameter_stability: Dict[str, float]
    degradation_factor: float


@dataclass
class StressTestResult:
    """Stress test result."""
    scenario_name: str
    original_metrics: Dict[str, float]
    stressed_metrics: Dict[str, float]
    impact_analysis: Dict[str, float]
    survival_probability: float


class BootstrapGenerator:
    """Bootstrap data generation for robustness testing."""
    
    def __init__(self, method: str = "block", block_size: int = 20):
        """Initialize bootstrap generator."""
        self.method = method
        self.block_size = block_size
    
    def generate_bootstrap_returns(self, returns: pd.Series, n_samples: int) -> pd.Series:
        """Generate bootstrap sample of returns."""
        if self.method == "block":
            return self._block_bootstrap(returns, n_samples)
        elif self.method == "stationary":
            return self._stationary_bootstrap(returns, n_samples)
        else:
            return self._simple_bootstrap(returns, n_samples)
    
    def _simple_bootstrap(self, returns: pd.Series, n_samples: int) -> pd.Series:
        """Simple bootstrap resampling."""
        bootstrap_indices = np.random.choice(len(returns), size=n_samples, replace=True)
        return returns.iloc[bootstrap_indices].reset_index(drop=True)
    
    def _block_bootstrap(self, returns: pd.Series, n_samples: int) -> pd.Series:
        """Block bootstrap to preserve time series structure."""
        n_blocks = int(np.ceil(n_samples / self.block_size))
        bootstrap_returns = []
        
        for _ in range(n_blocks):
            start_idx = np.random.randint(0, len(returns) - self.block_size + 1)
            block = returns.iloc[start_idx:start_idx + self.block_size]
            bootstrap_returns.extend(block.values)
        
        return pd.Series(bootstrap_returns[:n_samples])
    
    def _stationary_bootstrap(self, returns: pd.Series, n_samples: int) -> pd.Series:
        """Stationary bootstrap with geometric block lengths."""
        bootstrap_returns = []
        current_length = 0
        
        while current_length < n_samples:
            # Geometric distribution for block length
            block_length = np.random.geometric(1.0 / self.block_size)
            start_idx = np.random.randint(0, len(returns))
            
            for i in range(block_length):
                if current_length >= n_samples:
                    break
                idx = (start_idx + i) % len(returns)
                bootstrap_returns.append(returns.iloc[idx])
                current_length += 1
        
        return pd.Series(bootstrap_returns[:n_samples])


class MonteCarloSimulator:
    """Monte Carlo simulation for strategy robustness testing."""
    
    def __init__(self, config: MonteCarloConfig):
        """Initialize Monte Carlo simulator."""
        self.config = config
        self.bootstrap_generator = BootstrapGenerator(
            method=getattr(config, 'bootstrap_method', 'block'),
            block_size=getattr(config, 'block_size', 20)
        )
    
    def run_simulation(
        self,
        original_returns: pd.Series,
        strategy_function: Callable,
        base_parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> List[MonteCarloResult]:
        """Run Monte Carlo simulation."""
        results = []
        
        for run_id in range(self.config.simulation_runs):
            try:
                # Generate bootstrap sample
                if self.config.randomize_returns:
                    bootstrap_returns = self.bootstrap_generator.generate_bootstrap_returns(
                        original_returns, len(original_returns)
                    )
                else:
                    bootstrap_returns = original_returns.copy()
                
                # Randomize parameters if enabled
                if self.config.randomize_parameters:
                    parameters = self._randomize_parameters(base_parameters)
                else:
                    parameters = base_parameters.copy()
                
                # Run strategy simulation
                simulation_result = strategy_function(bootstrap_returns, parameters)
                
                # Create result
                result = MonteCarloResult(
                    run_id=run_id,
                    total_return=simulation_result.get('total_return', 0.0),
                    sharpe_ratio=simulation_result.get('sharpe_ratio', 0.0),
                    max_drawdown=simulation_result.get('max_drawdown', 0.0),
                    win_rate=simulation_result.get('win_rate', 0.0),
                    profit_factor=simulation_result.get('profit_factor', 0.0),
                    final_equity=simulation_result.get('final_equity', 100000.0),
                    parameters_used=parameters
                )
                
                results.append(result)
                
                # Progress callback
                if progress_callback:
                    progress_callback(run_id + 1, self.config.simulation_runs)
                
            except Exception as e:
                logger.warning(f"Monte Carlo run {run_id} failed: {e}")
                continue
        
        return results
    
    def _randomize_parameters(self, base_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Randomize strategy parameters within reasonable bounds."""
        randomized = base_parameters.copy()
        
        for key, value in base_parameters.items():
            if isinstance(value, (int, float)):
                # Add ±20% random variation
                variation = random.uniform(-0.2, 0.2)
                new_value = value * (1 + variation)
                
                # Ensure positive values stay positive
                if value > 0:
                    new_value = max(new_value, value * 0.1)
                
                randomized[key] = type(value)(new_value)
        
        return randomized
    
    def analyze_results(self, results: List[MonteCarloResult]) -> Dict[str, Any]:
        """Analyze Monte Carlo simulation results."""
        if not results:
            return {}
        
        # Extract metrics
        total_returns = [r.total_return for r in results]
        sharpe_ratios = [r.sharpe_ratio for r in results]
        max_drawdowns = [r.max_drawdown for r in results]
        win_rates = [r.win_rate for r in results]
        profit_factors = [r.profit_factor for r in results]
        
        # Calculate statistics
        analysis = {
            'total_runs': len(results),
            'success_rate': len([r for r in results if r.total_return > 0]) / len(results),
            
            # Total return statistics
            'total_return': {
                'mean': np.mean(total_returns),
                'std': np.std(total_returns),
                'min': np.min(total_returns),
                'max': np.max(total_returns),
                'percentiles': {
                    f'p{int(p*100)}': np.percentile(total_returns, p*100)
                    for p in self.config.confidence_intervals
                }
            },
            
            # Sharpe ratio statistics
            'sharpe_ratio': {
                'mean': np.mean(sharpe_ratios),
                'std': np.std(sharpe_ratios),
                'min': np.min(sharpe_ratios),
                'max': np.max(sharpe_ratios),
                'percentiles': {
                    f'p{int(p*100)}': np.percentile(sharpe_ratios, p*100)
                    for p in self.config.confidence_intervals
                }
            },
            
            # Max drawdown statistics
            'max_drawdown': {
                'mean': np.mean(max_drawdowns),
                'std': np.std(max_drawdowns),
                'min': np.min(max_drawdowns),
                'max': np.max(max_drawdowns),
                'percentiles': {
                    f'p{int(p*100)}': np.percentile(max_drawdowns, p*100)
                    for p in self.config.confidence_intervals
                }
            }
        }
        
        return analysis


class WalkForwardAnalyzer:
    """Walk-forward analysis for out-of-sample validation."""
    
    def __init__(self, config: WalkForwardConfig):
        """Initialize walk-forward analyzer."""
        self.config = config
    
    def run_analysis(
        self,
        market_data: pd.DataFrame,
        strategy_function: Callable,
        optimization_function: Callable,
        base_parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> List[WalkForwardResult]:
        """Run walk-forward analysis."""
        results = []
        
        # Calculate analysis periods
        periods = self._calculate_periods(market_data)
        
        for i, period in enumerate(periods):
            try:
                # Extract training and test data
                training_data = market_data[
                    (market_data.index >= period['training_start']) &
                    (market_data.index <= period['training_end'])
                ]
                
                test_data = market_data[
                    (market_data.index >= period['test_start']) &
                    (market_data.index <= period['test_end'])
                ]
                
                # Optimize parameters on training data
                if self.config.reoptimize_parameters:
                    optimized_params = optimization_function(training_data, base_parameters)
                else:
                    optimized_params = base_parameters.copy()
                
                # Test on training data (in-sample)
                in_sample_result = strategy_function(training_data, optimized_params)
                
                # Test on test data (out-of-sample)
                out_of_sample_result = strategy_function(test_data, optimized_params)
                
                # Calculate parameter stability
                param_stability = self._calculate_parameter_stability(
                    base_parameters, optimized_params
                )
                
                # Calculate degradation factor
                degradation = self._calculate_degradation_factor(
                    in_sample_result, out_of_sample_result
                )
                
                result = WalkForwardResult(
                    period_id=i,
                    training_start=period['training_start'],
                    training_end=period['training_end'],
                    test_start=period['test_start'],
                    test_end=period['test_end'],
                    in_sample_metrics=in_sample_result,
                    out_of_sample_metrics=out_of_sample_result,
                    parameter_stability=param_stability,
                    degradation_factor=degradation
                )
                
                results.append(result)
                
                # Progress callback
                if progress_callback:
                    progress_callback(i + 1, len(periods))
                
            except Exception as e:
                logger.warning(f"Walk-forward period {i} failed: {e}")
                continue
        
        return results
    
    def _calculate_periods(self, market_data: pd.DataFrame) -> List[Dict[str, datetime]]:
        """Calculate walk-forward analysis periods."""
        periods = []
        start_date = market_data.index[0]
        end_date = market_data.index[-1]
        
        current_date = start_date
        
        while current_date + timedelta(days=self.config.training_window_days + self.config.test_window_days) <= end_date:
            training_start = current_date
            training_end = current_date + timedelta(days=self.config.training_window_days)
            test_start = training_end + timedelta(days=1)
            test_end = test_start + timedelta(days=self.config.test_window_days)
            
            periods.append({
                'training_start': training_start,
                'training_end': training_end,
                'test_start': test_start,
                'test_end': test_end
            })
            
            current_date += timedelta(days=self.config.step_size_days)
        
        return periods
    
    def _calculate_parameter_stability(
        self,
        base_params: Dict[str, Any],
        optimized_params: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate parameter stability metrics."""
        stability = {}
        
        for key in base_params:
            if key in optimized_params:
                base_val = base_params[key]
                opt_val = optimized_params[key]
                
                if isinstance(base_val, (int, float)) and base_val != 0:
                    change_pct = abs(opt_val - base_val) / abs(base_val)
                    stability[key] = 1.0 - min(change_pct, 1.0)  # 1 = stable, 0 = completely changed
                else:
                    stability[key] = 1.0 if base_val == opt_val else 0.0
        
        return stability
    
    def _calculate_degradation_factor(
        self,
        in_sample: Dict[str, float],
        out_of_sample: Dict[str, float]
    ) -> float:
        """Calculate performance degradation from in-sample to out-of-sample."""
        # Use Sharpe ratio as primary metric for degradation
        in_sample_sharpe = in_sample.get('sharpe_ratio', 0.0)
        out_of_sample_sharpe = out_of_sample.get('sharpe_ratio', 0.0)
        
        if in_sample_sharpe <= 0:
            return 1.0  # No meaningful degradation if in-sample was poor
        
        degradation = 1.0 - (out_of_sample_sharpe / in_sample_sharpe)
        return max(0.0, min(degradation, 2.0))  # Cap between 0 and 2


class StressTester:
    """Stress testing framework for extreme market scenarios."""
    
    def __init__(self, config: StressTestConfig):
        """Initialize stress tester."""
        self.config = config
        self.scenarios = {**settings.STRESS_TEST_SCENARIOS, **config.custom_scenarios}
    
    def run_stress_tests(
        self,
        market_data: pd.DataFrame,
        strategy_function: Callable,
        parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> List[StressTestResult]:
        """Run stress tests on strategy."""
        results = []
        
        # Run baseline (original) scenario
        baseline_result = strategy_function(market_data, parameters)
        
        for i, scenario_name in enumerate(self.config.scenarios):
            if scenario_name not in self.scenarios:
                logger.warning(f"Unknown stress scenario: {scenario_name}")
                continue
            
            try:
                # Apply stress scenario to market data
                stressed_data = self._apply_stress_scenario(
                    market_data, scenario_name, self.scenarios[scenario_name]
                )
                
                # Run strategy on stressed data
                stressed_result = strategy_function(stressed_data, parameters)
                
                # Analyze impact
                impact_analysis = self._analyze_impact(baseline_result, stressed_result)
                
                # Calculate survival probability
                survival_prob = self._calculate_survival_probability(stressed_result)
                
                result = StressTestResult(
                    scenario_name=scenario_name,
                    original_metrics=baseline_result,
                    stressed_metrics=stressed_result,
                    impact_analysis=impact_analysis,
                    survival_probability=survival_prob
                )
                
                results.append(result)
                
                # Progress callback
                if progress_callback:
                    progress_callback(i + 1, len(self.config.scenarios))
                
            except Exception as e:
                logger.warning(f"Stress test {scenario_name} failed: {e}")
                continue
        
        return results
    
    def _apply_stress_scenario(
        self,
        market_data: pd.DataFrame,
        scenario_name: str,
        scenario_params: Dict[str, float]
    ) -> pd.DataFrame:
        """Apply stress scenario to market data."""
        stressed_data = market_data.copy()
        
        # Calculate returns
        returns = stressed_data['close'].pct_change().dropna()
        
        # Apply return shock
        if self.config.apply_to_returns and 'return_shock' in scenario_params:
            shock = scenario_params['return_shock']
            # Apply shock to all returns
            stressed_returns = returns + shock / len(returns)  # Distribute shock over period
            
            # Reconstruct prices
            new_prices = [stressed_data['close'].iloc[0]]
            for ret in stressed_returns:
                new_prices.append(new_prices[-1] * (1 + ret))
            
            stressed_data['close'] = new_prices[:len(stressed_data)]
            
            # Adjust OHLC proportionally
            price_ratio = stressed_data['close'] / market_data['close']
            stressed_data['open'] *= price_ratio
            stressed_data['high'] *= price_ratio
            stressed_data['low'] *= price_ratio
        
        # Apply volatility shock
        if self.config.apply_to_volatility and 'volatility_multiplier' in scenario_params:
            vol_mult = scenario_params['volatility_multiplier']
            
            # Increase volatility by scaling deviations from trend
            trend = stressed_data['close'].rolling(20).mean()
            deviations = stressed_data['close'] - trend
            stressed_deviations = deviations * vol_mult
            
            stressed_data['close'] = trend + stressed_deviations
            
            # Adjust OHLC
            price_ratio = stressed_data['close'] / market_data['close']
            stressed_data['open'] *= price_ratio
            stressed_data['high'] *= price_ratio
            stressed_data['low'] *= price_ratio
        
        return stressed_data
    
    def _analyze_impact(
        self,
        baseline: Dict[str, float],
        stressed: Dict[str, float]
    ) -> Dict[str, float]:
        """Analyze impact of stress scenario."""
        impact = {}
        
        for metric in ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']:
            baseline_val = baseline.get(metric, 0.0)
            stressed_val = stressed.get(metric, 0.0)
            
            if baseline_val != 0:
                impact[f'{metric}_change_pct'] = (stressed_val - baseline_val) / abs(baseline_val)
            else:
                impact[f'{metric}_change_pct'] = 0.0
            
            impact[f'{metric}_absolute_change'] = stressed_val - baseline_val
        
        return impact
    
    def _calculate_survival_probability(self, stressed_result: Dict[str, float]) -> float:
        """Calculate probability of strategy survival under stress."""
        # Simple survival metric based on multiple factors
        total_return = stressed_result.get('total_return', 0.0)
        max_drawdown = stressed_result.get('max_drawdown', 1.0)
        sharpe_ratio = stressed_result.get('sharpe_ratio', 0.0)
        
        # Survival factors
        return_factor = 1.0 if total_return > -0.5 else 0.0  # Survive if loss < 50%
        drawdown_factor = 1.0 if max_drawdown < 0.8 else 0.0  # Survive if drawdown < 80%
        sharpe_factor = 1.0 if sharpe_ratio > -2.0 else 0.0  # Survive if Sharpe > -2
        
        # Weighted average
        survival_prob = (return_factor * 0.4 + drawdown_factor * 0.4 + sharpe_factor * 0.2)
        
        return survival_prob


class RobustnessTestingFramework:
    """Main robustness testing framework coordinating all components."""
    
    def __init__(self):
        """Initialize robustness testing framework."""
        self.performance_analytics = PerformanceAnalytics()
    
    async def cleanup(self):
        """Cleanup robustness testing resources."""
        await self.performance_analytics.cleanup()
    
    def run_monte_carlo_analysis(
        self,
        config: MonteCarloConfig,
        original_returns: pd.Series,
        strategy_function: Callable,
        base_parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Run Monte Carlo robustness analysis."""
        simulator = MonteCarloSimulator(config)
        results = simulator.run_simulation(
            original_returns, strategy_function, base_parameters, progress_callback
        )
        analysis = simulator.analyze_results(results)
        
        return {
            'config': config.model_dump(),
            'results': [
                {
                    'run_id': r.run_id,
                    'total_return': r.total_return,
                    'sharpe_ratio': r.sharpe_ratio,
                    'max_drawdown': r.max_drawdown,
                    'win_rate': r.win_rate,
                    'profit_factor': r.profit_factor,
                    'parameters_used': r.parameters_used
                }
                for r in results
            ],
            'analysis': analysis
        }
    
    def run_walk_forward_analysis(
        self,
        config: WalkForwardConfig,
        market_data: pd.DataFrame,
        strategy_function: Callable,
        optimization_function: Callable,
        base_parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Run walk-forward robustness analysis."""
        analyzer = WalkForwardAnalyzer(config)
        results = analyzer.run_analysis(
            market_data, strategy_function, optimization_function, base_parameters, progress_callback
        )
        
        return {
            'config': config.model_dump(),
            'results': [
                {
                    'period_id': r.period_id,
                    'training_start': r.training_start.isoformat(),
                    'training_end': r.training_end.isoformat(),
                    'test_start': r.test_start.isoformat(),
                    'test_end': r.test_end.isoformat(),
                    'in_sample_metrics': r.in_sample_metrics,
                    'out_of_sample_metrics': r.out_of_sample_metrics,
                    'parameter_stability': r.parameter_stability,
                    'degradation_factor': r.degradation_factor
                }
                for r in results
            ]
        }
    
    def run_stress_tests(
        self,
        config: StressTestConfig,
        market_data: pd.DataFrame,
        strategy_function: Callable,
        parameters: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Run stress testing analysis."""
        tester = StressTester(config)
        results = tester.run_stress_tests(
            market_data, strategy_function, parameters, progress_callback
        )
        
        return {
            'config': config.model_dump(),
            'results': [
                {
                    'scenario_name': r.scenario_name,
                    'original_metrics': r.original_metrics,
                    'stressed_metrics': r.stressed_metrics,
                    'impact_analysis': r.impact_analysis,
                    'survival_probability': r.survival_probability
                }
                for r in results
            ]
        }
