"""
Real-time market data management with WebSocket connections.
"""

import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from decimal import Decimal

import httpx
import websockets

from app.core.config import settings
from app.schemas.execution import MarketDataSnapshot, AssetClass

logger = logging.getLogger(__name__)


class MarketDataManager:
    """Real-time market data management."""
    
    def __init__(self):
        """Initialize market data manager."""
        self.subscriptions = {}
        self.market_data_cache = {}
        self.callbacks = []
        self.websocket_connections = {}
        self.running = False
        
    async def initialize(self):
        """Initialize market data connections."""
        try:
            self.running = True
            
            # Initialize data providers
            await self._initialize_providers()
            
            logger.info("Market data manager initialized")
            
        except Exception as e:
            logger.error(f"Market data manager initialization failed: {e}")
            raise
    
    async def _initialize_providers(self):
        """Initialize market data providers."""
        # TODO: Initialize actual market data providers
        logger.info("Market data providers initialized")
    
    async def subscribe(self, symbol: str, asset_class: AssetClass, callback: Optional[Callable] = None):
        """Subscribe to market data for symbol."""
        try:
            subscription_key = f"{symbol}_{asset_class.value}"
            
            if subscription_key not in self.subscriptions:
                self.subscriptions[subscription_key] = {
                    "symbol": symbol,
                    "asset_class": asset_class,
                    "callbacks": [],
                    "last_update": None
                }
                
                # Start data feed for this symbol
                asyncio.create_task(self._start_data_feed(symbol, asset_class))
            
            if callback:
                self.subscriptions[subscription_key]["callbacks"].append(callback)
            
            logger.info(f"Subscribed to market data for {symbol}")
            
        except Exception as e:
            logger.error(f"Market data subscription failed: {e}")
    
    async def _start_data_feed(self, symbol: str, asset_class: AssetClass):
        """Start market data feed for symbol."""
        try:
            # Simulate market data updates
            while self.running:
                # Generate mock market data
                market_data = self._generate_mock_data(symbol, asset_class)
                
                # Update cache
                subscription_key = f"{symbol}_{asset_class.value}"
                self.market_data_cache[subscription_key] = market_data
                
                # Notify callbacks
                if subscription_key in self.subscriptions:
                    subscription = self.subscriptions[subscription_key]
                    subscription["last_update"] = datetime.utcnow()
                    
                    for callback in subscription["callbacks"]:
                        try:
                            await callback(market_data)
                        except Exception as e:
                            logger.error(f"Market data callback failed: {e}")
                
                # Notify global callbacks
                for callback in self.callbacks:
                    try:
                        await callback(symbol, market_data.__dict__)
                    except Exception as e:
                        logger.error(f"Global market data callback failed: {e}")
                
                await asyncio.sleep(1)  # Update every second
                
        except Exception as e:
            logger.error(f"Market data feed failed for {symbol}: {e}")
    
    def _generate_mock_data(self, symbol: str, asset_class: AssetClass) -> MarketDataSnapshot:
        """Generate mock market data for testing."""
        import random
        
        # Base price (would come from real data)
        base_price = 100.0
        
        # Add some randomness
        price_change = random.uniform(-0.02, 0.02)  # ±2%
        last_price = Decimal(str(base_price * (1 + price_change)))
        
        spread = Decimal(str(random.uniform(0.01, 0.05)))  # 1-5 cent spread
        bid_price = last_price - spread / 2
        ask_price = last_price + spread / 2
        
        volume = Decimal(str(random.randint(1000, 10000)))
        
        return MarketDataSnapshot(
            symbol=symbol,
            asset_class=asset_class,
            last_price=last_price,
            bid_price=bid_price,
            ask_price=ask_price,
            bid_size=Decimal(str(random.randint(100, 1000))),
            ask_size=Decimal(str(random.randint(100, 1000))),
            open_price=Decimal(str(base_price)),
            high_price=last_price * Decimal("1.01"),
            low_price=last_price * Decimal("0.99"),
            close_price=last_price,
            volume=volume,
            avg_volume=volume * Decimal("1.2"),
            spread=spread,
            spread_bps=spread / last_price * 10000,
            volatility=Decimal(str(random.uniform(0.1, 0.3))),
            timestamp=datetime.utcnow(),
            market_status="OPEN"
        )
    
    async def get_market_data(self, symbol: str, asset_class: AssetClass) -> Optional[MarketDataSnapshot]:
        """Get current market data for symbol."""
        subscription_key = f"{symbol}_{asset_class.value}"
        return self.market_data_cache.get(subscription_key)
    
    async def unsubscribe(self, symbol: str, asset_class: AssetClass, callback: Optional[Callable] = None):
        """Unsubscribe from market data."""
        subscription_key = f"{symbol}_{asset_class.value}"
        
        if subscription_key in self.subscriptions:
            if callback and callback in self.subscriptions[subscription_key]["callbacks"]:
                self.subscriptions[subscription_key]["callbacks"].remove(callback)
            
            # Remove subscription if no callbacks left
            if not self.subscriptions[subscription_key]["callbacks"]:
                del self.subscriptions[subscription_key]
                if subscription_key in self.market_data_cache:
                    del self.market_data_cache[subscription_key]
                
                logger.info(f"Unsubscribed from market data for {symbol}")
    
    def add_global_callback(self, callback: Callable):
        """Add global market data callback."""
        self.callbacks.append(callback)
    
    def remove_global_callback(self, callback: Callable):
        """Remove global market data callback."""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    async def get_subscription_status(self) -> Dict[str, Any]:
        """Get market data subscription status."""
        return {
            "total_subscriptions": len(self.subscriptions),
            "subscriptions": [
                {
                    "symbol": sub["symbol"],
                    "asset_class": sub["asset_class"].value,
                    "callback_count": len(sub["callbacks"]),
                    "last_update": sub["last_update"].isoformat() if sub["last_update"] else None
                }
                for sub in self.subscriptions.values()
            ],
            "cache_size": len(self.market_data_cache),
            "running": self.running
        }
    
    async def cleanup(self):
        """Cleanup market data manager."""
        self.running = False
        
        # Close websocket connections
        for connection in self.websocket_connections.values():
            try:
                await connection.close()
            except Exception as e:
                logger.error(f"Error closing websocket: {e}")
        
        # Clear subscriptions and cache
        self.subscriptions.clear()
        self.market_data_cache.clear()
        self.callbacks.clear()
        
        logger.info("Market data manager cleaned up")
