#!/bin/bash

# AthenaTrader Development Environment Setup Script

set -e

echo "🚀 Setting up AthenaTrader development environment..."

# Check if required tools are installed
check_requirements() {
    echo "📋 Checking requirements..."
    
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3.11+ is required but not installed."
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 18+ is required but not installed."
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is required but not installed."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose is required but not installed."
        exit 1
    fi
    
    echo "✅ All requirements satisfied."
}

# Create virtual environment
setup_python_env() {
    echo "🐍 Setting up Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install -r requirements-dev.txt
    
    echo "✅ Python environment ready."
}

# Setup frontend dependencies
setup_frontend() {
    echo "⚛️ Setting up frontend dependencies..."
    
    if [ ! -d "frontend/node_modules" ]; then
        cd frontend
        npm install
        cd ..
    fi
    
    echo "✅ Frontend dependencies installed."
}

# Create environment file
setup_env() {
    echo "🔧 Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        echo "📝 Created .env file from template. Please update with your API keys."
    fi
    
    echo "✅ Environment configuration ready."
}

# Setup pre-commit hooks
setup_hooks() {
    echo "🪝 Setting up pre-commit hooks..."
    
    source venv/bin/activate
    pre-commit install
    
    echo "✅ Pre-commit hooks installed."
}

# Create necessary directories
create_directories() {
    echo "📁 Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data/market_data
    mkdir -p data/models
    mkdir -p data/backtests
    
    echo "✅ Directories created."
}

# Setup database
setup_database() {
    echo "🗄️ Setting up database..."
    
    # Start database services
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    echo "⏳ Waiting for database to be ready..."
    sleep 10
    
    echo "✅ Database services started."
}

# Run initial tests
run_tests() {
    echo "🧪 Running initial tests..."
    
    source venv/bin/activate
    
    # Run linting
    echo "🔍 Running code quality checks..."
    black --check . || echo "⚠️ Code formatting issues found. Run 'black .' to fix."
    isort --check-only . || echo "⚠️ Import sorting issues found. Run 'isort .' to fix."
    flake8 . || echo "⚠️ Linting issues found."
    
    echo "✅ Initial tests completed."
}

# Main setup function
main() {
    echo "🎯 Starting AthenaTrader development setup..."
    
    check_requirements
    setup_python_env
    setup_env
    create_directories
    setup_hooks
    setup_database
    
    echo ""
    echo "🎉 Development environment setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Update .env file with your API keys"
    echo "2. Run 'docker-compose up' to start all services"
    echo "3. Visit http://localhost:3000 for the frontend"
    echo "4. Visit http://localhost:8000/docs for API documentation"
    echo ""
    echo "Happy coding! 🚀"
}

# Run main function
main "$@"
