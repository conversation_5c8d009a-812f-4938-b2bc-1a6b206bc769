export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_expert: boolean;
  created_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface AuthError {
  code: number;
  message: string;
  request_id?: string;
}
