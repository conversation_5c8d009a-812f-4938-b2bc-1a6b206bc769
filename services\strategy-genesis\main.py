"""
AthenaTrader Strategy Genesis Service

AI-driven strategy creation and optimization service that implements
multi-paradigm AI approaches including Genetic Programming, Reinforcement Learning,
and Deep Learning for trading strategy development.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import health, strategies, genetic_programming, reinforcement_learning, deep_learning

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader Strategy Genesis Service...")
    await init_db()
    logger.info("Database initialized")
    
    # Initialize AI modules
    from app.ai.genetic_programming import GeneticProgrammingEngine
    from app.ai.reinforcement_learning import ReinforcementLearningEngine
    from app.ai.deep_learning import DeepLearningEngine
    
    # Store engines in app state
    app.state.gp_engine = GeneticProgrammingEngine()
    app.state.rl_engine = ReinforcementLearningEngine()
    app.state.dl_engine = DeepLearningEngine()
    
    logger.info("AI engines initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader Strategy Genesis Service...")
    
    # Cleanup AI engines
    if hasattr(app.state, 'rl_engine'):
        await app.state.rl_engine.cleanup()
    if hasattr(app.state, 'dl_engine'):
        await app.state.dl_engine.cleanup()


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader Strategy Genesis",
    description="AI-driven strategy creation and optimization service for AthenaTrader",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
app.include_router(genetic_programming.router, prefix="/genetic-programming", tags=["genetic-programming"])
app.include_router(reinforcement_learning.router, prefix="/reinforcement-learning", tags=["reinforcement-learning"])
app.include_router(deep_learning.router, prefix="/deep-learning", tags=["deep-learning"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader Strategy Genesis Service",
        "version": "0.1.0",
        "status": "operational",
        "ai_paradigms": ["genetic_programming", "reinforcement_learning", "deep_learning"]
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
