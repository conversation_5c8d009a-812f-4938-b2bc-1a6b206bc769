"""
Strategy models for AI-driven trading strategy development.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, String, <PERSON>olean, DateTime, Numeric, Text, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Strategy(Base):
    """AI-generated trading strategy model."""
    
    __tablename__ = "strategies"
    __table_args__ = (
        {"schema": "strategies"},
        Index("idx_strategies_user_id", "user_id"),
        Index("idx_strategies_ai_paradigm", "ai_paradigm"),
        Index("idx_strategies_status", "status"),
        Index("idx_strategies_performance", "performance_score"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # Reference to auth.users
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # AI Paradigm Information
    ai_paradigm = Column(String(50), nullable=False)  # GP, RL, DL, HYBRID
    paradigm_config = Column(JSONB, nullable=False, default=dict)
    
    # Strategy Configuration
    parameters = Column(JSONB, nullable=False, default=dict)
    trading_rules = Column(JSONB, nullable=True)  # For GP: expression trees, for others: rule sets
    model_architecture = Column(JSONB, nullable=True)  # For DL: network architecture
    
    # Performance Metrics
    performance_metrics = Column(JSONB, nullable=True, default=dict)
    performance_score = Column(Numeric(10, 6), nullable=True)  # Composite performance score
    
    # Training Information
    training_data_start = Column(DateTime(timezone=True), nullable=True)
    training_data_end = Column(DateTime(timezone=True), nullable=True)
    training_duration_seconds = Column(Integer, nullable=True)
    training_episodes = Column(Integer, nullable=True)  # For RL
    training_epochs = Column(Integer, nullable=True)    # For DL
    
    # Status and Lifecycle
    status = Column(String(50), default="CREATED", nullable=False)  # CREATED, TRAINING, TRAINED, DEPLOYED, ARCHIVED
    is_active = Column(Boolean, default=True, nullable=False)
    version = Column(Integer, default=1, nullable=False)
    
    # Model Storage
    model_path = Column(String(500), nullable=True)  # Path to saved model files
    checkpoint_path = Column(String(500), nullable=True)  # Path to training checkpoints
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    trained_at = Column(DateTime(timezone=True), nullable=True)
    deployed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    versions = relationship("StrategyVersion", back_populates="strategy", cascade="all, delete-orphan")
    training_runs = relationship("StrategyTrainingRun", back_populates="strategy", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Strategy(id={self.id}, name={self.name}, paradigm={self.ai_paradigm})>"


class StrategyVersion(Base):
    """Strategy version control for tracking evolution and A/B testing."""
    
    __tablename__ = "strategy_versions"
    __table_args__ = (
        {"schema": "strategies"},
        Index("idx_strategy_versions_strategy_id", "strategy_id"),
        Index("idx_strategy_versions_version", "version_number"),
        Index("idx_strategy_versions_performance", "performance_score"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey("strategies.strategies.id"), nullable=False)
    version_number = Column(Integer, nullable=False)
    
    # Version-specific configuration
    parameters = Column(JSONB, nullable=False)
    trading_rules = Column(JSONB, nullable=True)
    model_architecture = Column(JSONB, nullable=True)
    
    # Performance for this version
    performance_metrics = Column(JSONB, nullable=True, default=dict)
    performance_score = Column(Numeric(10, 6), nullable=True)
    
    # Training information for this version
    training_data_start = Column(DateTime(timezone=True), nullable=True)
    training_data_end = Column(DateTime(timezone=True), nullable=True)
    training_duration_seconds = Column(Integer, nullable=True)
    
    # Model storage for this version
    model_path = Column(String(500), nullable=True)
    
    # Metadata
    change_description = Column(Text, nullable=True)
    is_baseline = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    strategy = relationship("Strategy", back_populates="versions")
    
    def __repr__(self) -> str:
        return f"<StrategyVersion(id={self.id}, strategy_id={self.strategy_id}, version={self.version_number})>"


class StrategyTrainingRun(Base):
    """Training run tracking for AI strategy development."""
    
    __tablename__ = "strategy_training_runs"
    __table_args__ = (
        {"schema": "strategies"},
        Index("idx_training_runs_strategy_id", "strategy_id"),
        Index("idx_training_runs_status", "status"),
        Index("idx_training_runs_start_time", "start_time"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey("strategies.strategies.id"), nullable=False)
    
    # Training configuration
    training_config = Column(JSONB, nullable=False)
    hyperparameters = Column(JSONB, nullable=False, default=dict)
    
    # Training data information
    data_start_date = Column(DateTime(timezone=True), nullable=False)
    data_end_date = Column(DateTime(timezone=True), nullable=False)
    instruments_used = Column(JSONB, nullable=False)  # List of instrument IDs
    
    # Training progress
    status = Column(String(50), default="PENDING", nullable=False)  # PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
    current_epoch = Column(Integer, default=0, nullable=False)
    total_epochs = Column(Integer, nullable=False)
    current_generation = Column(Integer, default=0, nullable=False)  # For GP
    total_generations = Column(Integer, nullable=True)  # For GP
    
    # Performance tracking
    training_metrics = Column(JSONB, nullable=True, default=dict)  # Loss, accuracy, etc.
    validation_metrics = Column(JSONB, nullable=True, default=dict)
    best_performance = Column(Numeric(10, 6), nullable=True)
    
    # Resource usage
    cpu_usage_percent = Column(Numeric(5, 2), nullable=True)
    memory_usage_mb = Column(Integer, nullable=True)
    gpu_usage_percent = Column(Numeric(5, 2), nullable=True)
    
    # Timing
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    estimated_completion = Column(DateTime(timezone=True), nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_traceback = Column(Text, nullable=True)
    
    # Checkpoints
    checkpoint_path = Column(String(500), nullable=True)
    last_checkpoint_time = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    strategy = relationship("Strategy", back_populates="training_runs")
    
    def __repr__(self) -> str:
        return f"<StrategyTrainingRun(id={self.id}, strategy_id={self.strategy_id}, status={self.status})>"


class AIParadigm(Base):
    """AI paradigm configuration and metadata."""
    
    __tablename__ = "ai_paradigms"
    __table_args__ = {"schema": "strategies"}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), unique=True, nullable=False)  # GP, RL, DL, HYBRID
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Configuration
    default_config = Column(JSONB, nullable=False, default=dict)
    supported_parameters = Column(JSONB, nullable=False, default=list)
    
    # Capabilities
    supports_online_learning = Column(Boolean, default=False, nullable=False)
    supports_multi_asset = Column(Boolean, default=True, nullable=False)
    supports_multi_timeframe = Column(Boolean, default=True, nullable=False)
    
    # Resource requirements
    min_memory_mb = Column(Integer, default=512, nullable=False)
    recommended_memory_mb = Column(Integer, default=2048, nullable=False)
    requires_gpu = Column(Boolean, default=False, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<AIParadigm(id={self.id}, name={self.name})>"
