"""
Audit trail and compliance management for XAI operations.
"""

import logging
import asyncio
import hashlib
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import get_audit_logger
from app.models.explanation import AuditTrail
from app.schemas.explanation import ComplianceStatus

logger = logging.getLogger(__name__)
audit_logger = get_audit_logger()


class BiasDetector:
    """Detect bias in model predictions and explanations."""
    
    def __init__(self):
        """Initialize bias detector."""
        self.bias_threshold = settings.BIAS_DETECTION_THRESHOLD
    
    def detect_bias(
        self,
        predictions: np.ndarray,
        explanations: Dict[str, Any],
        protected_attributes: Optional[Dict[str, np.ndarray]] = None,
        sensitive_features: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Detect various types of bias in model outputs."""
        
        try:
            bias_results = {
                "bias_detected": False,
                "bias_types": [],
                "bias_scores": {},
                "affected_groups": [],
                "mitigation_suggestions": []
            }
            
            # Statistical parity bias
            if protected_attributes:
                statistical_parity = self._check_statistical_parity(predictions, protected_attributes)
                bias_results["bias_scores"]["statistical_parity"] = statistical_parity
                
                if statistical_parity["max_disparity"] > self.bias_threshold:
                    bias_results["bias_detected"] = True
                    bias_results["bias_types"].append("STATISTICAL_PARITY")
                    bias_results["affected_groups"].extend(statistical_parity["disparate_groups"])
            
            # Feature importance bias
            if explanations and "feature_importance" in explanations:
                feature_bias = self._check_feature_importance_bias(
                    explanations["feature_importance"], sensitive_features
                )
                bias_results["bias_scores"]["feature_importance"] = feature_bias
                
                if feature_bias["bias_score"] > self.bias_threshold:
                    bias_results["bias_detected"] = True
                    bias_results["bias_types"].append("FEATURE_IMPORTANCE")
            
            # Explanation consistency bias
            if explanations and "shap_values" in explanations:
                consistency_bias = self._check_explanation_consistency(explanations["shap_values"])
                bias_results["bias_scores"]["explanation_consistency"] = consistency_bias
                
                if consistency_bias["inconsistency_score"] > self.bias_threshold:
                    bias_results["bias_detected"] = True
                    bias_results["bias_types"].append("EXPLANATION_CONSISTENCY")
            
            # Generate mitigation suggestions
            if bias_results["bias_detected"]:
                bias_results["mitigation_suggestions"] = self._generate_mitigation_suggestions(
                    bias_results["bias_types"]
                )
            
            return bias_results
            
        except Exception as e:
            logger.error(f"Bias detection failed: {e}")
            return {
                "error": str(e),
                "bias_detected": False,
                "bias_types": []
            }
    
    def _check_statistical_parity(
        self,
        predictions: np.ndarray,
        protected_attributes: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """Check for statistical parity violations."""
        
        results = {
            "max_disparity": 0.0,
            "disparate_groups": [],
            "group_statistics": {}
        }
        
        for attr_name, attr_values in protected_attributes.items():
            unique_groups = np.unique(attr_values)
            group_rates = {}
            
            for group in unique_groups:
                group_mask = attr_values == group
                group_predictions = predictions[group_mask]
                
                if len(group_predictions) > 0:
                    positive_rate = np.mean(group_predictions > 0.5)  # Assuming binary classification
                    group_rates[str(group)] = positive_rate
            
            # Calculate disparities
            if len(group_rates) > 1:
                rates = list(group_rates.values())
                max_rate = max(rates)
                min_rate = min(rates)
                disparity = max_rate - min_rate
                
                results["group_statistics"][attr_name] = group_rates
                
                if disparity > results["max_disparity"]:
                    results["max_disparity"] = disparity
                
                if disparity > self.bias_threshold:
                    results["disparate_groups"].append(attr_name)
        
        return results
    
    def _check_feature_importance_bias(
        self,
        feature_importance: Dict[str, float],
        sensitive_features: Optional[List[str]]
    ) -> Dict[str, Any]:
        """Check for bias in feature importance."""
        
        if not sensitive_features:
            return {"bias_score": 0.0, "sensitive_feature_importance": {}}
        
        sensitive_importance = {}
        total_importance = sum(abs(imp) for imp in feature_importance.values())
        
        for feature in sensitive_features:
            if feature in feature_importance:
                importance = abs(feature_importance[feature])
                relative_importance = importance / total_importance if total_importance > 0 else 0
                sensitive_importance[feature] = relative_importance
        
        # Calculate bias score as maximum sensitive feature importance
        bias_score = max(sensitive_importance.values()) if sensitive_importance else 0.0
        
        return {
            "bias_score": bias_score,
            "sensitive_feature_importance": sensitive_importance,
            "total_sensitive_importance": sum(sensitive_importance.values())
        }
    
    def _check_explanation_consistency(self, shap_values: List[float]) -> Dict[str, Any]:
        """Check for consistency in explanations."""
        
        if not shap_values or len(shap_values) < 2:
            return {"inconsistency_score": 0.0}
        
        # Calculate variance in SHAP values as a measure of inconsistency
        shap_array = np.array(shap_values)
        variance = np.var(shap_array)
        mean_abs_shap = np.mean(np.abs(shap_array))
        
        # Normalize by mean absolute SHAP value
        inconsistency_score = variance / (mean_abs_shap + 1e-10)
        
        return {
            "inconsistency_score": inconsistency_score,
            "shap_variance": variance,
            "mean_abs_shap": mean_abs_shap
        }
    
    def _generate_mitigation_suggestions(self, bias_types: List[str]) -> List[str]:
        """Generate bias mitigation suggestions."""
        
        suggestions = []
        
        if "STATISTICAL_PARITY" in bias_types:
            suggestions.extend([
                "Consider rebalancing training data across protected groups",
                "Apply fairness constraints during model training",
                "Use post-processing techniques to equalize group outcomes"
            ])
        
        if "FEATURE_IMPORTANCE" in bias_types:
            suggestions.extend([
                "Remove or reduce weight of sensitive features",
                "Apply feature selection techniques to reduce bias",
                "Use fairness-aware feature engineering"
            ])
        
        if "EXPLANATION_CONSISTENCY" in bias_types:
            suggestions.extend([
                "Increase explanation sample size for more stable results",
                "Use ensemble explanation methods",
                "Apply explanation smoothing techniques"
            ])
        
        return suggestions


class ComplianceValidator:
    """Validate compliance with regulatory frameworks."""
    
    def __init__(self):
        """Initialize compliance validator."""
        self.frameworks = settings.REGULATORY_FRAMEWORKS
        self.confidence_level = settings.VALIDATION_CONFIDENCE_LEVEL
    
    def validate_compliance(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any],
        framework: str = "MIFID_II"
    ) -> Dict[str, Any]:
        """Validate compliance with regulatory framework."""
        
        try:
            if framework not in self.frameworks:
                return {"error": f"Framework {framework} not supported"}
            
            # Route to framework-specific validation
            if framework == "MIFID_II":
                return self._validate_mifid_ii(explanation_data, model_metadata)
            elif framework == "GDPR":
                return self._validate_gdpr(explanation_data, model_metadata)
            elif framework == "FINRA":
                return self._validate_finra(explanation_data, model_metadata)
            elif framework == "SEC":
                return self._validate_sec(explanation_data, model_metadata)
            else:
                return self._validate_generic(explanation_data, model_metadata, framework)
            
        except Exception as e:
            logger.error(f"Compliance validation failed: {e}")
            return {
                "error": str(e),
                "compliance_status": ComplianceStatus.NON_COMPLIANT,
                "framework": framework
            }
    
    def _validate_mifid_ii(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate MiFID II compliance."""
        
        compliance_checks = {
            "algorithmic_trading_disclosure": False,
            "model_transparency": False,
            "risk_management": False,
            "audit_trail": False
        }
        
        violations = []
        
        # Check for model transparency
        if "feature_importance" in explanation_data or "shap_explanation" in explanation_data:
            compliance_checks["model_transparency"] = True
        else:
            violations.append("Insufficient model transparency - explanations required")
        
        # Check for audit trail
        if "audit_trail_id" in model_metadata:
            compliance_checks["audit_trail"] = True
        else:
            violations.append("Missing audit trail documentation")
        
        # Check for risk management
        if "risk_metrics" in model_metadata:
            compliance_checks["risk_management"] = True
        else:
            violations.append("Risk management documentation required")
        
        # Determine overall compliance
        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)
        
        if compliance_score >= 0.8:
            status = ComplianceStatus.COMPLIANT
        elif compliance_score >= 0.6:
            status = ComplianceStatus.UNDER_REVIEW
        else:
            status = ComplianceStatus.NON_COMPLIANT
        
        return {
            "framework": "MIFID_II",
            "compliance_status": status,
            "compliance_score": compliance_score,
            "compliance_checks": compliance_checks,
            "violations": violations,
            "recommendations": self._generate_mifid_recommendations(violations)
        }
    
    def _validate_gdpr(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate GDPR compliance."""
        
        compliance_checks = {
            "right_to_explanation": False,
            "data_protection": False,
            "consent_management": False,
            "data_minimization": False
        }
        
        violations = []
        
        # Check right to explanation
        if explanation_data and ("feature_importance" in explanation_data or "lime_explanation" in explanation_data):
            compliance_checks["right_to_explanation"] = True
        else:
            violations.append("Right to explanation not adequately addressed")
        
        # Check data protection measures
        if model_metadata.get("privacy_level") in ["CONFIDENTIAL", "RESTRICTED"]:
            compliance_checks["data_protection"] = True
        else:
            violations.append("Insufficient data protection measures")
        
        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)
        
        if compliance_score >= 0.75:
            status = ComplianceStatus.COMPLIANT
        else:
            status = ComplianceStatus.NON_COMPLIANT
        
        return {
            "framework": "GDPR",
            "compliance_status": status,
            "compliance_score": compliance_score,
            "compliance_checks": compliance_checks,
            "violations": violations
        }
    
    def _validate_finra(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate FINRA compliance."""
        
        # Simplified FINRA validation
        compliance_checks = {
            "model_documentation": bool(explanation_data),
            "risk_controls": "risk_metrics" in model_metadata,
            "supervisory_review": "validation_status" in model_metadata
        }
        
        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)
        status = ComplianceStatus.COMPLIANT if compliance_score >= 0.8 else ComplianceStatus.NON_COMPLIANT
        
        return {
            "framework": "FINRA",
            "compliance_status": status,
            "compliance_score": compliance_score,
            "compliance_checks": compliance_checks
        }
    
    def _validate_sec(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate SEC compliance."""
        
        # Simplified SEC validation
        compliance_checks = {
            "disclosure_requirements": bool(explanation_data),
            "fiduciary_duty": "risk_metrics" in model_metadata,
            "record_keeping": "audit_trail_id" in model_metadata
        }
        
        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)
        status = ComplianceStatus.COMPLIANT if compliance_score >= 0.8 else ComplianceStatus.NON_COMPLIANT
        
        return {
            "framework": "SEC",
            "compliance_status": status,
            "compliance_score": compliance_score,
            "compliance_checks": compliance_checks
        }
    
    def _validate_generic(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any],
        framework: str
    ) -> Dict[str, Any]:
        """Generic compliance validation."""
        
        compliance_checks = {
            "transparency": bool(explanation_data),
            "documentation": bool(model_metadata),
            "auditability": "audit_trail_id" in model_metadata
        }
        
        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)
        status = ComplianceStatus.COMPLIANT if compliance_score >= 0.7 else ComplianceStatus.NON_COMPLIANT
        
        return {
            "framework": framework,
            "compliance_status": status,
            "compliance_score": compliance_score,
            "compliance_checks": compliance_checks
        }
    
    def _generate_mifid_recommendations(self, violations: List[str]) -> List[str]:
        """Generate MiFID II compliance recommendations."""
        
        recommendations = []
        
        for violation in violations:
            if "transparency" in violation.lower():
                recommendations.append("Implement comprehensive model explanation capabilities")
            elif "audit" in violation.lower():
                recommendations.append("Establish detailed audit trail procedures")
            elif "risk" in violation.lower():
                recommendations.append("Document risk management procedures and controls")
        
        return recommendations


class AuditTrailManager:
    """Manage comprehensive audit trails for XAI operations."""
    
    def __init__(self):
        """Initialize audit trail manager."""
        self.bias_detector = BiasDetector()
        self.compliance_validator = ComplianceValidator()
    
    async def cleanup(self):
        """Cleanup audit manager resources."""
        pass
    
    async def log_explanation_request(
        self,
        db: AsyncSession,
        user_id: uuid.UUID,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        execution_time_ms: int,
        session_id: Optional[uuid.UUID] = None,
        request_id: Optional[uuid.UUID] = None
    ) -> uuid.UUID:
        """Log explanation request to audit trail."""
        
        try:
            # Create data hashes
            input_hash = self._create_data_hash(request_data)
            output_hash = self._create_data_hash(response_data)
            
            # Determine compliance status
            compliance_status = ComplianceStatus.PENDING
            if "error" not in response_data:
                # Run basic compliance check
                compliance_result = self.compliance_validator.validate_compliance(
                    response_data, request_data
                )
                compliance_status = compliance_result.get("compliance_status", ComplianceStatus.PENDING)
            
            # Create audit trail entry
            audit_entry = AuditTrail(
                user_id=user_id,
                session_id=session_id,
                request_id=request_id or uuid.uuid4(),
                action_type="EXPLANATION_REQUEST",
                action_category="EXPLANATION",
                action_description=f"Explanation request for strategy {request_data.get('strategy_id')}",
                strategy_id=request_data.get("strategy_id"),
                request_parameters=request_data,
                input_data_hash=input_hash,
                response_status="SUCCESS" if "error" not in response_data else "FAILURE",
                response_data_hash=output_hash,
                execution_time_ms=execution_time_ms,
                explanation_type=request_data.get("explanation_type"),
                explanation_quality=response_data.get("explanation_quality_score"),
                regulatory_framework=request_data.get("regulatory_framework"),
                compliance_status=compliance_status.value,
                data_retention_period=settings.AUDIT_RETENTION_DAYS,
                privacy_level=request_data.get("privacy_level", "INTERNAL"),
                risk_level=self._assess_risk_level(request_data, response_data),
                service_version=settings.VERSION,
                environment=settings.ENVIRONMENT,
                retention_until=datetime.utcnow() + timedelta(days=settings.AUDIT_RETENTION_DAYS)
            )
            
            db.add(audit_entry)
            await db.commit()
            await db.refresh(audit_entry)
            
            # Log to audit logger
            audit_logger.info(
                f"Explanation request logged - User: {user_id}, "
                f"Strategy: {request_data.get('strategy_id')}, "
                f"Type: {request_data.get('explanation_type')}, "
                f"Status: {audit_entry.response_status}"
            )
            
            return audit_entry.id
            
        except Exception as e:
            logger.error(f"Failed to log explanation request: {e}")
            raise
    
    async def log_model_prediction(
        self,
        db: AsyncSession,
        user_id: uuid.UUID,
        model_id: str,
        prediction_data: Dict[str, Any],
        model_metadata: Dict[str, Any],
        session_id: Optional[uuid.UUID] = None
    ) -> uuid.UUID:
        """Log model prediction to audit trail."""
        
        try:
            # Create audit trail entry
            audit_entry = AuditTrail(
                user_id=user_id,
                session_id=session_id,
                request_id=uuid.uuid4(),
                action_type="MODEL_PREDICTION",
                action_category="PREDICTION",
                action_description=f"Model prediction for {model_id}",
                model_id=model_id,
                request_parameters=prediction_data,
                input_data_hash=self._create_data_hash(prediction_data),
                response_status="SUCCESS",
                model_version=model_metadata.get("version"),
                ai_paradigm=model_metadata.get("ai_paradigm"),
                prediction_value=prediction_data.get("prediction"),
                confidence_score=prediction_data.get("confidence"),
                privacy_level="INTERNAL",
                risk_level=self._assess_prediction_risk(prediction_data),
                service_version=settings.VERSION,
                environment=settings.ENVIRONMENT,
                retention_until=datetime.utcnow() + timedelta(days=settings.AUDIT_RETENTION_DAYS)
            )
            
            db.add(audit_entry)
            await db.commit()
            await db.refresh(audit_entry)
            
            return audit_entry.id
            
        except Exception as e:
            logger.error(f"Failed to log model prediction: {e}")
            raise
    
    async def run_bias_detection(
        self,
        predictions: np.ndarray,
        explanations: Dict[str, Any],
        protected_attributes: Optional[Dict[str, np.ndarray]] = None,
        sensitive_features: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Run bias detection analysis."""
        
        try:
            bias_results = self.bias_detector.detect_bias(
                predictions, explanations, protected_attributes, sensitive_features
            )
            
            # Log bias detection results
            if bias_results.get("bias_detected"):
                audit_logger.warning(
                    f"Bias detected - Types: {bias_results['bias_types']}, "
                    f"Affected groups: {bias_results['affected_groups']}"
                )
            
            return bias_results
            
        except Exception as e:
            logger.error(f"Bias detection failed: {e}")
            return {"error": str(e), "bias_detected": False}
    
    async def validate_model_compliance(
        self,
        explanation_data: Dict[str, Any],
        model_metadata: Dict[str, Any],
        framework: str = "MIFID_II"
    ) -> Dict[str, Any]:
        """Validate model compliance with regulatory framework."""
        
        try:
            compliance_result = self.compliance_validator.validate_compliance(
                explanation_data, model_metadata, framework
            )
            
            # Log compliance validation
            audit_logger.info(
                f"Compliance validation - Framework: {framework}, "
                f"Status: {compliance_result.get('compliance_status')}, "
                f"Score: {compliance_result.get('compliance_score')}"
            )
            
            return compliance_result
            
        except Exception as e:
            logger.error(f"Compliance validation failed: {e}")
            return {"error": str(e), "compliance_status": ComplianceStatus.NON_COMPLIANT}
    
    def _create_data_hash(self, data: Dict[str, Any]) -> str:
        """Create SHA-256 hash of data."""
        
        try:
            # Convert to JSON string and create hash
            data_str = json.dumps(data, sort_keys=True, default=str)
            return hashlib.sha256(data_str.encode()).hexdigest()
        except Exception:
            return ""
    
    def _assess_risk_level(
        self,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any]
    ) -> str:
        """Assess risk level of explanation request."""
        
        # Simple risk assessment based on data sensitivity
        if request_data.get("privacy_level") == "RESTRICTED":
            return "HIGH"
        elif "error" in response_data:
            return "MEDIUM"
        elif request_data.get("explanation_type") in ["COUNTERFACTUAL", "WHAT_IF"]:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _assess_prediction_risk(self, prediction_data: Dict[str, Any]) -> str:
        """Assess risk level of model prediction."""
        
        confidence = prediction_data.get("confidence", 1.0)
        
        if confidence < 0.5:
            return "HIGH"
        elif confidence < 0.7:
            return "MEDIUM"
        else:
            return "LOW"
