"""
Visualization engine for interactive XAI dashboards and charts.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import json
import base64
from io import BytesIO

# Visualization libraries
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("Plotly not available - install for interactive visualizations")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logging.warning("Matplotlib/Seaborn not available - install for static visualizations")

from app.core.config import settings

logger = logging.getLogger(__name__)


class SHAPVisualizer:
    """Visualizations for SHAP explanations."""
    
    def __init__(self):
        """Initialize SHAP visualizer."""
        pass
    
    def create_shap_waterfall(
        self,
        shap_values: List[float],
        feature_names: List[str],
        feature_values: List[float],
        base_value: float,
        prediction: float
    ) -> Dict[str, Any]:
        """Create SHAP waterfall chart."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for SHAP waterfall chart"}
        
        try:
            # Prepare data for waterfall chart
            x_labels = ["Base"] + feature_names + ["Prediction"]
            y_values = [base_value] + shap_values + [prediction]
            
            # Calculate cumulative values for waterfall effect
            cumulative = [base_value]
            for shap_val in shap_values:
                cumulative.append(cumulative[-1] + shap_val)
            cumulative.append(prediction)
            
            # Create waterfall chart
            fig = go.Figure()
            
            # Add bars for each feature
            for i, (name, shap_val, cum_val) in enumerate(zip(x_labels[1:-1], shap_values, cumulative[1:-1])):
                color = "green" if shap_val > 0 else "red"
                fig.add_trace(go.Bar(
                    x=[name],
                    y=[abs(shap_val)],
                    base=[cum_val - shap_val if shap_val > 0 else cum_val],
                    marker_color=color,
                    name=f"{name}: {shap_val:.4f}",
                    text=f"{shap_val:.4f}",
                    textposition="middle center"
                ))
            
            # Add base and prediction bars
            fig.add_trace(go.Bar(
                x=["Base"],
                y=[base_value],
                marker_color="blue",
                name=f"Base: {base_value:.4f}"
            ))
            
            fig.add_trace(go.Bar(
                x=["Prediction"],
                y=[prediction],
                marker_color="purple",
                name=f"Prediction: {prediction:.4f}"
            ))
            
            fig.update_layout(
                title="SHAP Waterfall Chart",
                xaxis_title="Features",
                yaxis_title="Value",
                showlegend=False,
                height=600
            )
            
            return {
                "chart_type": "shap_waterfall",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"SHAP waterfall chart creation failed: {e}")
            return {"error": str(e)}
    
    def create_shap_summary_plot(
        self,
        shap_values: np.ndarray,
        feature_names: List[str],
        feature_values: np.ndarray
    ) -> Dict[str, Any]:
        """Create SHAP summary plot."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for SHAP summary plot"}
        
        try:
            # Calculate feature importance
            importance = np.abs(shap_values).mean(axis=0)
            
            # Sort features by importance
            sorted_indices = np.argsort(importance)[::-1]
            sorted_features = [feature_names[i] for i in sorted_indices]
            sorted_importance = importance[sorted_indices]
            
            # Create horizontal bar chart
            fig = go.Figure()
            
            fig.add_trace(go.Bar(
                y=sorted_features,
                x=sorted_importance,
                orientation='h',
                marker_color='skyblue',
                text=[f"{imp:.4f}" for imp in sorted_importance],
                textposition="outside"
            ))
            
            fig.update_layout(
                title="SHAP Feature Importance Summary",
                xaxis_title="Mean |SHAP Value|",
                yaxis_title="Features",
                height=max(400, len(sorted_features) * 30)
            )
            
            return {
                "chart_type": "shap_summary",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json(),
                "feature_importance": dict(zip(sorted_features, sorted_importance))
            }
            
        except Exception as e:
            logger.error(f"SHAP summary plot creation failed: {e}")
            return {"error": str(e)}


class FeatureImportanceVisualizer:
    """Visualizations for feature importance analysis."""
    
    def __init__(self):
        """Initialize feature importance visualizer."""
        pass
    
    def create_importance_comparison(
        self,
        importance_data: Dict[str, Dict[str, float]],
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """Create comparison chart for different importance methods."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for importance comparison"}
        
        try:
            fig = go.Figure()
            
            # Add trace for each importance method
            for method, importances in importance_data.items():
                values = [importances.get(feature, 0.0) for feature in feature_names]
                
                fig.add_trace(go.Bar(
                    name=method,
                    x=feature_names,
                    y=values,
                    text=[f"{val:.4f}" for val in values],
                    textposition="outside"
                ))
            
            fig.update_layout(
                title="Feature Importance Comparison",
                xaxis_title="Features",
                yaxis_title="Importance Score",
                barmode='group',
                height=600,
                xaxis_tickangle=-45
            )
            
            return {
                "chart_type": "importance_comparison",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Importance comparison chart creation failed: {e}")
            return {"error": str(e)}
    
    def create_importance_heatmap(
        self,
        importance_matrix: np.ndarray,
        feature_names: List[str],
        method_names: List[str]
    ) -> Dict[str, Any]:
        """Create heatmap for feature importance across methods."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for importance heatmap"}
        
        try:
            fig = go.Figure(data=go.Heatmap(
                z=importance_matrix,
                x=feature_names,
                y=method_names,
                colorscale='Viridis',
                text=importance_matrix,
                texttemplate="%{text:.3f}",
                textfont={"size": 10}
            ))
            
            fig.update_layout(
                title="Feature Importance Heatmap",
                xaxis_title="Features",
                yaxis_title="Methods",
                height=max(400, len(method_names) * 50),
                xaxis_tickangle=-45
            )
            
            return {
                "chart_type": "importance_heatmap",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Importance heatmap creation failed: {e}")
            return {"error": str(e)}


class AttributionVisualizer:
    """Visualizations for performance attribution analysis."""
    
    def __init__(self):
        """Initialize attribution visualizer."""
        pass
    
    def create_attribution_breakdown(
        self,
        attribution_data: Dict[str, float],
        total_return: float
    ) -> Dict[str, Any]:
        """Create attribution breakdown chart."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for attribution breakdown"}
        
        try:
            factors = list(attribution_data.keys())
            attributions = list(attribution_data.values())
            
            # Create pie chart for attribution breakdown
            fig = go.Figure(data=[go.Pie(
                labels=factors,
                values=[abs(attr) for attr in attributions],
                hole=0.3,
                textinfo='label+percent',
                textposition='outside'
            )])
            
            fig.update_layout(
                title=f"Performance Attribution Breakdown (Total Return: {total_return:.2%})",
                height=500
            )
            
            return {
                "chart_type": "attribution_breakdown",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Attribution breakdown chart creation failed: {e}")
            return {"error": str(e)}
    
    def create_temporal_attribution(
        self,
        temporal_data: pd.DataFrame,
        attribution_columns: List[str]
    ) -> Dict[str, Any]:
        """Create temporal attribution chart."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for temporal attribution"}
        
        try:
            fig = go.Figure()
            
            # Add trace for each attribution factor
            for column in attribution_columns:
                if column in temporal_data.columns:
                    fig.add_trace(go.Scatter(
                        x=temporal_data.index,
                        y=temporal_data[column],
                        mode='lines',
                        name=column,
                        line=dict(width=2)
                    ))
            
            fig.update_layout(
                title="Temporal Attribution Analysis",
                xaxis_title="Date",
                yaxis_title="Attribution Value",
                height=600,
                hovermode='x unified'
            )
            
            return {
                "chart_type": "temporal_attribution",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Temporal attribution chart creation failed: {e}")
            return {"error": str(e)}


class ModelInterpretabilityVisualizer:
    """Visualizations for model interpretability analysis."""
    
    def __init__(self):
        """Initialize model interpretability visualizer."""
        pass
    
    def create_attention_heatmap(
        self,
        attention_weights: np.ndarray,
        input_labels: List[str],
        layer_name: str = "Attention Layer"
    ) -> Dict[str, Any]:
        """Create attention weights heatmap."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for attention heatmap"}
        
        try:
            fig = go.Figure(data=go.Heatmap(
                z=attention_weights,
                x=input_labels,
                y=input_labels,
                colorscale='Blues',
                text=attention_weights,
                texttemplate="%{text:.3f}",
                textfont={"size": 8}
            ))
            
            fig.update_layout(
                title=f"Attention Weights - {layer_name}",
                xaxis_title="Input Positions",
                yaxis_title="Output Positions",
                height=600
            )
            
            return {
                "chart_type": "attention_heatmap",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Attention heatmap creation failed: {e}")
            return {"error": str(e)}
    
    def create_layer_analysis_chart(
        self,
        layer_statistics: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create layer-wise analysis chart."""
        
        if not PLOTLY_AVAILABLE:
            return {"error": "Plotly not available for layer analysis"}
        
        try:
            layers = [stat["layer"] for stat in layer_statistics]
            
            # Create subplots for different metrics
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=("Mean Activation", "Sparsity", "Max Activation", "Std Activation"),
                specs=[[{"secondary_y": False}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # Mean activation
            fig.add_trace(
                go.Scatter(x=layers, y=[stat["mean_activation"] for stat in layer_statistics],
                          mode='lines+markers', name='Mean Activation'),
                row=1, col=1
            )
            
            # Sparsity
            fig.add_trace(
                go.Scatter(x=layers, y=[stat["sparsity"] for stat in layer_statistics],
                          mode='lines+markers', name='Sparsity'),
                row=1, col=2
            )
            
            # Max activation
            fig.add_trace(
                go.Scatter(x=layers, y=[stat["max_activation"] for stat in layer_statistics],
                          mode='lines+markers', name='Max Activation'),
                row=2, col=1
            )
            
            # Std activation
            fig.add_trace(
                go.Scatter(x=layers, y=[stat["std_activation"] for stat in layer_statistics],
                          mode='lines+markers', name='Std Activation'),
                row=2, col=2
            )
            
            fig.update_layout(
                title="Layer-wise Analysis",
                height=800,
                showlegend=False
            )
            
            return {
                "chart_type": "layer_analysis",
                "chart_data": fig.to_dict(),
                "chart_html": fig.to_html(),
                "chart_json": fig.to_json()
            }
            
        except Exception as e:
            logger.error(f"Layer analysis chart creation failed: {e}")
            return {"error": str(e)}


class VisualizationEngine:
    """Main visualization engine coordinating all visualization components."""
    
    def __init__(self):
        """Initialize visualization engine."""
        self.shap_visualizer = SHAPVisualizer()
        self.importance_visualizer = FeatureImportanceVisualizer()
        self.attribution_visualizer = AttributionVisualizer()
        self.interpretability_visualizer = ModelInterpretabilityVisualizer()
    
    async def cleanup(self):
        """Cleanup visualization engine resources."""
        pass
    
    async def generate_explanation_dashboard(
        self,
        explanation_data: Dict[str, Any],
        dashboard_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive explanation dashboard."""
        
        try:
            config = dashboard_config or {}
            dashboard = {
                "dashboard_type": "explanation",
                "charts": [],
                "metadata": {
                    "generated_at": datetime.utcnow().isoformat(),
                    "chart_count": 0
                }
            }
            
            # SHAP visualizations
            if "shap_explanation" in explanation_data:
                shap_data = explanation_data["shap_explanation"]
                
                # Waterfall chart
                if all(key in shap_data for key in ["shap_values", "feature_names", "feature_values", "base_value"]):
                    waterfall_chart = self.shap_visualizer.create_shap_waterfall(
                        shap_data["shap_values"],
                        shap_data["feature_names"],
                        shap_data["feature_values"],
                        shap_data["base_value"],
                        shap_data.get("prediction", 0)
                    )
                    if "error" not in waterfall_chart:
                        dashboard["charts"].append(waterfall_chart)
            
            # Feature importance visualizations
            if "feature_importance" in explanation_data:
                importance_data = explanation_data["feature_importance"]
                
                if isinstance(importance_data, dict) and "results" in importance_data:
                    # Extract importance scores
                    feature_names = [result["feature_name"] for result in importance_data["results"]]
                    importance_scores = {
                        "permutation": {result["feature_name"]: result["importance_mean"] 
                                      for result in importance_data["results"]}
                    }
                    
                    comparison_chart = self.importance_visualizer.create_importance_comparison(
                        importance_scores, feature_names
                    )
                    if "error" not in comparison_chart:
                        dashboard["charts"].append(comparison_chart)
            
            # Attribution visualizations
            if "attribution_results" in explanation_data:
                attribution_data = explanation_data["attribution_results"]
                
                if isinstance(attribution_data, list) and len(attribution_data) > 0:
                    # Create attribution breakdown
                    attr_dict = {
                        result["factor_name"]: result["attribution_value"]
                        for result in attribution_data
                    }
                    total_return = sum(attr_dict.values())
                    
                    breakdown_chart = self.attribution_visualizer.create_attribution_breakdown(
                        attr_dict, total_return
                    )
                    if "error" not in breakdown_chart:
                        dashboard["charts"].append(breakdown_chart)
            
            dashboard["metadata"]["chart_count"] = len(dashboard["charts"])
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Dashboard generation failed: {e}")
            return {
                "error": str(e),
                "dashboard_type": "explanation",
                "charts": []
            }
    
    async def export_chart(
        self,
        chart_data: Dict[str, Any],
        export_format: str = "PNG",
        width: int = 800,
        height: int = 600
    ) -> Dict[str, Any]:
        """Export chart to specified format."""
        
        try:
            if not PLOTLY_AVAILABLE:
                return {"error": "Plotly not available for chart export"}
            
            if "chart_data" not in chart_data:
                return {"error": "No chart data provided"}
            
            # Recreate figure from data
            fig = go.Figure(chart_data["chart_data"])
            
            # Export based on format
            if export_format.upper() == "PNG":
                img_bytes = fig.to_image(format="png", width=width, height=height)
                img_base64 = base64.b64encode(img_bytes).decode()
                
                return {
                    "format": "PNG",
                    "data": img_base64,
                    "mime_type": "image/png"
                }
            
            elif export_format.upper() == "SVG":
                svg_str = fig.to_image(format="svg", width=width, height=height).decode()
                
                return {
                    "format": "SVG",
                    "data": svg_str,
                    "mime_type": "image/svg+xml"
                }
            
            elif export_format.upper() == "HTML":
                html_str = fig.to_html()
                
                return {
                    "format": "HTML",
                    "data": html_str,
                    "mime_type": "text/html"
                }
            
            elif export_format.upper() == "JSON":
                json_str = fig.to_json()
                
                return {
                    "format": "JSON",
                    "data": json_str,
                    "mime_type": "application/json"
                }
            
            else:
                return {"error": f"Unsupported export format: {export_format}"}
            
        except Exception as e:
            logger.error(f"Chart export failed: {e}")
            return {"error": str(e)}
