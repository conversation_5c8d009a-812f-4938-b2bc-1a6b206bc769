"""
Broker connectivity and management router.
"""

import logging
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


@router.get("/")
async def list_brokers(
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """List available brokers and their status."""
    try:
        if not hasattr(request.app.state, 'execution_gateway'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Execution gateway not available"
            )
        
        gateway = request.app.state.execution_gateway
        connections = await gateway.get_broker_connections()
        
        return {
            "brokers": connections,
            "total": len(connections)
        }
        
    except Exception as e:
        logger.error(f"Broker listing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list brokers"
        )


@router.get("/{broker_name}/status")
async def get_broker_status(
    broker_name: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get specific broker status."""
    # TODO: Implement specific broker status
    return {
        "message": "Broker status not yet implemented",
        "broker_name": broker_name
    }


@router.post("/{broker_name}/reconnect")
async def reconnect_broker(
    broker_name: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Reconnect to broker."""
    # TODO: Implement broker reconnection
    return {
        "message": "Broker reconnection not yet implemented",
        "broker_name": broker_name
    }
