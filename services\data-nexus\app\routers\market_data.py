"""
Market data router for OHLCV data operations.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.models.market_data import OHLCV, Instrument
from app.schemas.market_data import (
    OHLCV as OHLCVSchema,
    OHLCVCreate,
    OHLCVWithInstrument,
    MarketDataQuery,
    Timeframe,
    TechnicalIndicator
)
from app.integrations.alpha_vantage import AlphaVantageClient

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[OHLCVSchema])
async def get_market_data(
    instrument_id: Optional[uuid.UUID] = Query(None, description="Instrument ID"),
    symbol: Optional[str] = Query(None, description="Instrument symbol"),
    timeframe: Timeframe = Query(..., description="Timeframe"),
    start_time: Optional[datetime] = Query(None, description="Start time (inclusive)"),
    end_time: Optional[datetime] = Query(None, description="End time (inclusive)"),
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of records"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get market data (OHLCV) for specified criteria.

    Args:
        instrument_id: Instrument ID filter
        symbol: Instrument symbol filter
        timeframe: Data timeframe
        start_time: Start time filter
        end_time: End time filter
        limit: Maximum number of records
        db: Database session

    Returns:
        List[OHLCVSchema]: List of OHLCV data points
    """
    # Build query conditions
    conditions = [OHLCV.timeframe == timeframe]

    if instrument_id:
        conditions.append(OHLCV.instrument_id == instrument_id)
    elif symbol:
        # Join with instruments table to filter by symbol
        stmt = (
            select(OHLCV)
            .join(Instrument)
            .where(
                and_(
                    Instrument.symbol == symbol.upper(),
                    *conditions
                )
            )
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either instrument_id or symbol must be provided"
        )

    if start_time:
        conditions.append(OHLCV.time >= start_time)

    if end_time:
        conditions.append(OHLCV.time <= end_time)

    # If we didn't use symbol filtering above, build the basic query
    if not symbol:
        stmt = select(OHLCV).where(and_(*conditions))

    # Add ordering and limit
    stmt = stmt.order_by(desc(OHLCV.time)).limit(limit)

    result = await db.execute(stmt)
    ohlcv_data = result.scalars().all()

    return [OHLCVSchema.from_orm(data) for data in ohlcv_data]


@router.get("/latest", response_model=List[OHLCVWithInstrument])
async def get_latest_market_data(
    timeframe: Timeframe = Query(..., description="Timeframe"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of instruments"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get latest market data for all active instruments.

    Args:
        timeframe: Data timeframe
        limit: Maximum number of instruments
        db: Database session

    Returns:
        List[OHLCVWithInstrument]: Latest data with instrument info
    """
    # Get latest data for each instrument
    subquery = (
        select(
            OHLCV.instrument_id,
            func.max(OHLCV.time).label("latest_time")
        )
        .where(OHLCV.timeframe == timeframe)
        .group_by(OHLCV.instrument_id)
        .subquery()
    )

    stmt = (
        select(OHLCV)
        .join(subquery, and_(
            OHLCV.instrument_id == subquery.c.instrument_id,
            OHLCV.time == subquery.c.latest_time,
            OHLCV.timeframe == timeframe
        ))
        .options(selectinload(OHLCV.instrument))
        .limit(limit)
    )

    result = await db.execute(stmt)
    ohlcv_data = result.scalars().all()

    return [OHLCVWithInstrument.from_orm(data) for data in ohlcv_data]


@router.post("/", response_model=List[OHLCVSchema])
async def create_market_data(
    ohlcv_data: List[OHLCVCreate],
    db: AsyncSession = Depends(get_db)
):
    """
    Create new market data entries.

    Args:
        ohlcv_data: List of OHLCV data to create
        db: Database session

    Returns:
        List[OHLCVSchema]: Created OHLCV data
    """
    # Validate that all instruments exist
    instrument_ids = {data.instrument_id for data in ohlcv_data}
    stmt = select(Instrument.id).where(Instrument.id.in_(instrument_ids))
    result = await db.execute(stmt)
    existing_ids = {row[0] for row in result.fetchall()}

    missing_ids = instrument_ids - existing_ids
    if missing_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Instruments not found: {missing_ids}"
        )

    # Create OHLCV records
    db_ohlcv_data = []
    for data in ohlcv_data:
        db_ohlcv = OHLCV(**data.dict())
        db_ohlcv_data.append(db_ohlcv)
        db.add(db_ohlcv)

    try:
        await db.commit()
        for db_ohlcv in db_ohlcv_data:
            await db.refresh(db_ohlcv)

        logger.info(f"Created {len(db_ohlcv_data)} OHLCV records")
        return [OHLCVSchema.from_orm(data) for data in db_ohlcv_data]

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create OHLCV data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create market data"
        )


@router.post("/ingest/{symbol}")
async def ingest_market_data(
    symbol: str,
    timeframe: Timeframe = Query(..., description="Timeframe to ingest"),
    outputsize: str = Query("compact", regex="^(compact|full)$", description="Data size"),
    db: AsyncSession = Depends(get_db)
):
    """
    Ingest market data from Alpha Vantage for a specific FOREX pair.

    Args:
        symbol: FOREX symbol (e.g., 'EURUSD')
        timeframe: Data timeframe to ingest
        outputsize: 'compact' or 'full'
        db: Database session

    Returns:
        dict: Ingestion result
    """
    # Validate symbol format (should be 6 characters for FOREX)
    if len(symbol) != 6:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Symbol must be 6 characters (e.g., 'EURUSD')"
        )

    from_currency = symbol[:3].upper()
    to_currency = symbol[3:].upper()

    # Find the instrument
    stmt = select(Instrument).where(Instrument.symbol == symbol.upper())
    result = await db.execute(stmt)
    instrument = result.scalar_one_or_none()

    if not instrument:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Instrument {symbol} not found"
        )

    # Initialize Alpha Vantage client
    av_client = AlphaVantageClient()

    try:
        # Fetch data from Alpha Vantage
        ohlcv_data = await av_client.get_forex_data(
            from_symbol=from_currency,
            to_symbol=to_currency,
            timeframe=timeframe,
            outputsize=outputsize
        )

        if not ohlcv_data:
            return {
                "message": "No data received (possibly rate limited)",
                "records_ingested": 0
            }

        # Set instrument_id for all records
        for data in ohlcv_data:
            data.instrument_id = instrument.id

        # Store data in database (using upsert to handle duplicates)
        records_created = 0
        for data in ohlcv_data:
            # Check if record already exists
            stmt = select(OHLCV).where(
                and_(
                    OHLCV.instrument_id == data.instrument_id,
                    OHLCV.time == data.time,
                    OHLCV.timeframe == data.timeframe
                )
            )
            result = await db.execute(stmt)
            existing = result.scalar_one_or_none()

            if not existing:
                db_ohlcv = OHLCV(**data.dict())
                db.add(db_ohlcv)
                records_created += 1

        await db.commit()

        logger.info(f"Ingested {records_created} new records for {symbol} ({timeframe})")

        return {
            "message": f"Successfully ingested data for {symbol}",
            "records_ingested": records_created,
            "total_records_received": len(ohlcv_data)
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to ingest data for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to ingest data: {str(e)}"
        )
    finally:
        await av_client.close()
