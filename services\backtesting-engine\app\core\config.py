"""
Configuration settings for Backtesting Engine service.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, validator
from decimal import Decimal


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "AthenaTrader Backtesting Engine"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Database
    DATABASE_URL: str = "postgresql://athena_user:athena_password@localhost:5432/athena_trader"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # External Services
    DATA_NEXUS_URL: str = "http://localhost:8001"
    STRATEGY_GENESIS_URL: str = "http://localhost:8002"
    
    # Simulation Configuration
    # Market Friction Parameters
    DEFAULT_SPREAD_BPS: Decimal = Decimal("2.0")  # 2 basis points default spread
    DEFAULT_COMMISSION_BPS: Decimal = Decimal("1.0")  # 1 basis point commission
    DEFAULT_SLIPPAGE_BPS: Decimal = Decimal("0.5")  # 0.5 basis points slippage
    MARKET_IMPACT_FACTOR: Decimal = Decimal("0.1")  # Market impact scaling factor
    
    # Latency Simulation
    MIN_EXECUTION_LATENCY_MS: int = 10  # Minimum execution latency
    MAX_EXECUTION_LATENCY_MS: int = 100  # Maximum execution latency
    NETWORK_LATENCY_MS: int = 50  # Network latency
    
    # Position and Risk Limits
    MAX_POSITION_SIZE: Decimal = Decimal("0.2")  # 20% of portfolio
    MAX_LEVERAGE: Decimal = Decimal("10.0")  # Maximum leverage
    DEFAULT_STOP_LOSS_PCT: Decimal = Decimal("0.02")  # 2% stop loss
    DEFAULT_TAKE_PROFIT_PCT: Decimal = Decimal("0.04")  # 4% take profit
    
    # Performance Analytics
    RISK_FREE_RATE: Decimal = Decimal("0.02")  # 2% annual risk-free rate
    TRADING_DAYS_PER_YEAR: int = 252
    CONFIDENCE_LEVELS: List[float] = [0.95, 0.99]  # For VaR calculations
    
    # Benchmark Configuration
    DEFAULT_BENCHMARK: str = "SPY"  # Default benchmark for comparison
    BENCHMARK_SYMBOLS: List[str] = ["SPY", "QQQ", "IWM", "EFA", "EEM"]
    
    # Monte Carlo Configuration
    MC_SIMULATION_RUNS: int = 1000  # Number of Monte Carlo runs
    MC_CONFIDENCE_INTERVALS: List[float] = [0.05, 0.25, 0.5, 0.75, 0.95]
    BOOTSTRAP_SAMPLES: int = 1000  # Bootstrap sample size
    
    # Walk-Forward Analysis
    WF_TRAINING_WINDOW_DAYS: int = 252  # 1 year training window
    WF_TEST_WINDOW_DAYS: int = 63  # 3 months test window
    WF_STEP_SIZE_DAYS: int = 21  # 1 month step size
    
    # Stress Testing Scenarios
    STRESS_TEST_SCENARIOS: Dict[str, Dict[str, float]] = {
        "market_crash": {"return_shock": -0.20, "volatility_multiplier": 3.0},
        "flash_crash": {"return_shock": -0.10, "volatility_multiplier": 5.0},
        "high_volatility": {"return_shock": 0.0, "volatility_multiplier": 2.5},
        "trending_market": {"return_shock": 0.15, "volatility_multiplier": 0.8},
        "sideways_market": {"return_shock": 0.0, "volatility_multiplier": 0.5}
    }
    
    # Data Configuration
    MIN_BACKTEST_DAYS: int = 30  # Minimum backtest period
    MAX_BACKTEST_DAYS: int = 1825  # Maximum backtest period (5 years)
    DEFAULT_TIMEFRAME: str = "1h"  # Default data timeframe
    SUPPORTED_TIMEFRAMES: List[str] = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    
    # Execution Configuration
    MAX_CONCURRENT_BACKTESTS: int = 5  # Maximum concurrent backtest jobs
    BACKTEST_TIMEOUT_HOURS: int = 24  # Backtest timeout
    PROGRESS_UPDATE_INTERVAL: int = 100  # Progress update every N bars
    
    # Storage Configuration
    RESULTS_STORAGE_PATH: str = "/app/results"
    CACHE_RESULTS: bool = True
    CACHE_EXPIRY_HOURS: int = 24
    
    # Performance Optimization
    CHUNK_SIZE: int = 10000  # Data processing chunk size
    PARALLEL_PROCESSING: bool = True
    MAX_WORKERS: int = 4  # Maximum worker processes
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Validation and Quality Checks
    MIN_TRADE_COUNT: int = 10  # Minimum trades for valid backtest
    MAX_DRAWDOWN_THRESHOLD: Decimal = Decimal("0.50")  # 50% max drawdown warning
    MIN_SHARPE_RATIO: Decimal = Decimal("-2.0")  # Minimum acceptable Sharpe ratio
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("RESULTS_STORAGE_PATH", pre=True)
    def create_results_storage_path(cls, v):
        """Ensure results storage path exists."""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator("DEFAULT_SPREAD_BPS", "DEFAULT_COMMISSION_BPS", "DEFAULT_SLIPPAGE_BPS", pre=True)
    def validate_bps_values(cls, v):
        """Validate basis points values are reasonable."""
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v < 0 or v > 1000:  # 0 to 10% seems reasonable
            raise ValueError("Basis points values must be between 0 and 1000")
        return v
    
    @validator("MAX_POSITION_SIZE", pre=True)
    def validate_position_size(cls, v):
        """Validate position size is reasonable."""
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0 or v > 1:
            raise ValueError("Position size must be between 0 and 1")
        return v
    
    @validator("CONFIDENCE_LEVELS", pre=True)
    def validate_confidence_levels(cls, v):
        """Validate confidence levels are between 0 and 1."""
        if isinstance(v, list):
            for level in v:
                if not 0 < level < 1:
                    raise ValueError("Confidence levels must be between 0 and 1")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
