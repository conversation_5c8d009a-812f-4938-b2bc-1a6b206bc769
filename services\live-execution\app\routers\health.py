"""
Health check router for Live Execution Module.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import psutil
import os

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "AthenaTrader Live Execution Module",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "capabilities": [
            "multi_broker_execution",
            "fix_protocol_support",
            "real_time_risk_management",
            "strategy_deployment_pipeline",
            "execution_quality_analytics",
            "market_microstructure_analysis",
            "real_time_xai_integration",
            "automated_risk_controls",
            "canary_releases",
            "performance_monitoring",
            "regulatory_compliance",
            "multi_asset_support"
        ]
    }


@router.get("/detailed")
async def detailed_health_check(request: Request, db: AsyncSession = Depends(get_db)):
    """Detailed health check with system and dependency status."""
    health_status = {
        "status": "healthy",
        "service": "AthenaTrader Live Execution Module",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database connectivity check
    try:
        await db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Check for resource warnings
        warnings = []
        if cpu_percent > 90:
            warnings.append("High CPU usage")
        if memory.percent > 90:
            warnings.append("High memory usage")
        if disk.percent > 90:
            warnings.append("Low disk space")
        
        if warnings:
            health_status["checks"]["system"]["warnings"] = warnings
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "error",
            "message": f"Failed to get system metrics: {str(e)}"
        }
    
    # Execution engines check
    try:
        engines_status = {}
        
        # Check execution gateway
        if hasattr(request.app.state, 'execution_gateway'):
            gateway = request.app.state.execution_gateway
            broker_connections = await gateway.get_broker_connections()
            engines_status["execution_gateway"] = {
                "status": "healthy",
                "active_brokers": len(broker_connections),
                "brokers": [conn.broker_name for conn in broker_connections]
            }
        else:
            engines_status["execution_gateway"] = {
                "status": "not_initialized",
                "message": "Execution gateway not available"
            }
        
        # Check risk manager
        if hasattr(request.app.state, 'risk_manager'):
            risk_manager = request.app.state.risk_manager
            risk_summary = await risk_manager.get_risk_summary()
            engines_status["risk_manager"] = {
                "status": "healthy",
                "monitoring_active": risk_summary.get("monitoring_active", False),
                "active_alerts": risk_summary.get("active_alerts", 0)
            }
        else:
            engines_status["risk_manager"] = {
                "status": "not_initialized",
                "message": "Risk manager not available"
            }
        
        # Check strategy deployer
        if hasattr(request.app.state, 'strategy_deployer'):
            deployer = request.app.state.strategy_deployer
            active_deployments = await deployer.list_active_deployments()
            engines_status["strategy_deployer"] = {
                "status": "healthy",
                "active_deployments": len(active_deployments)
            }
        else:
            engines_status["strategy_deployer"] = {
                "status": "not_initialized",
                "message": "Strategy deployer not available"
            }
        
        health_status["checks"]["execution_engines"] = {
            "status": "healthy",
            "engines": engines_status
        }
        
    except Exception as e:
        health_status["checks"]["execution_engines"] = {
            "status": "error",
            "message": f"Execution engines check failed: {str(e)}"
        }
    
    # External services check
    try:
        import httpx
        external_services = {
            "data_nexus": settings.DATA_NEXUS_URL,
            "strategy_genesis": settings.STRATEGY_GENESIS_URL,
            "backtesting_engine": settings.BACKTESTING_ENGINE_URL,
            "portfolio_construction": settings.PORTFOLIO_CONSTRUCTION_URL,
            "xai_module": settings.XAI_MODULE_URL
        }
        
        service_status = {}
        for service_name, service_url in external_services.items():
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{service_url}/health")
                    if response.status_code == 200:
                        service_status[service_name] = "healthy"
                    else:
                        service_status[service_name] = "unhealthy"
                        health_status["status"] = "degraded"
            except Exception:
                service_status[service_name] = "unreachable"
                health_status["status"] = "degraded"
        
        health_status["checks"]["external_services"] = {
            "status": "healthy" if all(s == "healthy" for s in service_status.values()) else "degraded",
            "services": service_status
        }
        
    except Exception as e:
        health_status["checks"]["external_services"] = {
            "status": "error",
            "message": f"External services check failed: {str(e)}"
        }
    
    # Trading libraries check
    try:
        trading_libraries = {}
        
        # Check QuickFIX
        try:
            import quickfix
            trading_libraries["quickfix"] = {
                "available": True,
                "version": quickfix.QUICKFIX_VERSION
            }
        except ImportError:
            trading_libraries["quickfix"] = {
                "available": False,
                "message": "QuickFIX not installed"
            }
        
        # Check httpx for REST APIs
        try:
            import httpx
            trading_libraries["httpx"] = {
                "available": True,
                "version": httpx.__version__
            }
        except ImportError:
            trading_libraries["httpx"] = {
                "available": False,
                "message": "httpx not installed"
            }
        
        health_status["checks"]["trading_libraries"] = {
            "status": "healthy",
            "libraries": trading_libraries
        }
        
    except Exception as e:
        health_status["checks"]["trading_libraries"] = {
            "status": "error",
            "message": f"Trading libraries check failed: {str(e)}"
        }
    
    return health_status


@router.get("/brokers")
async def broker_status(request: Request):
    """Get broker connection status."""
    try:
        if not hasattr(request.app.state, 'execution_gateway'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Execution gateway not available"
            )
        
        gateway = request.app.state.execution_gateway
        broker_connections = await gateway.get_broker_connections()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_brokers": len(broker_connections),
            "brokers": [
                {
                    "name": conn.broker_name,
                    "status": conn.status,
                    "connection_type": conn.connection_type,
                    "supported_assets": conn.supported_asset_classes,
                    "avg_latency_ms": conn.avg_latency_ms,
                    "uptime_percentage": conn.uptime_percentage,
                    "last_heartbeat": conn.last_heartbeat.isoformat() if conn.last_heartbeat else None
                }
                for conn in broker_connections
            ]
        }
        
    except Exception as e:
        logger.error(f"Broker status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get broker status"
        )


@router.get("/risk")
async def risk_status(request: Request):
    """Get risk management status."""
    try:
        if not hasattr(request.app.state, 'risk_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Risk manager not available"
            )
        
        risk_manager = request.app.state.risk_manager
        risk_summary = await risk_manager.get_risk_summary()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "risk_summary": risk_summary
        }
        
    except Exception as e:
        logger.error(f"Risk status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get risk status"
        )


@router.get("/deployments")
async def deployment_status(request: Request):
    """Get strategy deployment status."""
    try:
        if not hasattr(request.app.state, 'strategy_deployer'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Strategy deployer not available"
            )
        
        deployer = request.app.state.strategy_deployer
        active_deployments = await deployer.list_active_deployments()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_deployments": len(active_deployments),
            "deployments": [
                {
                    "deployment_id": dep.deployment_id,
                    "strategy_id": str(dep.strategy_id),
                    "deployment_type": dep.deployment_type,
                    "status": dep.status,
                    "health_status": dep.health_status,
                    "traffic_percentage": float(dep.traffic_percentage),
                    "deployed_at": dep.deployed_at.isoformat() if dep.deployed_at else None
                }
                for dep in active_deployments
            ]
        }
        
    except Exception as e:
        logger.error(f"Deployment status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get deployment status"
        )


@router.get("/metrics")
async def get_metrics(request: Request, db: AsyncSession = Depends(get_db)):
    """Get service metrics and statistics."""
    try:
        from app.models.execution import LiveOrder, OrderExecution, LivePosition, RiskEvent
        from sqlalchemy import func, select
        from datetime import timedelta
        
        # Get order statistics
        order_counts = await db.execute(
            select(LiveOrder.status, func.count(LiveOrder.id))
            .group_by(LiveOrder.status)
        )
        
        # Get recent activity
        recent_orders = await db.execute(
            select(func.count(LiveOrder.id))
            .where(LiveOrder.created_at >= datetime.utcnow() - timedelta(hours=24))
        )
        
        # Get execution statistics
        execution_counts = await db.execute(
            select(func.count(OrderExecution.id))
            .where(OrderExecution.execution_time >= datetime.utcnow() - timedelta(hours=24))
        )
        
        # Get risk event statistics
        risk_event_counts = await db.execute(
            select(RiskEvent.severity, func.count(RiskEvent.id))
            .where(RiskEvent.detected_at >= datetime.utcnow() - timedelta(hours=24))
            .group_by(RiskEvent.severity)
        )
        
        # Get position statistics
        position_counts = await db.execute(
            select(LivePosition.status, func.count(LivePosition.id))
            .group_by(LivePosition.status)
        )
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "orders": {
                "by_status": dict(order_counts.fetchall()),
                "recent_24h": recent_orders.scalar() or 0
            },
            "executions": {
                "recent_24h": execution_counts.scalar() or 0
            },
            "risk_events": {
                "by_severity_24h": dict(risk_event_counts.fetchall())
            },
            "positions": {
                "by_status": dict(position_counts.fetchall())
            },
            "system": {
                "uptime_seconds": psutil.boot_time(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "supported_brokers": settings.SUPPORTED_BROKERS,
                "supported_assets": settings.SUPPORTED_ASSET_CLASSES
            },
            "configuration": {
                "max_position_size": float(settings.MAX_POSITION_SIZE),
                "max_daily_loss": float(settings.MAX_DAILY_LOSS),
                "max_leverage": float(settings.MAX_LEVERAGE),
                "circuit_breakers_enabled": settings.ENABLE_CIRCUIT_BREAKERS,
                "paper_trading_mode": settings.ENABLE_PAPER_TRADING
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )
