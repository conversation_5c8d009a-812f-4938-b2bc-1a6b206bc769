"""
Tests for Genetic Programming module.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from app.ai.genetic_programming import GeneticProgrammingEngine, TechnicalIndicators, TradingEnvironment
from app.schemas.strategy import GeneticProgrammingConfig, PerformanceMetrics


@pytest.fixture
def sample_market_data():
    """Create sample market data for testing."""
    dates = pd.date_range('2023-01-01', periods=1000, freq='H')
    np.random.seed(42)
    
    # Generate realistic OHLCV data
    close_prices = 100 + np.cumsum(np.random.randn(1000) * 0.01)
    
    data = pd.DataFrame({
        'time': dates,
        'open': close_prices + np.random.randn(1000) * 0.001,
        'high': close_prices + np.abs(np.random.randn(1000) * 0.002),
        'low': close_prices - np.abs(np.random.randn(1000) * 0.002),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 1000)
    })
    
    return data


@pytest.fixture
def gp_config():
    """Create GP configuration for testing."""
    return GeneticProgrammingConfig(
        population_size=10,  # Small for testing
        generations=5,       # Few generations for speed
        tournament_size=2,
        crossover_prob=0.8,
        mutation_prob=0.2,
        max_tree_depth=5
    )


class TestTechnicalIndicators:
    """Test technical indicators functionality."""
    
    def test_sma_calculation(self):
        """Test Simple Moving Average calculation."""
        data = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        sma = TechnicalIndicators.sma(data, period=3)
        
        # Check that SMA is calculated correctly (ignoring NaN values)
        assert not np.isnan(sma[-1])  # Last value should not be NaN
        assert sma[-1] == pytest.approx(9.0, rel=1e-2)  # SMA of [8,9,10] = 9
    
    def test_ema_calculation(self):
        """Test Exponential Moving Average calculation."""
        data = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        ema = TechnicalIndicators.ema(data, period=3)
        
        # Check that EMA is calculated
        assert not np.isnan(ema[-1])  # Last value should not be NaN
        assert ema[-1] > 0  # Should be positive
    
    def test_rsi_calculation(self):
        """Test RSI calculation."""
        data = np.array([44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89])
        rsi = TechnicalIndicators.rsi(data, period=5)
        
        # Check that RSI is in valid range
        valid_rsi = rsi[~np.isnan(rsi)]
        if len(valid_rsi) > 0:
            assert all(0 <= val <= 100 for val in valid_rsi)


class TestTradingEnvironment:
    """Test trading environment functionality."""
    
    def test_environment_initialization(self, sample_market_data):
        """Test trading environment initialization."""
        env = TradingEnvironment(sample_market_data)
        
        assert env.initial_capital == 10000.0
        assert env.current_capital == 10000.0
        assert env.position == 0.0
        assert len(env.indicators) > 0
    
    def test_indicator_precomputation(self, sample_market_data):
        """Test that indicators are precomputed correctly."""
        env = TradingEnvironment(sample_market_data)
        
        # Check that indicators exist
        expected_indicators = ['sma_10', 'sma_20', 'sma_50', 'ema_10', 'ema_20', 'rsi']
        for indicator in expected_indicators:
            assert indicator in env.indicators
            assert len(env.indicators[indicator]) == len(sample_market_data)
    
    def test_get_indicator_value(self, sample_market_data):
        """Test getting indicator values."""
        env = TradingEnvironment(sample_market_data)
        
        # Test valid index
        value = env.get_indicator_value('sma_20', 100)
        assert isinstance(value, float)
        
        # Test invalid indicator
        value = env.get_indicator_value('invalid_indicator', 100)
        assert value == 0.0
        
        # Test out of bounds index
        value = env.get_indicator_value('sma_20', 10000)
        assert value == 0.0
    
    def test_execute_trade(self, sample_market_data):
        """Test trade execution."""
        env = TradingEnvironment(sample_market_data)
        initial_capital = env.current_capital
        
        # Execute a buy signal
        env.execute_trade(0.5, 100, 100.0)
        
        # Check that position changed
        assert env.position != 0.0
        assert len(env.trades) > 0
        
        # Check that capital decreased due to transaction costs
        assert env.current_capital < initial_capital
    
    def test_performance_calculation(self, sample_market_data):
        """Test performance metrics calculation."""
        env = TradingEnvironment(sample_market_data)
        
        # Simulate some trades
        for i in range(50, 100):
            signal = 0.1 if i % 10 == 0 else 0.0
            price = sample_market_data.iloc[i]['close']
            env.execute_trade(signal, i, price)
            env.equity_curve.append(env.current_capital)
        
        performance = env.calculate_performance()
        
        assert isinstance(performance, PerformanceMetrics)
        assert performance.total_return is not None
        assert performance.sharpe_ratio is not None
        assert performance.max_drawdown is not None


class TestGeneticProgrammingEngine:
    """Test Genetic Programming engine functionality."""
    
    def test_engine_initialization(self, gp_config):
        """Test GP engine initialization."""
        engine = GeneticProgrammingEngine(gp_config)
        
        assert engine.config == gp_config
        assert engine.toolbox is not None
        assert engine.population is None
        assert engine.best_individual is None
    
    def test_protected_division(self, gp_config):
        """Test protected division function."""
        engine = GeneticProgrammingEngine(gp_config)
        
        # Normal division
        result = engine._protected_div(10.0, 2.0)
        assert result == 5.0
        
        # Division by zero
        result = engine._protected_div(10.0, 0.0)
        assert result == 1.0
        
        # Division by very small number
        result = engine._protected_div(10.0, 1e-7)
        assert result == 1.0
    
    def test_if_then_else(self, gp_config):
        """Test conditional operator."""
        engine = GeneticProgrammingEngine(gp_config)
        
        result = engine._if_then_else(True, 10.0, 20.0)
        assert result == 10.0
        
        result = engine._if_then_else(False, 10.0, 20.0)
        assert result == 20.0
    
    @patch('app.ai.genetic_programming.logger')
    def test_evolve_strategy_basic(self, mock_logger, gp_config, sample_market_data):
        """Test basic strategy evolution."""
        engine = GeneticProgrammingEngine(gp_config)
        
        # Mock progress callback
        progress_callback = Mock()
        
        # Run evolution (should complete without errors)
        result = engine.evolve_strategy(sample_market_data, progress_callback)
        
        # Check result structure
        assert isinstance(result, dict)
        assert 'best_individual' in result
        assert 'best_fitness' in result
        assert 'performance_metrics' in result
        assert 'evolution_stats' in result
        assert 'population_size' in result
        assert 'generations' in result
        
        # Check that progress callback was called
        assert progress_callback.call_count > 0
    
    def test_fitness_calculation(self, gp_config):
        """Test fitness calculation."""
        engine = GeneticProgrammingEngine(gp_config)
        
        # Create mock performance metrics
        performance = PerformanceMetrics(
            total_return=0.1,
            sharpe_ratio=1.5,
            max_drawdown=0.05,
            win_rate=0.6
        )
        
        fitness = engine._calculate_fitness(performance)
        
        assert isinstance(fitness, float)
        # Fitness should be positive for good performance
        assert fitness > 0
    
    def test_predict_signal(self, gp_config, sample_market_data):
        """Test signal prediction."""
        engine = GeneticProgrammingEngine(gp_config)
        
        # Create a simple strategy expression
        strategy_expression = "add(get_close(ARG0), 1.0)"
        
        # Test signal generation
        signal = engine.predict_signal(sample_market_data.head(100), strategy_expression)
        
        assert isinstance(signal, float)
        assert -1.0 <= signal <= 1.0


@pytest.mark.integration
class TestGeneticProgrammingIntegration:
    """Integration tests for GP module."""
    
    def test_full_evolution_cycle(self, sample_market_data):
        """Test complete evolution cycle with realistic data."""
        config = GeneticProgrammingConfig(
            population_size=20,
            generations=10,
            tournament_size=3,
            crossover_prob=0.8,
            mutation_prob=0.2,
            max_tree_depth=6
        )
        
        engine = GeneticProgrammingEngine(config)
        
        # Track progress
        progress_calls = []
        def progress_callback(gen, total_gen, stats):
            progress_calls.append((gen, total_gen, stats))
        
        # Run evolution
        result = engine.evolve_strategy(sample_market_data, progress_callback)
        
        # Verify results
        assert result['population_size'] == 20
        assert result['generations'] == 10
        assert len(progress_calls) == 11  # 0 to 10 generations
        
        # Check that fitness improved over generations
        if len(progress_calls) > 1:
            first_fitness = progress_calls[0][2].get('max', 0)
            last_fitness = progress_calls[-1][2].get('max', 0)
            # Fitness should generally improve (though not guaranteed)
            assert isinstance(first_fitness, (int, float))
            assert isinstance(last_fitness, (int, float))
    
    def test_strategy_save_load(self, gp_config, sample_market_data, tmp_path):
        """Test strategy saving and loading."""
        engine = GeneticProgrammingEngine(gp_config)
        
        # Evolve a strategy
        result = engine.evolve_strategy(sample_market_data)
        
        # Save strategy
        filepath = tmp_path / "test_strategy.pkl"
        engine.save_strategy(result, str(filepath))
        
        assert filepath.exists()
        
        # Load strategy
        new_engine = GeneticProgrammingEngine()
        loaded_result = new_engine.load_strategy(str(filepath))
        
        assert loaded_result['best_individual'] == result['best_individual']
        assert loaded_result['best_fitness'] == result['best_fitness']
