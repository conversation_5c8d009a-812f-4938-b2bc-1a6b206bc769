"""
Tests for Strategy Genesis API endpoints.
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import Test<PERSON>lient
from fastapi import FastAPI

from app.routers import health, strategies
from app.core.database import get_db


@pytest.fixture
def test_app(override_get_db):
    """Create test FastAPI application."""
    app = FastAPI()
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
    
    # Override database dependency
    app.dependency_overrides[get_db] = override_get_db
    
    return app


@pytest.fixture
def client(test_app):
    """Create test client."""
    return TestClient(test_app)


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_basic_health_check(self, client):
        """Test basic health check endpoint."""
        response = client.get("/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["service"] == "AthenaTrader Strategy Genesis"
        assert "timestamp" in data
    
    @patch('app.routers.health.psutil')
    def test_detailed_health_check(self, mock_psutil, client):
        """Test detailed health check endpoint."""
        # Mock system metrics
        mock_psutil.cpu_percent.return_value = 25.0
        mock_psutil.virtual_memory.return_value.percent = 60.0
        mock_psutil.virtual_memory.return_value.available = 8 * 1024**3  # 8GB
        mock_psutil.disk_usage.return_value.percent = 45.0
        mock_psutil.disk_usage.return_value.free = 100 * 1024**3  # 100GB
        
        response = client.get("/health/detailed")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] in ["healthy", "degraded"]  # May be degraded due to external services
        assert "checks" in data
        assert "database" in data["checks"]
        assert "system" in data["checks"]
    
    @patch('app.ai.genetic_programming.GeneticProgrammingEngine')
    @patch('app.ai.reinforcement_learning.ReinforcementLearningEngine')
    @patch('app.ai.deep_learning.DeepLearningEngine')
    def test_ai_engines_status(self, mock_dl, mock_rl, mock_gp, client):
        """Test AI engines status endpoint."""
        response = client.get("/health/ai-engines")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "engines" in data
        assert "genetic_programming" in data["engines"]
        assert "reinforcement_learning" in data["engines"]
        assert "deep_learning" in data["engines"]


class TestStrategyEndpoints:
    """Test strategy management endpoints."""
    
    def test_list_strategies_unauthorized(self, client):
        """Test listing strategies without authentication."""
        response = client.get("/strategies/")
        
        # Should fail without authentication
        assert response.status_code == 401
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_list_strategies_empty(self, mock_get_user, client, mock_user_id):
        """Test listing strategies with no strategies."""
        mock_get_user.return_value = mock_user_id
        
        response = client.get("/strategies/")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_create_strategy(self, mock_get_user, client, mock_user_id, sample_strategy_data):
        """Test creating a new strategy."""
        mock_get_user.return_value = mock_user_id
        
        response = client.post("/strategies/", json=sample_strategy_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == sample_strategy_data["name"]
        assert data["ai_paradigm"] == sample_strategy_data["ai_paradigm"]
        assert data["status"] == "CREATED"
        assert data["is_active"] is True
        assert "id" in data
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_get_strategy_not_found(self, mock_get_user, client, mock_user_id):
        """Test getting a non-existent strategy."""
        mock_get_user.return_value = mock_user_id
        
        import uuid
        fake_id = str(uuid.uuid4())
        response = client.get(f"/strategies/{fake_id}")
        
        assert response.status_code == 404
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_update_strategy(self, mock_get_user, client, mock_user_id, test_strategy):
        """Test updating an existing strategy."""
        mock_get_user.return_value = mock_user_id
        
        update_data = {
            "name": "Updated Strategy Name",
            "description": "Updated description"
        }
        
        response = client.put(f"/strategies/{test_strategy.id}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_delete_strategy(self, mock_get_user, client, mock_user_id, test_strategy):
        """Test deleting a strategy (soft delete)."""
        mock_get_user.return_value = mock_user_id
        
        response = client.delete(f"/strategies/{test_strategy.id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "message" in data
        assert "deleted" in data["message"].lower()


class TestGeneticProgrammingEndpoints:
    """Test Genetic Programming specific endpoints."""
    
    @patch('app.routers.genetic_programming.get_current_user_id')
    @patch('app.routers.genetic_programming.fetch_market_data')
    @patch('app.routers.genetic_programming.run_gp_training')
    def test_train_gp_strategy(self, mock_training, mock_fetch_data, mock_get_user, 
                              client, mock_user_id, test_strategy, sample_training_config,
                              sample_market_data):
        """Test starting GP training for a strategy."""
        mock_get_user.return_value = mock_user_id
        mock_fetch_data.return_value = sample_market_data
        
        # Update test strategy to be GP paradigm
        test_strategy.ai_paradigm = "GP"
        
        response = client.post(
            f"/genetic-programming/train/{test_strategy.id}",
            json=sample_training_config
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "training_run_id" in data
        assert data["status"] == "PENDING"
        assert "message" in data
    
    @patch('app.routers.genetic_programming.get_current_user_id')
    def test_train_non_gp_strategy(self, mock_get_user, client, mock_user_id, 
                                  test_strategy, sample_training_config):
        """Test training a non-GP strategy should fail."""
        mock_get_user.return_value = mock_user_id
        
        # Ensure strategy is not GP
        test_strategy.ai_paradigm = "RL"
        
        response = client.post(
            f"/genetic-programming/train/{test_strategy.id}",
            json=sample_training_config
        )
        
        assert response.status_code == 400
        assert "not a Genetic Programming strategy" in response.json()["detail"]
    
    @patch('app.routers.genetic_programming.get_current_user_id')
    @patch('app.ai.genetic_programming.GeneticProgrammingEngine')
    def test_predict_gp_signal(self, mock_gp_engine, mock_get_user, client, 
                              mock_user_id, test_strategy):
        """Test generating trading signal with GP strategy."""
        mock_get_user.return_value = mock_user_id
        
        # Setup strategy as trained
        test_strategy.ai_paradigm = "GP"
        test_strategy.status = "TRAINED"
        test_strategy.model_path = "/fake/path/model.pkl"
        
        # Mock GP engine
        mock_engine_instance = mock_gp_engine.return_value
        mock_engine_instance.load_strategy.return_value = {
            "best_individual": "add(get_close(ARG0), 1.0)"
        }
        mock_engine_instance.predict_signal.return_value = 0.3
        
        market_data = {
            "open": 100.0,
            "high": 100.5,
            "low": 99.5,
            "close": 100.2,
            "volume": 5000
        }
        
        response = client.post(
            f"/genetic-programming/predict/{test_strategy.id}",
            json=market_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "signal" in data
        assert "strategy_id" in data
        assert "timestamp" in data
        assert "confidence" in data
        assert "metadata" in data
        
        assert -1.0 <= data["signal"] <= 1.0
        assert data["strategy_id"] == str(test_strategy.id)
    
    @patch('app.routers.genetic_programming.get_current_user_id')
    def test_predict_untrained_strategy(self, mock_get_user, client, mock_user_id, test_strategy):
        """Test predicting with untrained strategy should fail."""
        mock_get_user.return_value = mock_user_id
        
        test_strategy.ai_paradigm = "GP"
        test_strategy.status = "CREATED"  # Not trained
        
        market_data = {
            "open": 100.0,
            "high": 100.5,
            "low": 99.5,
            "close": 100.2,
            "volume": 5000
        }
        
        response = client.post(
            f"/genetic-programming/predict/{test_strategy.id}",
            json=market_data
        )
        
        assert response.status_code == 400
        assert "not trained" in response.json()["detail"]


@pytest.mark.integration
class TestAPIIntegration:
    """Integration tests for API endpoints."""
    
    @patch('app.routers.strategies.get_current_user_id')
    def test_strategy_lifecycle(self, mock_get_user, client, mock_user_id, sample_strategy_data):
        """Test complete strategy lifecycle: create, read, update, delete."""
        mock_get_user.return_value = mock_user_id
        
        # Create strategy
        response = client.post("/strategies/", json=sample_strategy_data)
        assert response.status_code == 200
        strategy = response.json()
        strategy_id = strategy["id"]
        
        # Read strategy
        response = client.get(f"/strategies/{strategy_id}")
        assert response.status_code == 200
        retrieved_strategy = response.json()
        assert retrieved_strategy["id"] == strategy_id
        
        # Update strategy
        update_data = {"name": "Updated Strategy"}
        response = client.put(f"/strategies/{strategy_id}", json=update_data)
        assert response.status_code == 200
        updated_strategy = response.json()
        assert updated_strategy["name"] == "Updated Strategy"
        
        # List strategies
        response = client.get("/strategies/")
        assert response.status_code == 200
        strategies = response.json()
        assert len(strategies) == 1
        assert strategies[0]["id"] == strategy_id
        
        # Delete strategy
        response = client.delete(f"/strategies/{strategy_id}")
        assert response.status_code == 200
