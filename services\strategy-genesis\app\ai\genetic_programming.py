"""
Genetic Programming module for evolving trading strategies using DEAP.
"""

import logging
import random
import operator
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional, Callable
from datetime import datetime, timedelta
import pickle
import os

from deap import algorithms, base, creator, tools, gp
import talib

from app.core.config import settings
from app.schemas.strategy import GeneticProgrammingConfig, PerformanceMetrics

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Technical indicators for use as GP primitives."""

    @staticmethod
    def sma(data: np.ndarray, period: int = 20) -> np.ndarray:
        """Simple Moving Average."""
        return talib.SMA(data, timeperiod=min(period, len(data) - 1))

    @staticmethod
    def ema(data: np.ndarray, period: int = 20) -> np.ndarray:
        """Exponential Moving Average."""
        return talib.EMA(data, timeperiod=min(period, len(data) - 1))

    @staticmethod
    def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """Relative Strength Index."""
        return talib.RSI(data, timeperiod=min(period, len(data) - 1))

    @staticmethod
    def macd(data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """MACD indicator."""
        return talib.MACD(data)

    @staticmethod
    def bollinger_bands(data: np.ndarray, period: int = 20) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Bollinger Bands."""
        return talib.BBANDS(data, timeperiod=min(period, len(data) - 1))

    @staticmethod
    def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Stochastic oscillator."""
        return talib.STOCH(high, low, close)

    @staticmethod
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average True Range."""
        return talib.ATR(high, low, close, timeperiod=min(period, len(high) - 1))


class TradingEnvironment:
    """Trading environment for strategy evaluation."""

    def __init__(self, data: pd.DataFrame, initial_capital: float = 10000.0):
        """
        Initialize trading environment.

        Args:
            data: OHLCV data with columns ['open', 'high', 'low', 'close', 'volume']
            initial_capital: Initial capital for trading
        """
        self.data = data
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position = 0.0  # Current position size
        self.trades = []
        self.equity_curve = []

        # Precompute technical indicators
        self._precompute_indicators()

    def _precompute_indicators(self):
        """Precompute technical indicators for efficiency."""
        close = self.data['close'].values
        high = self.data['high'].values
        low = self.data['low'].values

        self.indicators = {
            'sma_10': TechnicalIndicators.sma(close, 10),
            'sma_20': TechnicalIndicators.sma(close, 20),
            'sma_50': TechnicalIndicators.sma(close, 50),
            'ema_10': TechnicalIndicators.ema(close, 10),
            'ema_20': TechnicalIndicators.ema(close, 20),
            'rsi': TechnicalIndicators.rsi(close),
            'atr': TechnicalIndicators.atr(high, low, close),
        }

        # MACD
        macd, macd_signal, macd_hist = TechnicalIndicators.macd(close)
        self.indicators.update({
            'macd': macd,
            'macd_signal': macd_signal,
            'macd_hist': macd_hist,
        })

        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(close)
        self.indicators.update({
            'bb_upper': bb_upper,
            'bb_middle': bb_middle,
            'bb_lower': bb_lower,
        })

        # Stochastic
        stoch_k, stoch_d = TechnicalIndicators.stochastic(high, low, close)
        self.indicators.update({
            'stoch_k': stoch_k,
            'stoch_d': stoch_d,
        })

    def get_indicator_value(self, indicator_name: str, index: int) -> float:
        """Get indicator value at specific index."""
        if indicator_name not in self.indicators:
            return 0.0

        values = self.indicators[indicator_name]
        if index >= len(values) or np.isnan(values[index]):
            return 0.0

        return float(values[index])

    def get_price_data(self, index: int) -> Dict[str, float]:
        """Get OHLCV data at specific index."""
        if index >= len(self.data):
            return {'open': 0, 'high': 0, 'low': 0, 'close': 0, 'volume': 0}

        row = self.data.iloc[index]
        return {
            'open': float(row['open']),
            'high': float(row['high']),
            'low': float(row['low']),
            'close': float(row['close']),
            'volume': float(row['volume']),
        }

    def execute_trade(self, signal: float, index: int, price: float):
        """Execute a trade based on signal."""
        # Normalize signal to [-1, 1] range
        signal = max(-1.0, min(1.0, signal))

        # Calculate position size (percentage of capital)
        max_position_size = settings.MAX_POSITION_SIZE
        target_position = signal * max_position_size

        # Calculate trade size
        position_change = target_position - self.position

        if abs(position_change) > 0.01:  # Minimum trade threshold
            # Calculate trade cost (simplified)
            trade_cost = abs(position_change) * self.current_capital * 0.001  # 0.1% transaction cost

            # Update position and capital
            self.position = target_position
            self.current_capital -= trade_cost

            # Record trade
            self.trades.append({
                'index': index,
                'price': price,
                'position_change': position_change,
                'new_position': self.position,
                'cost': trade_cost,
                'capital': self.current_capital
            })

    def calculate_performance(self) -> PerformanceMetrics:
        """Calculate strategy performance metrics."""
        if len(self.equity_curve) < 2:
            return PerformanceMetrics()

        returns = np.diff(self.equity_curve) / self.equity_curve[:-1]

        # Basic metrics
        total_return = (self.equity_curve[-1] - self.equity_curve[0]) / self.equity_curve[0]
        volatility = np.std(returns) * np.sqrt(252)  # Annualized

        # Sharpe ratio (assuming 0% risk-free rate)
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0

        # Maximum drawdown
        peak = np.maximum.accumulate(self.equity_curve)
        drawdown = (self.equity_curve - peak) / peak
        max_drawdown = np.min(drawdown)

        # Win rate
        winning_trades = sum(1 for trade in self.trades if trade['position_change'] > 0)
        win_rate = winning_trades / len(self.trades) if self.trades else 0

        return PerformanceMetrics(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=abs(max_drawdown),
            win_rate=win_rate,
            volatility=volatility
        )


class GeneticProgrammingEngine:
    """Genetic Programming engine for evolving trading strategies."""

    def __init__(self, config: Optional[GeneticProgrammingConfig] = None):
        """Initialize GP engine."""
        self.config = config or GeneticProgrammingConfig()
        self.toolbox = None
        self.population = None
        self.stats = None
        self.logbook = None
        self.best_individual = None

        self._setup_gp()

    def _setup_gp(self):
        """Set up DEAP genetic programming framework."""
        # Create primitive set
        pset = gp.PrimitiveSet("MAIN", 1)  # 1 input (current index)

        # Add arithmetic operators
        pset.addPrimitive(operator.add, 2)
        pset.addPrimitive(operator.sub, 2)
        pset.addPrimitive(operator.mul, 2)
        pset.addPrimitive(self._protected_div, 2)

        # Add comparison operators
        pset.addPrimitive(operator.lt, 2)
        pset.addPrimitive(operator.gt, 2)

        # Add conditional operator
        pset.addPrimitive(self._if_then_else, 3)

        # Add technical indicator functions
        pset.addPrimitive(self._get_sma_10, 1)
        pset.addPrimitive(self._get_sma_20, 1)
        pset.addPrimitive(self._get_sma_50, 1)
        pset.addPrimitive(self._get_ema_10, 1)
        pset.addPrimitive(self._get_ema_20, 1)
        pset.addPrimitive(self._get_rsi, 1)
        pset.addPrimitive(self._get_macd, 1)
        pset.addPrimitive(self._get_close, 1)
        pset.addPrimitive(self._get_volume, 1)

        # Add constants
        pset.addEphemeralConstant("rand", lambda: random.uniform(-1, 1))

        # Rename arguments
        pset.renameArguments(ARG0='index')

        # Create fitness and individual classes
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        creator.create("Individual", gp.PrimitiveTree, fitness=creator.FitnessMax)

        # Create toolbox
        self.toolbox = base.Toolbox()
        self.toolbox.register("expr", gp.genHalfAndHalf, pset=pset, min_=1, max_=self.config.max_tree_depth)
        self.toolbox.register("individual", tools.initIterate, creator.Individual, self.toolbox.expr)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("compile", gp.compile, pset=pset)
        self.toolbox.register("evaluate", self._evaluate_individual)
        self.toolbox.register("select", tools.selTournament, tournsize=self.config.tournament_size)
        self.toolbox.register("mate", gp.cxOnePoint)
        self.toolbox.register("expr_mut", gp.genFull, min_=0, max_=2)
        self.toolbox.register("mutate", gp.mutUniform, expr=self.toolbox.expr_mut, pset=pset)

        # Set up statistics
        self.stats = tools.Statistics(lambda ind: ind.fitness.values)
        self.stats.register("avg", np.mean)
        self.stats.register("std", np.std)
        self.stats.register("min", np.min)
        self.stats.register("max", np.max)

        self.pset = pset

    def _protected_div(self, left: float, right: float) -> float:
        """Protected division to avoid division by zero."""
        try:
            return left / right if abs(right) > 1e-6 else 1.0
        except (ZeroDivisionError, OverflowError):
            return 1.0

    def _if_then_else(self, condition: bool, if_true: float, if_false: float) -> float:
        """Conditional operator."""
        return if_true if condition else if_false

    def _get_sma_10(self, index: int) -> float:
        """Get SMA(10) value."""
        return self.trading_env.get_indicator_value('sma_10', int(index))

    def _get_sma_20(self, index: int) -> float:
        """Get SMA(20) value."""
        return self.trading_env.get_indicator_value('sma_20', int(index))

    def _get_sma_50(self, index: int) -> float:
        """Get SMA(50) value."""
        return self.trading_env.get_indicator_value('sma_50', int(index))

    def _get_ema_10(self, index: int) -> float:
        """Get EMA(10) value."""
        return self.trading_env.get_indicator_value('ema_10', int(index))

    def _get_ema_20(self, index: int) -> float:
        """Get EMA(20) value."""
        return self.trading_env.get_indicator_value('ema_20', int(index))

    def _get_rsi(self, index: int) -> float:
        """Get RSI value."""
        return self.trading_env.get_indicator_value('rsi', int(index))

    def _get_macd(self, index: int) -> float:
        """Get MACD value."""
        return self.trading_env.get_indicator_value('macd', int(index))

    def _get_close(self, index: int) -> float:
        """Get close price."""
        price_data = self.trading_env.get_price_data(int(index))
        return price_data['close']

    def _get_volume(self, index: int) -> float:
        """Get volume."""
        price_data = self.trading_env.get_price_data(int(index))
        return price_data['volume']

    def _evaluate_individual(self, individual) -> Tuple[float]:
        """Evaluate an individual's fitness."""
        try:
            # Compile the individual into a callable function
            func = self.toolbox.compile(expr=individual)

            # Reset trading environment
            self.trading_env.current_capital = self.trading_env.initial_capital
            self.trading_env.position = 0.0
            self.trading_env.trades = []
            self.trading_env.equity_curve = []

            # Simulate trading
            for i in range(50, len(self.trading_env.data)):  # Start after warm-up period
                try:
                    # Get trading signal from GP individual
                    signal = func(i)

                    # Ensure signal is numeric
                    if not isinstance(signal, (int, float)) or np.isnan(signal) or np.isinf(signal):
                        signal = 0.0

                    # Get current price
                    price_data = self.trading_env.get_price_data(i)
                    current_price = price_data['close']

                    # Execute trade
                    self.trading_env.execute_trade(signal, i, current_price)

                    # Calculate current equity
                    position_value = self.trading_env.position * self.trading_env.current_capital * current_price
                    total_equity = self.trading_env.current_capital + position_value
                    self.trading_env.equity_curve.append(total_equity)

                except Exception as e:
                    # Handle individual evaluation errors
                    logger.warning(f"Error evaluating individual at index {i}: {e}")
                    continue

            # Calculate performance metrics
            performance = self.trading_env.calculate_performance()

            # Calculate fitness (composite score)
            fitness = self._calculate_fitness(performance)

            return (fitness,)

        except Exception as e:
            logger.error(f"Error evaluating individual: {e}")
            return (0.0,)

    def _calculate_fitness(self, performance: PerformanceMetrics) -> float:
        """Calculate fitness score from performance metrics."""
        # Composite fitness function
        fitness = 0.0

        # Total return component (40%)
        if performance.total_return is not None:
            fitness += 0.4 * float(performance.total_return)

        # Sharpe ratio component (30%)
        if performance.sharpe_ratio is not None:
            fitness += 0.3 * float(performance.sharpe_ratio) / 3.0  # Normalize

        # Max drawdown penalty (20%)
        if performance.max_drawdown is not None:
            fitness -= 0.2 * float(performance.max_drawdown)

        # Win rate component (10%)
        if performance.win_rate is not None:
            fitness += 0.1 * float(performance.win_rate)

        return fitness

    def evolve_strategy(self, training_data: pd.DataFrame,
                       progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Evolve a trading strategy using genetic programming.

        Args:
            training_data: Historical market data for training
            progress_callback: Optional callback for progress updates

        Returns:
            Dict containing the best strategy and evolution statistics
        """
        logger.info(f"Starting GP evolution with {self.config.population_size} individuals for {self.config.generations} generations")

        # Set up trading environment
        self.trading_env = TradingEnvironment(training_data)

        # Create initial population
        self.population = self.toolbox.population(n=self.config.population_size)

        # Initialize logbook
        self.logbook = tools.Logbook()
        self.logbook.header = ['gen', 'nevals'] + self.stats.fields

        # Evaluate initial population
        fitnesses = list(map(self.toolbox.evaluate, self.population))
        for ind, fit in zip(self.population, fitnesses):
            ind.fitness.values = fit

        # Record initial statistics
        record = self.stats.compile(self.population)
        self.logbook.record(gen=0, nevals=len(self.population), **record)

        if progress_callback:
            progress_callback(0, self.config.generations, record)

        # Evolution loop
        for generation in range(1, self.config.generations + 1):
            logger.info(f"Generation {generation}/{self.config.generations}")

            # Select parents
            offspring = self.toolbox.select(self.population, len(self.population))
            offspring = list(map(self.toolbox.clone, offspring))

            # Apply crossover and mutation
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < self.config.crossover_prob:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values

            for mutant in offspring:
                if random.random() < self.config.mutation_prob:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values

            # Evaluate invalid individuals
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = list(map(self.toolbox.evaluate, invalid_ind))
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit

            # Elitism: keep best individuals
            if hasattr(self.config, 'elitism_size') and self.config.elitism_size > 0:
                elite = tools.selBest(self.population, self.config.elitism_size)
                offspring.extend(elite)

            # Replace population
            self.population[:] = offspring

            # Record statistics
            record = self.stats.compile(self.population)
            self.logbook.record(gen=generation, nevals=len(invalid_ind), **record)

            if progress_callback:
                progress_callback(generation, self.config.generations, record)

            logger.info(f"Generation {generation}: Best fitness = {record['max']:.6f}, Avg fitness = {record['avg']:.6f}")

        # Get best individual
        self.best_individual = tools.selBest(self.population, 1)[0]

        # Evaluate best individual one more time for detailed metrics
        self.trading_env.current_capital = self.trading_env.initial_capital
        self.trading_env.position = 0.0
        self.trading_env.trades = []
        self.trading_env.equity_curve = []

        func = self.toolbox.compile(expr=self.best_individual)
        for i in range(50, len(training_data)):
            try:
                signal = func(i)
                if not isinstance(signal, (int, float)) or np.isnan(signal) or np.isinf(signal):
                    signal = 0.0

                price_data = self.trading_env.get_price_data(i)
                current_price = price_data['close']
                self.trading_env.execute_trade(signal, i, current_price)

                position_value = self.trading_env.position * self.trading_env.current_capital * current_price
                total_equity = self.trading_env.current_capital + position_value
                self.trading_env.equity_curve.append(total_equity)
            except:
                continue

        final_performance = self.trading_env.calculate_performance()

        return {
            'best_individual': str(self.best_individual),
            'best_fitness': float(self.best_individual.fitness.values[0]),
            'performance_metrics': final_performance.model_dump(),
            'evolution_stats': self.logbook,
            'population_size': self.config.population_size,
            'generations': self.config.generations,
            'final_equity': self.trading_env.equity_curve[-1] if self.trading_env.equity_curve else self.trading_env.initial_capital
        }

    def save_strategy(self, strategy_data: Dict[str, Any], filepath: str):
        """Save evolved strategy to file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'wb') as f:
            pickle.dump({
                'strategy_data': strategy_data,
                'config': self.config,
                'pset': self.pset
            }, f)
        logger.info(f"Strategy saved to {filepath}")

    def load_strategy(self, filepath: str) -> Dict[str, Any]:
        """Load evolved strategy from file."""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        self.config = data['config']
        self.pset = data['pset']
        self._setup_gp()

        logger.info(f"Strategy loaded from {filepath}")
        return data['strategy_data']

    def predict_signal(self, current_data: pd.DataFrame, strategy_expression: str) -> float:
        """Generate trading signal using evolved strategy."""
        try:
            # Set up trading environment with current data
            self.trading_env = TradingEnvironment(current_data)

            # Compile strategy expression
            individual = gp.PrimitiveTree.from_string(strategy_expression, self.pset)
            func = self.toolbox.compile(expr=individual)

            # Generate signal for the latest data point
            latest_index = len(current_data) - 1
            signal = func(latest_index)

            # Ensure signal is valid
            if not isinstance(signal, (int, float)) or np.isnan(signal) or np.isinf(signal):
                signal = 0.0

            # Normalize signal to [-1, 1] range
            signal = max(-1.0, min(1.0, signal))

            return signal

        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return 0.0
