import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
} from '@mui/material';

const MarketDataPage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Market Data
      </Typography>
      
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Real-time and historical market data for FOREX and other instruments.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Live Market Data
              </Typography>
              <Paper sx={{ p: 3, textAlign: 'center', minHeight: 400 }}>
                <Typography color="text.secondary">
                  Market data visualization and real-time feeds will be implemented here.
                  This will include:
                </Typography>
                <Box sx={{ mt: 2, textAlign: 'left' }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    • Real-time FOREX price feeds
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    • Historical OHLCV data charts
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    • Technical indicators
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    • Data quality monitoring
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    • Alpha Vantage integration status
                  </Typography>
                </Box>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MarketDataPage;
