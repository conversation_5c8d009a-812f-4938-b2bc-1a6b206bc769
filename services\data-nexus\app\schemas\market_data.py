"""
Pydantic schemas for market data operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from enum import Enum

from pydantic import BaseModel, Field, validator


class InstrumentType(str, Enum):
    """Instrument type enumeration."""
    FOREX = "FOREX"
    STOCK = "STOCK"
    CRYPTO = "CRYPTO"
    COMMODITY = "COMMODITY"
    INDEX = "INDEX"
    BOND = "BOND"
    OPTION = "OPTION"
    FUTURE = "FUTURE"


class Timeframe(str, Enum):
    """Timeframe enumeration."""
    TICK = "tick"
    ONE_MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    THIRTY_MINUTES = "30m"
    ONE_HOUR = "1h"
    FOUR_HOURS = "4h"
    ONE_DAY = "1d"
    ONE_WEEK = "1w"
    ONE_MONTH = "1M"


class InstrumentBase(BaseModel):
    """Base instrument schema."""
    symbol: str = Field(..., min_length=1, max_length=20, description="Instrument symbol")
    name: str = Field(..., min_length=1, max_length=255, description="Instrument name")
    instrument_type: InstrumentType = Field(..., description="Type of instrument")
    base_currency: Optional[str] = Field(None, max_length=10, description="Base currency (for FOREX)")
    quote_currency: Optional[str] = Field(None, max_length=10, description="Quote currency (for FOREX)")
    exchange: Optional[str] = Field(None, max_length=100, description="Exchange or market")


class InstrumentCreate(InstrumentBase):
    """Schema for creating a new instrument."""
    pass


class InstrumentUpdate(BaseModel):
    """Schema for updating instrument information."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    is_active: Optional[bool] = None


class Instrument(InstrumentBase):
    """Public instrument schema."""
    id: uuid.UUID
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class OHLCVBase(BaseModel):
    """Base OHLCV schema."""
    time: datetime = Field(..., description="Timestamp")
    timeframe: Timeframe = Field(..., description="Timeframe")
    open: Decimal = Field(..., gt=0, description="Opening price")
    high: Decimal = Field(..., gt=0, description="Highest price")
    low: Decimal = Field(..., gt=0, description="Lowest price")
    close: Decimal = Field(..., gt=0, description="Closing price")
    volume: Decimal = Field(default=Decimal("0"), ge=0, description="Volume")
    
    @validator("high")
    def validate_high(cls, v, values):
        """Validate that high >= max(open, close)."""
        if "open" in values and "close" in values:
            max_price = max(values["open"], values["close"])
            if v < max_price:
                raise ValueError("High price must be >= max(open, close)")
        return v
    
    @validator("low")
    def validate_low(cls, v, values):
        """Validate that low <= min(open, close)."""
        if "open" in values and "close" in values:
            min_price = min(values["open"], values["close"])
            if v > min_price:
                raise ValueError("Low price must be <= min(open, close)")
        return v


class OHLCVCreate(OHLCVBase):
    """Schema for creating OHLCV data."""
    instrument_id: uuid.UUID = Field(..., description="Instrument ID")


class OHLCV(OHLCVBase):
    """Public OHLCV schema."""
    instrument_id: uuid.UUID
    
    class Config:
        from_attributes = True


class OHLCVWithInstrument(OHLCV):
    """OHLCV schema with instrument information."""
    instrument: Instrument


class MarketDataQuery(BaseModel):
    """Schema for market data queries."""
    instrument_ids: Optional[List[uuid.UUID]] = Field(None, description="List of instrument IDs")
    symbols: Optional[List[str]] = Field(None, description="List of instrument symbols")
    timeframe: Timeframe = Field(..., description="Timeframe")
    start_time: Optional[datetime] = Field(None, description="Start time (inclusive)")
    end_time: Optional[datetime] = Field(None, description="End time (inclusive)")
    limit: Optional[int] = Field(1000, ge=1, le=10000, description="Maximum number of records")
    
    @validator("end_time")
    def validate_end_time(cls, v, values):
        """Validate that end_time > start_time."""
        if v and "start_time" in values and values["start_time"]:
            if v <= values["start_time"]:
                raise ValueError("End time must be after start time")
        return v


class DataSourceBase(BaseModel):
    """Base data source schema."""
    name: str = Field(..., min_length=1, max_length=100, description="Data source name")
    source_type: str = Field(..., min_length=1, max_length=50, description="Source type")
    endpoint_url: Optional[str] = Field(None, max_length=500, description="API endpoint URL")
    api_key_required: bool = Field(default=False, description="Whether API key is required")
    rate_limit_per_minute: Optional[Decimal] = Field(None, ge=0, description="Rate limit per minute")
    priority: Decimal = Field(default=Decimal("1.0"), ge=0, le=10, description="Priority (higher = more important)")


class DataSourceCreate(DataSourceBase):
    """Schema for creating a data source."""
    pass


class DataSource(DataSourceBase):
    """Public data source schema."""
    id: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TechnicalIndicator(BaseModel):
    """Schema for technical indicator data."""
    name: str = Field(..., description="Indicator name")
    value: Decimal = Field(..., description="Indicator value")
    parameters: Optional[dict] = Field(None, description="Indicator parameters")
    timestamp: datetime = Field(..., description="Calculation timestamp")


class MarketDataSummary(BaseModel):
    """Schema for market data summary."""
    instrument: Instrument
    latest_data: Optional[OHLCV] = None
    data_count: int = Field(..., description="Number of data points available")
    first_timestamp: Optional[datetime] = None
    last_timestamp: Optional[datetime] = None
    timeframes_available: List[Timeframe] = Field(default_factory=list)
