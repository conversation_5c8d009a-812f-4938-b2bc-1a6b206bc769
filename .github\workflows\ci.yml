name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: timescale/timescaledb:latest-pg15
        env:
          POSTGRES_DB: athena_trader_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        python-version: [3.11]
        service: [
          'api-gateway',
          'data-nexus',
          'strategy-genesis',
          'backtesting-engine'
        ]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with flake8
      run: |
        flake8 services/${{ matrix.service }} --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 services/${{ matrix.service }} --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Format check with black
      run: |
        black --check services/${{ matrix.service }}
    
    - name: Import sort check with isort
      run: |
        isort --check-only services/${{ matrix.service }}
    
    - name: Type check with mypy
      run: |
        mypy services/${{ matrix.service }}
    
    - name: Test with pytest
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/athena_trader_test
        REDIS_URL: redis://localhost:6379
        JWT_SECRET_KEY: test-secret-key
      run: |
        pytest services/${{ matrix.service }}/tests/ -v --cov=services/${{ matrix.service }} --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: ${{ matrix.service }}
        name: codecov-${{ matrix.service }}

  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Lint
      run: |
        cd frontend
        npm run lint
    
    - name: Type check
      run: |
        cd frontend
        npm run type-check
    
    - name: Test
      run: |
        cd frontend
        npm test -- --coverage --watchAll=false
    
    - name: Build
      run: |
        cd frontend
        npm run build

  docker-build:
    runs-on: ubuntu-latest
    needs: [test, frontend-test]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build and test Docker images
      run: |
        docker-compose build
        docker-compose up -d
        sleep 30
        docker-compose ps
        docker-compose down
