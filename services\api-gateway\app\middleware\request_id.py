"""
Request ID middleware for request tracing.
"""

import uuid
import logging
from typing import <PERSON><PERSON>

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """Middleware for adding request IDs to requests and responses."""
    
    async def dispatch(self, request: Request, call_next):
        """Process request through request ID middleware."""
        
        # Get or generate request ID
        request_id = self._get_or_generate_request_id(request)
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Add request ID to logging context
        logger.info(f"Processing request {request_id}: {request.method} {request.url.path}")
        
        # Continue with the request
        response = await call_next(request)
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response
    
    def _get_or_generate_request_id(self, request: Request) -> str:
        """Get request ID from headers or generate a new one."""
        # Check if request ID is provided in headers
        request_id = request.headers.get("X-Request-ID")
        
        if request_id:
            # Validate the provided request ID
            try:
                uuid.UUID(request_id)
                return request_id
            except ValueError:
                # Invalid UUID format, generate a new one
                pass
        
        # Generate new request ID
        return str(uuid.uuid4())
