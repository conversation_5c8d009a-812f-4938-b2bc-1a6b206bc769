"""
Test configuration and fixtures for Strategy Genesis service.
"""

import pytest
import asyncio
from typing import As<PERSON><PERSON><PERSON>ator
import uuid
from unittest.mock import AsyncMock, Mock

import pandas as pd
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import <PERSON><PERSON><PERSON>ool

from app.core.database import Base, get_db
from app.models.strategy import Strategy, StrategyVersion, StrategyTrainingRun, AIParadigm


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    # Use in-memory SQLite for testing
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False,
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async_session = async_sessionmaker(engine, expire_on_commit=False)
    
    async with async_session() as session:
        yield session
    
    await engine.dispose()


@pytest.fixture
def mock_user_id():
    """Mock user ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_strategy_data(mock_user_id):
    """Sample strategy data for testing."""
    return {
        "user_id": mock_user_id,
        "name": "Test GP Strategy",
        "description": "A test genetic programming strategy",
        "ai_paradigm": "GP",
        "paradigm_config": {
            "population_size": 50,
            "generations": 25,
            "tournament_size": 3
        },
        "parameters": {
            "crossover_prob": 0.8,
            "mutation_prob": 0.2,
            "max_tree_depth": 8
        }
    }


@pytest.fixture
async def test_strategy(test_db: AsyncSession, sample_strategy_data):
    """Create a test strategy in the database."""
    strategy = Strategy(**sample_strategy_data)
    test_db.add(strategy)
    await test_db.commit()
    await test_db.refresh(strategy)
    return strategy


@pytest.fixture
def sample_market_data():
    """Generate sample market data for testing."""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=1000, freq='H')
    
    # Generate realistic price data with trend and volatility
    base_price = 100.0
    returns = np.random.normal(0.0001, 0.01, 1000)  # Small positive drift with volatility
    prices = base_price * np.exp(np.cumsum(returns))
    
    # Generate OHLCV data
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        volatility = np.random.uniform(0.001, 0.005)
        high = close * (1 + volatility)
        low = close * (1 - volatility)
        open_price = prices[i-1] if i > 0 else close
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'time': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    return pd.DataFrame(data)


@pytest.fixture
def mock_data_nexus_response():
    """Mock response from Data Nexus service."""
    return [
        {
            "time": "2023-01-01T00:00:00Z",
            "open": 100.0,
            "high": 100.5,
            "low": 99.5,
            "close": 100.2,
            "volume": 5000
        },
        {
            "time": "2023-01-01T01:00:00Z",
            "open": 100.2,
            "high": 100.8,
            "low": 100.0,
            "close": 100.6,
            "volume": 5500
        }
    ]


@pytest.fixture
def mock_httpx_client(mock_data_nexus_response):
    """Mock httpx client for external API calls."""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = mock_data_nexus_response
    mock_response.raise_for_status.return_value = None
    
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_response
    
    return mock_client


@pytest.fixture
def sample_training_config():
    """Sample training configuration for testing."""
    return {
        "training_config": {
            "paradigm": "GP",
            "data_source": "alpha_vantage",
            "validation_split": 0.2
        },
        "hyperparameters": {
            "population_size": 20,
            "generations": 10,
            "tournament_size": 3,
            "crossover_prob": 0.8,
            "mutation_prob": 0.2,
            "max_tree_depth": 6
        },
        "data_start_date": "2023-01-01T00:00:00Z",
        "data_end_date": "2023-12-31T23:59:59Z",
        "instruments_used": [str(uuid.uuid4())],
        "total_epochs": 10,
        "total_generations": 10
    }


@pytest.fixture
async def test_ai_paradigm(test_db: AsyncSession):
    """Create test AI paradigm in database."""
    paradigm = AIParadigm(
        name="GP",
        display_name="Genetic Programming",
        description="Test GP paradigm",
        default_config={
            "population_size": 100,
            "generations": 50
        },
        supported_parameters=[
            "population_size",
            "generations",
            "tournament_size"
        ],
        supports_online_learning=False,
        supports_multi_asset=True,
        supports_multi_timeframe=True,
        min_memory_mb=512,
        recommended_memory_mb=2048,
        requires_gpu=False
    )
    
    test_db.add(paradigm)
    await test_db.commit()
    await test_db.refresh(paradigm)
    return paradigm


@pytest.fixture
def mock_gp_engine():
    """Mock Genetic Programming engine for testing."""
    mock_engine = Mock()
    mock_engine.evolve_strategy.return_value = {
        "best_individual": "add(get_close(ARG0), 1.0)",
        "best_fitness": 0.85,
        "performance_metrics": {
            "total_return": 0.15,
            "sharpe_ratio": 1.2,
            "max_drawdown": 0.08,
            "win_rate": 0.65
        },
        "evolution_stats": [],
        "population_size": 20,
        "generations": 10,
        "final_equity": 11500.0
    }
    mock_engine.save_strategy.return_value = None
    mock_engine.load_strategy.return_value = mock_engine.evolve_strategy.return_value
    mock_engine.predict_signal.return_value = 0.3
    
    return mock_engine


@pytest.fixture
def mock_request_with_user(mock_user_id):
    """Mock FastAPI request with user state."""
    mock_request = Mock()
    mock_request.state.user_id = mock_user_id
    return mock_request


# Override the database dependency for testing
@pytest.fixture
def override_get_db(test_db):
    """Override the get_db dependency for testing."""
    async def _override_get_db():
        yield test_db
    
    return _override_get_db


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )
