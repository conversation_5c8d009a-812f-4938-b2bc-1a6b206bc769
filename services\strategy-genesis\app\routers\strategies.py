"""
Strategies router for strategy management operations.
"""

import logging
from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.models.strategy import Strategy, StrategyVersion, StrategyTrainingRun
from app.schemas.strategy import (
    Strategy as StrategySchema,
    StrategyCreate,
    StrategyUpdate,
    StrategyVersion as StrategyVersionSchema,
    TrainingRun as TrainingRunSchema,
    AIParadigm,
    StrategyStatus
)

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


@router.get("/", response_model=List[StrategySchema])
async def list_strategies(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    ai_paradigm: Optional[AIParadigm] = Query(None, description="Filter by AI paradigm"),
    status_filter: Optional[StrategyStatus] = Query(None, description="Filter by status"),
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    List strategies for the current user.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records
        ai_paradigm: Filter by AI paradigm
        status_filter: Filter by strategy status
        user_id: Current user ID
        db: Database session
        
    Returns:
        List[StrategySchema]: List of strategies
    """
    # Build query conditions
    conditions = [Strategy.user_id == user_id]
    
    if ai_paradigm:
        conditions.append(Strategy.ai_paradigm == ai_paradigm)
    
    if status_filter:
        conditions.append(Strategy.status == status_filter)
    
    # Build and execute query
    stmt = (
        select(Strategy)
        .where(and_(*conditions))
        .order_by(desc(Strategy.created_at))
        .offset(skip)
        .limit(limit)
    )
    
    result = await db.execute(stmt)
    strategies = result.scalars().all()
    
    return [StrategySchema.from_orm(strategy) for strategy in strategies]


@router.get("/{strategy_id}", response_model=StrategySchema)
async def get_strategy(
    strategy_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific strategy by ID.
    
    Args:
        strategy_id: Strategy ID
        user_id: Current user ID
        db: Database session
        
    Returns:
        StrategySchema: Strategy information
        
    Raises:
        HTTPException: If strategy not found or access denied
    """
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    return StrategySchema.from_orm(strategy)


@router.post("/", response_model=StrategySchema)
async def create_strategy(
    strategy_data: StrategyCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new strategy.
    
    Args:
        strategy_data: Strategy creation data
        db: Database session
        
    Returns:
        StrategySchema: Created strategy
    """
    # Create new strategy
    strategy = Strategy(
        user_id=strategy_data.user_id,
        name=strategy_data.name,
        description=strategy_data.description,
        ai_paradigm=strategy_data.ai_paradigm,
        paradigm_config=strategy_data.paradigm_config,
        parameters=strategy_data.parameters,
        status="CREATED",
        is_active=True,
        version=1
    )
    
    db.add(strategy)
    
    try:
        await db.commit()
        await db.refresh(strategy)
        
        logger.info(f"Created new strategy: {strategy.name} ({strategy.ai_paradigm})")
        return StrategySchema.from_orm(strategy)
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create strategy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create strategy"
        )


@router.put("/{strategy_id}", response_model=StrategySchema)
async def update_strategy(
    strategy_id: uuid.UUID,
    strategy_update: StrategyUpdate,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Update an existing strategy.
    
    Args:
        strategy_id: Strategy ID
        strategy_update: Update data
        user_id: Current user ID
        db: Database session
        
    Returns:
        StrategySchema: Updated strategy
        
    Raises:
        HTTPException: If strategy not found or access denied
    """
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    # Update fields
    update_data = strategy_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(strategy, field, value)
    
    try:
        await db.commit()
        await db.refresh(strategy)
        
        logger.info(f"Updated strategy: {strategy.name}")
        return StrategySchema.from_orm(strategy)
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to update strategy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update strategy"
        )


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a strategy (soft delete by setting is_active=False).
    
    Args:
        strategy_id: Strategy ID
        user_id: Current user ID
        db: Database session
        
    Returns:
        dict: Deletion confirmation
        
    Raises:
        HTTPException: If strategy not found or access denied
    """
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    # Soft delete
    strategy.is_active = False
    strategy.status = "ARCHIVED"
    
    try:
        await db.commit()
        
        logger.info(f"Deleted strategy: {strategy.name}")
        return {"message": "Strategy deleted successfully"}
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to delete strategy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete strategy"
        )


@router.get("/{strategy_id}/versions", response_model=List[StrategyVersionSchema])
async def get_strategy_versions(
    strategy_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all versions of a strategy.
    
    Args:
        strategy_id: Strategy ID
        user_id: Current user ID
        db: Database session
        
    Returns:
        List[StrategyVersionSchema]: List of strategy versions
        
    Raises:
        HTTPException: If strategy not found or access denied
    """
    # Verify strategy ownership
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    # Get versions
    stmt = (
        select(StrategyVersion)
        .where(StrategyVersion.strategy_id == strategy_id)
        .order_by(desc(StrategyVersion.version_number))
    )
    result = await db.execute(stmt)
    versions = result.scalars().all()
    
    return [StrategyVersionSchema.from_orm(version) for version in versions]


@router.get("/{strategy_id}/training-runs", response_model=List[TrainingRunSchema])
async def get_strategy_training_runs(
    strategy_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get training runs for a strategy.
    
    Args:
        strategy_id: Strategy ID
        user_id: Current user ID
        db: Database session
        
    Returns:
        List[TrainingRunSchema]: List of training runs
        
    Raises:
        HTTPException: If strategy not found or access denied
    """
    # Verify strategy ownership
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    # Get training runs
    stmt = (
        select(StrategyTrainingRun)
        .where(StrategyTrainingRun.strategy_id == strategy_id)
        .order_by(desc(StrategyTrainingRun.created_at))
    )
    result = await db.execute(stmt)
    training_runs = result.scalars().all()
    
    return [TrainingRunSchema.from_orm(run) for run in training_runs]
