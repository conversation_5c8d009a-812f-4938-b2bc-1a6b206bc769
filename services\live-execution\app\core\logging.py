"""
Logging configuration for Live Execution Module.
"""

import logging
import logging.config
import sys
from pathlib import Path

from app.core.config import settings


def setup_logging():
    """Setup logging configuration."""
    
    # Create logs directory
    logs_dir = Path(settings.EXECUTION_LOGS_PATH)
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Logging configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(module)s %(funcName)s %(lineno)d %(message)s"
            } if settings.ENABLE_STRUCTURED_LOGGING else {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "default",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "detailed",
                "filename": str(logs_dir / "live_execution.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "execution_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": str(logs_dir / "executions.log"),
                "maxBytes": 52428800,  # 50MB
                "backupCount": 10
            },
            "risk_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "WARNING",
                "formatter": "json",
                "filename": str(logs_dir / "risk_events.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10
            },
            "audit_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": str(logs_dir / "audit_trail.log"),
                "maxBytes": 52428800,  # 50MB
                "backupCount": 20
            }
        },
        "loggers": {
            "": {  # Root logger
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file"]
            },
            "app.engine.execution_gateway": {
                "level": "INFO",
                "handlers": ["execution_file"],
                "propagate": False
            },
            "app.engine.risk_manager": {
                "level": "WARNING",
                "handlers": ["risk_file"],
                "propagate": False
            },
            "app.engine.order_manager": {
                "level": "INFO",
                "handlers": ["execution_file"],
                "propagate": False
            },
            "audit": {
                "level": "INFO",
                "handlers": ["audit_file"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            }
        }
    }
    
    logging.config.dictConfig(config)
    
    # Set third-party loggers to WARNING to reduce noise
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)


def get_execution_logger():
    """Get execution-specific logger."""
    return logging.getLogger("app.engine.execution_gateway")


def get_risk_logger():
    """Get risk-specific logger."""
    return logging.getLogger("app.engine.risk_manager")


def get_audit_logger():
    """Get audit-specific logger."""
    return logging.getLogger("audit")
