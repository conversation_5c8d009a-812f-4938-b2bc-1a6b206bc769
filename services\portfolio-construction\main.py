"""
AthenaTrader Portfolio Construction Service

Sophisticated multi-strategy portfolio optimization service that provides
institutional-grade portfolio management capabilities with dynamic rebalancing,
risk management, and performance attribution for AI-generated trading strategies.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import health, portfolios, optimization, rebalancing, analytics

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader Portfolio Construction Service...")
    await init_db()
    logger.info("Database initialized")
    
    # Initialize portfolio construction components
    from app.engine.optimization import PortfolioOptimizer
    from app.engine.rebalancing import RebalancingEngine
    from app.engine.risk_management import RiskManager
    from app.engine.attribution import PerformanceAttributor
    
    # Store engines in app state
    app.state.portfolio_optimizer = PortfolioOptimizer()
    app.state.rebalancing_engine = RebalancingEngine()
    app.state.risk_manager = RiskManager()
    app.state.performance_attributor = PerformanceAttributor()
    
    logger.info("Portfolio construction engines initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader Portfolio Construction Service...")
    
    # Cleanup engines
    if hasattr(app.state, 'portfolio_optimizer'):
        await app.state.portfolio_optimizer.cleanup()
    if hasattr(app.state, 'rebalancing_engine'):
        await app.state.rebalancing_engine.cleanup()
    if hasattr(app.state, 'risk_manager'):
        await app.state.risk_manager.cleanup()
    if hasattr(app.state, 'performance_attributor'):
        await app.state.performance_attributor.cleanup()


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader Portfolio Construction",
    description="Sophisticated multi-strategy portfolio optimization service for AI-generated trading strategies",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(portfolios.router, prefix="/portfolios", tags=["portfolios"])
app.include_router(optimization.router, prefix="/optimization", tags=["optimization"])
app.include_router(rebalancing.router, prefix="/rebalancing", tags=["rebalancing"])
app.include_router(analytics.router, prefix="/analytics", tags=["analytics"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader Portfolio Construction Service",
        "version": "0.1.0",
        "status": "operational",
        "capabilities": [
            "multi_strategy_optimization",
            "mean_variance_optimization",
            "risk_parity_allocation",
            "black_litterman_model",
            "dynamic_rebalancing",
            "risk_management",
            "performance_attribution"
        ]
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
