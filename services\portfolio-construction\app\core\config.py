"""
Configuration settings for Portfolio Construction service.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, validator
from decimal import Decimal


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "AthenaTrader Portfolio Construction"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Database
    DATABASE_URL: str = "postgresql://athena_user:athena_password@localhost:5432/athena_trader"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # External Services
    DATA_NEXUS_URL: str = "http://localhost:8001"
    STRATEGY_GENESIS_URL: str = "http://localhost:8002"
    BACKTESTING_ENGINE_URL: str = "http://localhost:8003"
    
    # Portfolio Optimization Configuration
    # Risk-Free Rate and Market Parameters
    RISK_FREE_RATE: Decimal = Decimal("0.02")  # 2% annual risk-free rate
    MARKET_RETURN: Decimal = Decimal("0.08")  # 8% expected market return
    TRADING_DAYS_PER_YEAR: int = 252
    
    # Optimization Constraints
    MIN_WEIGHT: Decimal = Decimal("0.0")  # Minimum strategy weight (0% - no short selling by default)
    MAX_WEIGHT: Decimal = Decimal("0.5")  # Maximum strategy weight (50%)
    MAX_CONCENTRATION: Decimal = Decimal("0.3")  # Maximum concentration in single strategy type
    MIN_DIVERSIFICATION: int = 3  # Minimum number of strategies in portfolio
    MAX_STRATEGIES: int = 20  # Maximum number of strategies in portfolio
    
    # Risk Management Parameters
    MAX_PORTFOLIO_VOLATILITY: Decimal = Decimal("0.15")  # 15% maximum portfolio volatility
    MAX_DRAWDOWN_LIMIT: Decimal = Decimal("0.10")  # 10% maximum drawdown before rebalancing
    MIN_SHARPE_RATIO: Decimal = Decimal("0.5")  # Minimum acceptable Sharpe ratio
    MAX_CORRELATION_THRESHOLD: Decimal = Decimal("0.8")  # Maximum correlation between strategies
    
    # Rebalancing Configuration
    REBALANCING_FREQUENCY: str = "WEEKLY"  # DAILY, WEEKLY, MONTHLY, QUARTERLY
    DRIFT_THRESHOLD: Decimal = Decimal("0.05")  # 5% drift threshold for rebalancing
    VOLATILITY_THRESHOLD: Decimal = Decimal("0.02")  # 2% volatility change threshold
    MIN_REBALANCING_INTERVAL_HOURS: int = 24  # Minimum time between rebalancing
    TRANSACTION_COST_BPS: Decimal = Decimal("5.0")  # 5 bps transaction cost
    
    # Optimization Methods Configuration
    DEFAULT_OPTIMIZATION_METHOD: str = "MEAN_VARIANCE"  # MEAN_VARIANCE, RISK_PARITY, BLACK_LITTERMAN, MIN_VARIANCE, MAX_SHARPE
    ENABLE_BLACK_LITTERMAN: bool = True
    ENABLE_RISK_PARITY: bool = True
    ENABLE_HIERARCHICAL_CLUSTERING: bool = True
    
    # Black-Litterman Parameters
    BL_TAU: Decimal = Decimal("0.025")  # Scaling factor for uncertainty of prior
    BL_CONFIDENCE_LEVEL: Decimal = Decimal("0.95")  # Confidence level for views
    BL_VIEW_UNCERTAINTY: Decimal = Decimal("0.1")  # Default view uncertainty
    
    # Risk Parity Parameters
    RP_RISK_BUDGET_TOLERANCE: Decimal = Decimal("0.01")  # 1% tolerance for risk budget
    RP_MAX_ITERATIONS: int = 1000  # Maximum iterations for risk parity optimization
    RP_CONVERGENCE_TOLERANCE: Decimal = Decimal("1e-6")  # Convergence tolerance
    
    # Performance Attribution
    ATTRIBUTION_LOOKBACK_DAYS: int = 252  # 1 year lookback for attribution
    BENCHMARK_STRATEGY_ID: Optional[str] = None  # Benchmark strategy for comparison
    ENABLE_FACTOR_ATTRIBUTION: bool = True
    FACTOR_MODELS: List[str] = ["FAMA_FRENCH_3", "CAPM", "CUSTOM"]
    
    # Constraints and Limits
    SECTOR_LIMITS: Dict[str, Decimal] = {
        "EQUITY": Decimal("0.6"),
        "FIXED_INCOME": Decimal("0.4"),
        "ALTERNATIVES": Decimal("0.2"),
        "CASH": Decimal("0.1")
    }
    
    STRATEGY_TYPE_LIMITS: Dict[str, Decimal] = {
        "GP": Decimal("0.4"),  # Genetic Programming strategies
        "RL": Decimal("0.4"),  # Reinforcement Learning strategies
        "DL": Decimal("0.4"),  # Deep Learning strategies
        "HYBRID": Decimal("0.3")  # Hybrid strategies
    }
    
    GEOGRAPHIC_LIMITS: Dict[str, Decimal] = {
        "NORTH_AMERICA": Decimal("0.5"),
        "EUROPE": Decimal("0.4"),
        "ASIA_PACIFIC": Decimal("0.3"),
        "EMERGING_MARKETS": Decimal("0.2")
    }
    
    # Optimization Solver Configuration
    SOLVER: str = "ECOS"  # ECOS, SCS, OSQP, CVXOPT
    SOLVER_VERBOSE: bool = False
    SOLVER_MAX_ITERS: int = 10000
    SOLVER_TOLERANCE: Decimal = Decimal("1e-6")
    
    # Performance and Caching
    CACHE_OPTIMIZATION_RESULTS: bool = True
    CACHE_EXPIRY_HOURS: int = 6
    MAX_CONCURRENT_OPTIMIZATIONS: int = 3
    OPTIMIZATION_TIMEOUT_MINUTES: int = 30
    
    # Data Configuration
    MIN_HISTORY_DAYS: int = 63  # Minimum 3 months of history for optimization
    MAX_HISTORY_DAYS: int = 1260  # Maximum 5 years of history
    DEFAULT_LOOKBACK_DAYS: int = 252  # Default 1 year lookback
    CORRELATION_WINDOW_DAYS: int = 126  # 6 months for correlation calculation
    
    # Monitoring and Alerts
    ENABLE_PERFORMANCE_MONITORING: bool = True
    ALERT_THRESHOLD_DRAWDOWN: Decimal = Decimal("0.05")  # 5% drawdown alert
    ALERT_THRESHOLD_VOLATILITY: Decimal = Decimal("0.20")  # 20% volatility alert
    ALERT_THRESHOLD_CORRELATION: Decimal = Decimal("0.9")  # 90% correlation alert
    
    # Compliance and Regulatory
    ENABLE_UCITS_COMPLIANCE: bool = False
    ENABLE_RIA_COMPLIANCE: bool = True
    MAX_SINGLE_ISSUER_EXPOSURE: Decimal = Decimal("0.05")  # 5% maximum single issuer
    MAX_ILLIQUID_ALLOCATION: Decimal = Decimal("0.15")  # 15% maximum illiquid strategies
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Storage
    RESULTS_STORAGE_PATH: str = "/app/results"
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("RESULTS_STORAGE_PATH", pre=True)
    def create_results_storage_path(cls, v):
        """Ensure results storage path exists."""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator("MIN_WEIGHT", "MAX_WEIGHT", "MAX_CONCENTRATION", pre=True)
    def validate_weight_constraints(cls, v):
        """Validate weight constraints are reasonable."""
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v < 0 or v > 1:
            raise ValueError("Weight constraints must be between 0 and 1")
        return v
    
    @validator("MAX_PORTFOLIO_VOLATILITY", "MAX_DRAWDOWN_LIMIT", pre=True)
    def validate_risk_limits(cls, v):
        """Validate risk limits are reasonable."""
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0 or v > 1:
            raise ValueError("Risk limits must be between 0 and 1")
        return v
    
    @validator("DRIFT_THRESHOLD", "VOLATILITY_THRESHOLD", pre=True)
    def validate_rebalancing_thresholds(cls, v):
        """Validate rebalancing thresholds are reasonable."""
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0 or v > 0.5:
            raise ValueError("Rebalancing thresholds must be between 0 and 0.5")
        return v
    
    @validator("DEFAULT_OPTIMIZATION_METHOD")
    def validate_optimization_method(cls, v):
        """Validate optimization method is supported."""
        valid_methods = ["MEAN_VARIANCE", "RISK_PARITY", "BLACK_LITTERMAN", "MIN_VARIANCE", "MAX_SHARPE"]
        if v not in valid_methods:
            raise ValueError(f"Optimization method must be one of: {valid_methods}")
        return v
    
    @validator("REBALANCING_FREQUENCY")
    def validate_rebalancing_frequency(cls, v):
        """Validate rebalancing frequency is supported."""
        valid_frequencies = ["DAILY", "WEEKLY", "MONTHLY", "QUARTERLY"]
        if v not in valid_frequencies:
            raise ValueError(f"Rebalancing frequency must be one of: {valid_frequencies}")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
