"""
Market data models for instruments and OHLCV data.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, String, Boolean, DateTime, Numeric, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Instrument(Base):
    """Financial instrument model."""
    
    __tablename__ = "instruments"
    __table_args__ = (
        {"schema": "market_data"},
        Index("idx_instruments_symbol_exchange", "symbol", "exchange"),
        Index("idx_instruments_type", "instrument_type"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    symbol = Column(String(20), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    instrument_type = Column(String(50), nullable=False)  # FOREX, STOCK, CRYPTO, etc.
    base_currency = Column(String(10), nullable=True)
    quote_currency = Column(String(10), nullable=True)
    exchange = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    ohlcv_data = relationship("OHLCV", back_populates="instrument", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Instrument(id={self.id}, symbol={self.symbol}, type={self.instrument_type})>"


class OHLCV(Base):
    """OHLCV market data model (TimescaleDB hypertable)."""
    
    __tablename__ = "ohlcv"
    __table_args__ = (
        {"schema": "market_data"},
        Index("idx_ohlcv_instrument_time", "instrument_id", "time"),
        Index("idx_ohlcv_timeframe", "timeframe"),
    )
    
    time = Column(DateTime(timezone=True), primary_key=True, nullable=False)
    instrument_id = Column(UUID(as_uuid=True), ForeignKey("market_data.instruments.id"), primary_key=True, nullable=False)
    timeframe = Column(String(10), primary_key=True, nullable=False)  # 1m, 5m, 15m, 1h, 4h, 1d
    open = Column(Numeric(20, 8), nullable=False)
    high = Column(Numeric(20, 8), nullable=False)
    low = Column(Numeric(20, 8), nullable=False)
    close = Column(Numeric(20, 8), nullable=False)
    volume = Column(Numeric(20, 8), default=0, nullable=False)
    
    # Relationships
    instrument = relationship("Instrument", back_populates="ohlcv_data")
    
    def __repr__(self) -> str:
        return f"<OHLCV(instrument_id={self.instrument_id}, time={self.time}, timeframe={self.timeframe})>"


class DataSource(Base):
    """Data source configuration model."""
    
    __tablename__ = "data_sources"
    __table_args__ = {"schema": "market_data"}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False)
    source_type = Column(String(50), nullable=False)  # API, FILE, STREAM, etc.
    endpoint_url = Column(String(500), nullable=True)
    api_key_required = Column(Boolean, default=False, nullable=False)
    rate_limit_per_minute = Column(Numeric(10, 2), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    priority = Column(Numeric(3, 1), default=1.0, nullable=False)  # Higher number = higher priority
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<DataSource(id={self.id}, name={self.name}, type={self.source_type})>"


class DataIngestionJob(Base):
    """Data ingestion job tracking model."""
    
    __tablename__ = "data_ingestion_jobs"
    __table_args__ = (
        {"schema": "market_data"},
        Index("idx_ingestion_jobs_status", "status"),
        Index("idx_ingestion_jobs_source", "data_source_id"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_source_id = Column(UUID(as_uuid=True), ForeignKey("market_data.data_sources.id"), nullable=False)
    job_type = Column(String(50), nullable=False)  # HISTORICAL, REALTIME, BACKFILL
    status = Column(String(20), default="PENDING", nullable=False)  # PENDING, RUNNING, COMPLETED, FAILED
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    records_processed = Column(Numeric(10, 0), default=0, nullable=False)
    records_failed = Column(Numeric(10, 0), default=0, nullable=False)
    error_message = Column(String(1000), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    def __repr__(self) -> str:
        return f"<DataIngestionJob(id={self.id}, type={self.job_type}, status={self.status})>"
