"""
Instruments router for financial instrument management.
"""

import logging
from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.models.market_data import Instrument, OHLCV
from app.schemas.market_data import (
    Instrument as InstrumentSchema,
    InstrumentCreate,
    InstrumentUpdate,
    InstrumentType,
    MarketDataSummary,
    Timeframe
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[InstrumentSchema])
async def list_instruments(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    instrument_type: Optional[InstrumentType] = Query(None, description="Filter by instrument type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search by symbol or name"),
    db: AsyncSession = Depends(get_db)
):
    """
    List financial instruments with optional filtering.

    Args:
        skip: Number of records to skip
        limit: Maximum number of records
        instrument_type: Filter by instrument type
        is_active: Filter by active status
        search: Search term for symbol or name
        db: Database session

    Returns:
        List[InstrumentSchema]: List of instruments
    """
    # Build query conditions
    conditions = []

    if instrument_type:
        conditions.append(Instrument.instrument_type == instrument_type)

    if is_active is not None:
        conditions.append(Instrument.is_active == is_active)

    if search:
        search_term = f"%{search.upper()}%"
        conditions.append(
            (Instrument.symbol.ilike(search_term)) |
            (Instrument.name.ilike(search_term))
        )

    # Build and execute query
    stmt = select(Instrument)
    if conditions:
        stmt = stmt.where(and_(*conditions))

    stmt = stmt.offset(skip).limit(limit).order_by(Instrument.symbol)

    result = await db.execute(stmt)
    instruments = result.scalars().all()

    return [InstrumentSchema.from_orm(instrument) for instrument in instruments]


@router.get("/{instrument_id}", response_model=InstrumentSchema)
async def get_instrument(
    instrument_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific instrument by ID.

    Args:
        instrument_id: Instrument ID
        db: Database session

    Returns:
        InstrumentSchema: Instrument information

    Raises:
        HTTPException: If instrument not found
    """
    stmt = select(Instrument).where(Instrument.id == instrument_id)
    result = await db.execute(stmt)
    instrument = result.scalar_one_or_none()

    if not instrument:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Instrument not found"
        )

    return InstrumentSchema.from_orm(instrument)


@router.get("/symbol/{symbol}", response_model=InstrumentSchema)
async def get_instrument_by_symbol(
    symbol: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific instrument by symbol.

    Args:
        symbol: Instrument symbol
        db: Database session

    Returns:
        InstrumentSchema: Instrument information

    Raises:
        HTTPException: If instrument not found
    """
    stmt = select(Instrument).where(Instrument.symbol == symbol.upper())
    result = await db.execute(stmt)
    instrument = result.scalar_one_or_none()

    if not instrument:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Instrument with symbol '{symbol}' not found"
        )

    return InstrumentSchema.from_orm(instrument)


@router.post("/", response_model=InstrumentSchema)
async def create_instrument(
    instrument_data: InstrumentCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new financial instrument.

    Args:
        instrument_data: Instrument creation data
        db: Database session

    Returns:
        InstrumentSchema: Created instrument

    Raises:
        HTTPException: If symbol already exists
    """
    # Check if symbol already exists
    stmt = select(Instrument).where(Instrument.symbol == instrument_data.symbol.upper())
    result = await db.execute(stmt)
    existing = result.scalar_one_or_none()

    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Instrument with symbol '{instrument_data.symbol}' already exists"
        )

    # Create new instrument
    instrument = Instrument(
        symbol=instrument_data.symbol.upper(),
        name=instrument_data.name,
        instrument_type=instrument_data.instrument_type,
        base_currency=instrument_data.base_currency,
        quote_currency=instrument_data.quote_currency,
        exchange=instrument_data.exchange,
        is_active=True
    )

    db.add(instrument)

    try:
        await db.commit()
        await db.refresh(instrument)

        logger.info(f"Created new instrument: {instrument.symbol}")
        return InstrumentSchema.from_orm(instrument)

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to create instrument: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create instrument"
        )


@router.put("/{instrument_id}", response_model=InstrumentSchema)
async def update_instrument(
    instrument_id: uuid.UUID,
    instrument_update: InstrumentUpdate,
    db: AsyncSession = Depends(get_db)
):
    """
    Update an existing instrument.

    Args:
        instrument_id: Instrument ID
        instrument_update: Update data
        db: Database session

    Returns:
        InstrumentSchema: Updated instrument

    Raises:
        HTTPException: If instrument not found
    """
    stmt = select(Instrument).where(Instrument.id == instrument_id)
    result = await db.execute(stmt)
    instrument = result.scalar_one_or_none()

    if not instrument:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Instrument not found"
        )

    # Update fields
    update_data = instrument_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(instrument, field, value)

    try:
        await db.commit()
        await db.refresh(instrument)

        logger.info(f"Updated instrument: {instrument.symbol}")
        return InstrumentSchema.from_orm(instrument)

    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to update instrument: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update instrument"
        )


@router.get("/{instrument_id}/summary", response_model=MarketDataSummary)
async def get_instrument_summary(
    instrument_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get market data summary for an instrument.

    Args:
        instrument_id: Instrument ID
        db: Database session

    Returns:
        MarketDataSummary: Market data summary

    Raises:
        HTTPException: If instrument not found
    """
    # Get instrument
    stmt = select(Instrument).where(Instrument.id == instrument_id)
    result = await db.execute(stmt)
    instrument = result.scalar_one_or_none()

    if not instrument:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Instrument not found"
        )

    # Get data summary
    stmt = select(
        func.count(OHLCV.time).label("data_count"),
        func.min(OHLCV.time).label("first_timestamp"),
        func.max(OHLCV.time).label("last_timestamp"),
        func.array_agg(func.distinct(OHLCV.timeframe)).label("timeframes")
    ).where(OHLCV.instrument_id == instrument_id)

    result = await db.execute(stmt)
    summary_data = result.first()

    # Get latest data point
    stmt = (
        select(OHLCV)
        .where(OHLCV.instrument_id == instrument_id)
        .order_by(desc(OHLCV.time))
        .limit(1)
    )
    result = await db.execute(stmt)
    latest_data = result.scalar_one_or_none()

    return MarketDataSummary(
        instrument=InstrumentSchema.from_orm(instrument),
        latest_data=latest_data,
        data_count=summary_data.data_count or 0,
        first_timestamp=summary_data.first_timestamp,
        last_timestamp=summary_data.last_timestamp,
        timeframes_available=summary_data.timeframes or []
    )
