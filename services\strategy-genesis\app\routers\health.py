"""
Health check router for Strategy Genesis service.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import psutil
import os

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "AthenaTrader Strategy Genesis",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """Detailed health check with system and dependency status."""
    health_status = {
        "status": "healthy",
        "service": "AthenaTrader Strategy Genesis",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database connectivity check
    try:
        await db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Check for resource warnings
        if cpu_percent > 90:
            health_status["checks"]["system"]["warnings"] = ["High CPU usage"]
        if memory.percent > 90:
            health_status["checks"]["system"]["warnings"] = health_status["checks"]["system"].get("warnings", []) + ["High memory usage"]
        if disk.percent > 90:
            health_status["checks"]["system"]["warnings"] = health_status["checks"]["system"].get("warnings", []) + ["Low disk space"]
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "error",
            "message": f"Failed to get system metrics: {str(e)}"
        }
    
    # AI engines check
    try:
        # Check if model storage directory exists and is writable
        model_path = settings.MODEL_STORAGE_PATH
        if os.path.exists(model_path) and os.access(model_path, os.W_OK):
            health_status["checks"]["ai_engines"] = {
                "status": "healthy",
                "model_storage": "accessible",
                "model_storage_path": model_path
            }
        else:
            health_status["checks"]["ai_engines"] = {
                "status": "warning",
                "model_storage": "not accessible",
                "model_storage_path": model_path
            }
    except Exception as e:
        health_status["checks"]["ai_engines"] = {
            "status": "error",
            "message": f"AI engines check failed: {str(e)}"
        }
    
    # Configuration check
    try:
        config_status = {
            "status": "healthy",
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG,
            "data_nexus_url": settings.DATA_NEXUS_URL,
            "ai_paradigms": ["GP", "RL", "DL"]
        }
        
        # Check external service connectivity (Data Nexus)
        import httpx
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{settings.DATA_NEXUS_URL}/health")
                if response.status_code == 200:
                    config_status["data_nexus_status"] = "healthy"
                else:
                    config_status["data_nexus_status"] = "unhealthy"
                    health_status["status"] = "degraded"
        except Exception:
            config_status["data_nexus_status"] = "unreachable"
            health_status["status"] = "degraded"
        
        health_status["checks"]["configuration"] = config_status
        
    except Exception as e:
        health_status["checks"]["configuration"] = {
            "status": "error",
            "message": f"Configuration check failed: {str(e)}"
        }
    
    return health_status


@router.get("/ai-engines")
async def ai_engines_status():
    """Check status of AI engines."""
    engines_status = {
        "timestamp": datetime.utcnow().isoformat(),
        "engines": {}
    }
    
    # Genetic Programming engine
    try:
        from app.ai.genetic_programming import GeneticProgrammingEngine
        gp_engine = GeneticProgrammingEngine()
        engines_status["engines"]["genetic_programming"] = {
            "status": "available",
            "config": {
                "population_size": settings.GP_POPULATION_SIZE,
                "generations": settings.GP_GENERATIONS,
                "max_tree_depth": settings.GP_MAX_TREE_DEPTH
            }
        }
    except Exception as e:
        engines_status["engines"]["genetic_programming"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Reinforcement Learning engine
    try:
        from app.ai.reinforcement_learning import ReinforcementLearningEngine
        rl_engine = ReinforcementLearningEngine()
        engines_status["engines"]["reinforcement_learning"] = {
            "status": "available",
            "config": {
                "training_episodes": settings.RL_TRAINING_EPISODES,
                "learning_rate": settings.RL_LEARNING_RATE,
                "batch_size": settings.RL_BATCH_SIZE
            }
        }
    except Exception as e:
        engines_status["engines"]["reinforcement_learning"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Deep Learning engine
    try:
        from app.ai.deep_learning import DeepLearningEngine
        dl_engine = DeepLearningEngine()
        engines_status["engines"]["deep_learning"] = {
            "status": "available",
            "config": {
                "batch_size": settings.DL_BATCH_SIZE,
                "epochs": settings.DL_EPOCHS,
                "lstm_units": settings.DL_LSTM_UNITS
            }
        }
    except Exception as e:
        engines_status["engines"]["deep_learning"] = {
            "status": "error",
            "message": str(e)
        }
    
    return engines_status


@router.get("/metrics")
async def get_metrics(db: AsyncSession = Depends(get_db)):
    """Get service metrics and statistics."""
    try:
        # Get strategy counts by paradigm
        from app.models.strategy import Strategy
        from sqlalchemy import func, select
        
        paradigm_counts = await db.execute(
            select(Strategy.ai_paradigm, func.count(Strategy.id))
            .group_by(Strategy.ai_paradigm)
        )
        
        # Get training run statistics
        from app.models.strategy import StrategyTrainingRun
        
        training_stats = await db.execute(
            select(
                StrategyTrainingRun.status,
                func.count(StrategyTrainingRun.id)
            )
            .group_by(StrategyTrainingRun.status)
        )
        
        # Get recent activity
        recent_strategies = await db.execute(
            select(func.count(Strategy.id))
            .where(Strategy.created_at >= datetime.utcnow() - timedelta(days=7))
        )
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "strategies": {
                "by_paradigm": dict(paradigm_counts.fetchall()),
                "recent_count": recent_strategies.scalar()
            },
            "training_runs": {
                "by_status": dict(training_stats.fetchall())
            },
            "system": {
                "uptime_seconds": psutil.boot_time(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2)
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )
