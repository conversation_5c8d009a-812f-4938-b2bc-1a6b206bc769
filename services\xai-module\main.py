"""
AthenaTrader XAI (Explainable AI) Module

Comprehensive strategy explainability and performance attribution service that provides
institutional-grade model interpretability, regulatory compliance, and transparent
AI decision-making for trading strategies across all AI paradigms.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import health, explanations, attribution, interpretability, audit, dashboards

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader XAI Module...")
    await init_db()
    logger.info("Database initialized")
    
    # Initialize XAI components
    from app.engine.explainability import StrategyExplainer
    from app.engine.attribution import PerformanceAttributor
    from app.engine.interpretability import ModelInterpreter
    from app.engine.audit import AuditTrailManager
    from app.engine.visualization import VisualizationEngine
    
    # Store engines in app state
    app.state.strategy_explainer = StrategyExplainer()
    app.state.performance_attributor = PerformanceAttributor()
    app.state.model_interpreter = ModelInterpreter()
    app.state.audit_manager = AuditTrailManager()
    app.state.visualization_engine = VisualizationEngine()
    
    logger.info("XAI engines initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader XAI Module...")
    
    # Cleanup engines
    if hasattr(app.state, 'strategy_explainer'):
        await app.state.strategy_explainer.cleanup()
    if hasattr(app.state, 'performance_attributor'):
        await app.state.performance_attributor.cleanup()
    if hasattr(app.state, 'model_interpreter'):
        await app.state.model_interpreter.cleanup()
    if hasattr(app.state, 'audit_manager'):
        await app.state.audit_manager.cleanup()
    if hasattr(app.state, 'visualization_engine'):
        await app.state.visualization_engine.cleanup()


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader XAI Module",
    description="Comprehensive strategy explainability and performance attribution service for AI-generated trading strategies",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(explanations.router, prefix="/explanations", tags=["explanations"])
app.include_router(attribution.router, prefix="/attribution", tags=["attribution"])
app.include_router(interpretability.router, prefix="/interpretability", tags=["interpretability"])
app.include_router(audit.router, prefix="/audit", tags=["audit"])
app.include_router(dashboards.router, prefix="/dashboards", tags=["dashboards"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader XAI Module",
        "version": "0.1.0",
        "status": "operational",
        "capabilities": [
            "strategy_explainability",
            "shap_explanations",
            "lime_explanations",
            "feature_importance_analysis",
            "performance_attribution",
            "model_interpretability",
            "audit_trail_management",
            "regulatory_compliance",
            "interactive_dashboards",
            "real_time_explanations"
        ]
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
