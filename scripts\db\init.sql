-- AthenaTrader Database Initialization Script
-- This script sets up the initial database structure for AthenaTrader

-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Create schemas for different modules
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS market_data;
CREATE SCHEMA IF NOT EXISTS strategies;
CREATE SCHEMA IF NOT EXISTS portfolios;
CREATE SCHEMA IF NOT EXISTS backtests;
CREATE SCHEMA IF NOT EXISTS executions;
CREATE SCHEMA IF NOT EXISTS analytics;

-- Users table (Authentication)
CREATE TABLE IF NOT EXISTS auth.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(255),
    is_active BOOLEAN DEFAULT true,
    is_expert <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User sessions table
CREATE TABLE IF NOT EXISTS auth.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Market data instruments
CREATE TABLE IF NOT EXISTS market_data.instruments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(20) NOT NULL,
    name VARCHAR(255) NOT NULL,
    instrument_type VARCHAR(50) NOT NULL, -- FOREX, STOCK, CRYPTO, etc.
    base_currency VARCHAR(10),
    quote_currency VARCHAR(10),
    exchange VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, exchange)
);

-- Market data OHLCV (TimescaleDB hypertable)
CREATE TABLE IF NOT EXISTS market_data.ohlcv (
    time TIMESTAMP WITH TIME ZONE NOT NULL,
    instrument_id UUID REFERENCES market_data.instruments(id),
    timeframe VARCHAR(10) NOT NULL, -- 1m, 5m, 15m, 1h, 4h, 1d
    open DECIMAL(20, 8) NOT NULL,
    high DECIMAL(20, 8) NOT NULL,
    low DECIMAL(20, 8) NOT NULL,
    close DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) DEFAULT 0,
    PRIMARY KEY (time, instrument_id, timeframe)
);

-- Convert to hypertable
SELECT create_hypertable('market_data.ohlcv', 'time', if_not_exists => TRUE);

-- Strategies table (enhanced for Strategy Genesis)
CREATE TABLE IF NOT EXISTS strategies.strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    ai_paradigm VARCHAR(50) NOT NULL, -- GP, RL, DL, HYBRID
    paradigm_config JSONB DEFAULT '{}',
    parameters JSONB DEFAULT '{}',
    trading_rules JSONB,
    model_architecture JSONB,
    performance_metrics JSONB DEFAULT '{}',
    performance_score DECIMAL(10, 6),
    training_data_start TIMESTAMP WITH TIME ZONE,
    training_data_end TIMESTAMP WITH TIME ZONE,
    training_duration_seconds INTEGER,
    training_episodes INTEGER,
    training_epochs INTEGER,
    status VARCHAR(50) DEFAULT 'CREATED', -- CREATED, TRAINING, TRAINED, DEPLOYED, ARCHIVED
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    model_path VARCHAR(500),
    checkpoint_path VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    trained_at TIMESTAMP WITH TIME ZONE,
    deployed_at TIMESTAMP WITH TIME ZONE
);

-- Strategy versions for version control
CREATE TABLE IF NOT EXISTS strategies.strategy_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_id UUID REFERENCES strategies.strategies(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    parameters JSONB NOT NULL,
    trading_rules JSONB,
    model_architecture JSONB,
    performance_metrics JSONB DEFAULT '{}',
    performance_score DECIMAL(10, 6),
    training_data_start TIMESTAMP WITH TIME ZONE,
    training_data_end TIMESTAMP WITH TIME ZONE,
    training_duration_seconds INTEGER,
    model_path VARCHAR(500),
    change_description TEXT,
    is_baseline BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(strategy_id, version_number)
);

-- Strategy training runs
CREATE TABLE IF NOT EXISTS strategies.strategy_training_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_id UUID REFERENCES strategies.strategies(id) ON DELETE CASCADE,
    training_config JSONB NOT NULL,
    hyperparameters JSONB DEFAULT '{}',
    data_start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    data_end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    instruments_used JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
    current_epoch INTEGER DEFAULT 0,
    total_epochs INTEGER NOT NULL,
    current_generation INTEGER DEFAULT 0,
    total_generations INTEGER,
    training_metrics JSONB DEFAULT '{}',
    validation_metrics JSONB DEFAULT '{}',
    best_performance DECIMAL(10, 6),
    cpu_usage_percent DECIMAL(5, 2),
    memory_usage_mb INTEGER,
    gpu_usage_percent DECIMAL(5, 2),
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    estimated_completion TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    error_traceback TEXT,
    checkpoint_path VARCHAR(500),
    last_checkpoint_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- AI paradigms configuration
CREATE TABLE IF NOT EXISTS strategies.ai_paradigms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL, -- GP, RL, DL, HYBRID
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_config JSONB DEFAULT '{}',
    supported_parameters JSONB DEFAULT '[]',
    supports_online_learning BOOLEAN DEFAULT false,
    supports_multi_asset BOOLEAN DEFAULT true,
    supports_multi_timeframe BOOLEAN DEFAULT true,
    min_memory_mb INTEGER DEFAULT 512,
    recommended_memory_mb INTEGER DEFAULT 2048,
    requires_gpu BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Portfolios table
CREATE TABLE IF NOT EXISTS portfolios.portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    risk_profile VARCHAR(50) NOT NULL, -- CONSERVATIVE, MODERATE, AGGRESSIVE
    constraints JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Portfolio strategy allocations
CREATE TABLE IF NOT EXISTS portfolios.portfolio_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_id UUID REFERENCES portfolios.portfolios(id) ON DELETE CASCADE,
    strategy_id UUID REFERENCES strategies.strategies(id) ON DELETE CASCADE,
    allocation_percentage DECIMAL(5, 2) NOT NULL CHECK (allocation_percentage >= 0 AND allocation_percentage <= 100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, strategy_id)
);

-- Enhanced Backtests table for Backtesting Engine
CREATE TABLE IF NOT EXISTS backtests.backtests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    strategy_id UUID REFERENCES strategies.strategies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,

    -- Backtest Configuration
    config JSONB NOT NULL DEFAULT '{}',

    -- Time Period
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    timeframe VARCHAR(10) NOT NULL DEFAULT '1h',

    -- Market Data Configuration
    instruments JSONB NOT NULL,
    benchmark_symbol VARCHAR(20) DEFAULT 'SPY',

    -- Simulation Parameters
    initial_capital DECIMAL(15, 2) NOT NULL DEFAULT 100000.00,
    commission_bps DECIMAL(8, 4) NOT NULL DEFAULT 1.0,
    spread_bps DECIMAL(8, 4) NOT NULL DEFAULT 2.0,
    slippage_bps DECIMAL(8, 4) NOT NULL DEFAULT 0.5,
    market_impact_factor DECIMAL(8, 6) NOT NULL DEFAULT 0.1,

    -- Risk Management
    max_position_size DECIMAL(5, 4) NOT NULL DEFAULT 0.2,
    stop_loss_pct DECIMAL(5, 4),
    take_profit_pct DECIMAL(5, 4),
    max_leverage DECIMAL(8, 2) NOT NULL DEFAULT 1.0,

    -- Execution Status
    status VARCHAR(50) DEFAULT 'PENDING' NOT NULL,
    progress_pct DECIMAL(5, 2) DEFAULT 0.0 NOT NULL,

    -- Performance Results
    total_return DECIMAL(10, 6),
    annualized_return DECIMAL(10, 6),
    volatility DECIMAL(10, 6),
    sharpe_ratio DECIMAL(10, 6),
    sortino_ratio DECIMAL(10, 6),
    calmar_ratio DECIMAL(10, 6),
    max_drawdown DECIMAL(10, 6),
    max_drawdown_duration_days INTEGER,

    -- Trade Statistics
    total_trades INTEGER,
    winning_trades INTEGER,
    losing_trades INTEGER,
    win_rate DECIMAL(5, 4),
    profit_factor DECIMAL(10, 6),
    avg_trade_return DECIMAL(10, 6),
    avg_trade_duration_hours DECIMAL(10, 2),

    -- Risk Metrics
    var_95 DECIMAL(10, 6),
    cvar_95 DECIMAL(10, 6),
    beta DECIMAL(10, 6),
    alpha DECIMAL(10, 6),
    information_ratio DECIMAL(10, 6),
    treynor_ratio DECIMAL(10, 6),

    -- Detailed Results
    performance_metrics JSONB DEFAULT '{}',
    equity_curve JSONB,
    drawdown_curve JSONB,

    -- Execution Information
    bars_processed INTEGER,
    execution_time_seconds INTEGER,
    memory_usage_mb INTEGER,

    -- Error Handling
    error_message TEXT,
    error_traceback TEXT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Enhanced Backtest trades table
CREATE TABLE IF NOT EXISTS backtests.backtest_trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backtest_id UUID REFERENCES backtests.backtests(id) ON DELETE CASCADE,

    -- Trade Identification
    trade_id VARCHAR(50) NOT NULL,
    instrument_id UUID REFERENCES market_data.instruments(id),
    symbol VARCHAR(20) NOT NULL,

    -- Trade Details
    side VARCHAR(10) NOT NULL, -- BUY, SELL
    quantity DECIMAL(15, 8) NOT NULL,
    price DECIMAL(15, 8) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,

    -- Execution Details
    signal_strength DECIMAL(5, 4),
    intended_quantity DECIMAL(15, 8),
    fill_type VARCHAR(20) NOT NULL DEFAULT 'FULL',

    -- Costs and Slippage
    commission DECIMAL(15, 8) NOT NULL DEFAULT 0.0,
    spread_cost DECIMAL(15, 8) NOT NULL DEFAULT 0.0,
    slippage_cost DECIMAL(15, 8) NOT NULL DEFAULT 0.0,
    market_impact_cost DECIMAL(15, 8) NOT NULL DEFAULT 0.0,
    total_cost DECIMAL(15, 8) NOT NULL DEFAULT 0.0,

    -- Position Information
    position_before DECIMAL(15, 8) NOT NULL DEFAULT 0.0,
    position_after DECIMAL(15, 8) NOT NULL,
    portfolio_value_before DECIMAL(15, 2) NOT NULL,
    portfolio_value_after DECIMAL(15, 2) NOT NULL,

    -- Trade Performance
    entry_price DECIMAL(15, 8),
    exit_price DECIMAL(15, 8),
    entry_timestamp TIMESTAMP WITH TIME ZONE,
    exit_timestamp TIMESTAMP WITH TIME ZONE,
    pnl DECIMAL(15, 8),
    pnl_pct DECIMAL(10, 6),
    duration_hours DECIMAL(10, 2),

    -- Risk Management
    stop_loss_triggered BOOLEAN DEFAULT FALSE NOT NULL,
    take_profit_triggered BOOLEAN DEFAULT FALSE NOT NULL,
    risk_limit_triggered BOOLEAN DEFAULT FALSE NOT NULL,

    -- Market Context
    market_price DECIMAL(15, 8),
    bid_price DECIMAL(15, 8),
    ask_price DECIMAL(15, 8),
    volatility DECIMAL(10, 6),

    -- Metadata
    metadata JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Backtest analytics table
CREATE TABLE IF NOT EXISTS backtests.backtest_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backtest_id UUID REFERENCES backtests.backtests(id) ON DELETE CASCADE,

    -- Analysis Period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    period_type VARCHAR(20) NOT NULL, -- DAILY, WEEKLY, MONTHLY, YEARLY, FULL

    -- Performance Metrics
    period_return DECIMAL(10, 6),
    cumulative_return DECIMAL(10, 6),
    benchmark_return DECIMAL(10, 6),
    excess_return DECIMAL(10, 6),

    -- Risk Metrics
    volatility DECIMAL(10, 6),
    downside_volatility DECIMAL(10, 6),
    tracking_error DECIMAL(10, 6),

    -- Ratios
    sharpe_ratio DECIMAL(10, 6),
    sortino_ratio DECIMAL(10, 6),
    information_ratio DECIMAL(10, 6),
    calmar_ratio DECIMAL(10, 6),

    -- Drawdown Analysis
    max_drawdown DECIMAL(10, 6),
    drawdown_duration_days INTEGER,
    recovery_time_days INTEGER,

    -- Trade Statistics
    trade_count INTEGER,
    win_count INTEGER,
    loss_count INTEGER,
    win_rate DECIMAL(5, 4),
    avg_win DECIMAL(10, 6),
    avg_loss DECIMAL(10, 6),
    profit_factor DECIMAL(10, 6),

    -- Detailed Analytics
    detailed_metrics JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ohlcv_instrument_time ON market_data.ohlcv (instrument_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_ohlcv_timeframe ON market_data.ohlcv (timeframe);
CREATE INDEX IF NOT EXISTS idx_strategies_user ON strategies.strategies (user_id);
CREATE INDEX IF NOT EXISTS idx_strategies_paradigm ON strategies.strategies (ai_paradigm);
CREATE INDEX IF NOT EXISTS idx_strategies_status ON strategies.strategies (status);
CREATE INDEX IF NOT EXISTS idx_strategies_performance ON strategies.strategies (performance_score);
CREATE INDEX IF NOT EXISTS idx_strategy_versions_strategy_id ON strategies.strategy_versions (strategy_id);
CREATE INDEX IF NOT EXISTS idx_strategy_versions_version ON strategies.strategy_versions (version_number);
CREATE INDEX IF NOT EXISTS idx_strategy_versions_performance ON strategies.strategy_versions (performance_score);
CREATE INDEX IF NOT EXISTS idx_training_runs_strategy_id ON strategies.strategy_training_runs (strategy_id);
CREATE INDEX IF NOT EXISTS idx_training_runs_status ON strategies.strategy_training_runs (status);
CREATE INDEX IF NOT EXISTS idx_training_runs_start_time ON strategies.strategy_training_runs (start_time);
CREATE INDEX IF NOT EXISTS idx_portfolios_user ON portfolios.portfolios (user_id);
CREATE INDEX IF NOT EXISTS idx_backtests_strategy ON backtests.backtests (strategy_id);
CREATE INDEX IF NOT EXISTS idx_backtests_user ON backtests.backtests (user_id);
CREATE INDEX IF NOT EXISTS idx_backtests_status ON backtests.backtests (status);
CREATE INDEX IF NOT EXISTS idx_backtests_created_at ON backtests.backtests (created_at);
CREATE INDEX IF NOT EXISTS idx_backtests_performance ON backtests.backtests (total_return);
CREATE INDEX IF NOT EXISTS idx_backtest_trades_backtest ON backtests.backtest_trades (backtest_id);
CREATE INDEX IF NOT EXISTS idx_backtest_trades_timestamp ON backtests.backtest_trades (timestamp);
CREATE INDEX IF NOT EXISTS idx_backtest_trades_instrument ON backtests.backtest_trades (instrument_id);
CREATE INDEX IF NOT EXISTS idx_backtest_trades_side ON backtests.backtest_trades (side);
CREATE INDEX IF NOT EXISTS idx_backtest_analytics_backtest_id ON backtests.backtest_analytics (backtest_id);
CREATE INDEX IF NOT EXISTS idx_backtest_analytics_period ON backtests.backtest_analytics (period_start, period_end);

-- Insert default expert users (for development)
INSERT INTO auth.users (username, email, hashed_password, full_name, is_expert) VALUES
('expert1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 1', true),
('expert2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 2', true),
('expert3', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 3', true),
('expert4', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 4', true),
('expert5', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 5', true),
('expert6', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Expert Trader 6', true)
ON CONFLICT (username) DO NOTHING;

-- Insert AI paradigms configuration
INSERT INTO strategies.ai_paradigms (name, display_name, description, default_config, supported_parameters, supports_online_learning, supports_multi_asset, supports_multi_timeframe, min_memory_mb, recommended_memory_mb, requires_gpu) VALUES
('GP', 'Genetic Programming', 'Evolutionary algorithm that evolves trading strategies using genetic programming techniques',
 '{"population_size": 100, "generations": 50, "tournament_size": 3, "crossover_prob": 0.8, "mutation_prob": 0.2, "max_tree_depth": 10}',
 '["population_size", "generations", "tournament_size", "crossover_prob", "mutation_prob", "max_tree_depth", "elitism_size"]',
 false, true, true, 512, 2048, false),
('RL', 'Reinforcement Learning', 'Agent-based learning that optimizes trading strategies through trial and error in market environments',
 '{"algorithm": "PPO", "learning_rate": 0.001, "discount_factor": 0.99, "exploration_rate": 0.1, "batch_size": 32, "memory_size": 10000, "episodes": 1000}',
 '["algorithm", "learning_rate", "discount_factor", "exploration_rate", "batch_size", "memory_size", "target_update_freq", "episodes"]',
 true, true, true, 1024, 4096, false),
('DL', 'Deep Learning', 'Neural network-based models including LSTM, GRU, and Transformer architectures for pattern recognition',
 '{"model_type": "LSTM", "batch_size": 64, "epochs": 100, "learning_rate": 0.001, "dropout_rate": 0.2, "lstm_units": 128, "attention_heads": 8, "sequence_length": 60}',
 '["model_type", "batch_size", "epochs", "learning_rate", "dropout_rate", "lstm_units", "attention_heads", "sequence_length", "layers"]',
 true, true, true, 2048, 8192, true),
('HYBRID', 'Hybrid AI', 'Combination of multiple AI paradigms for enhanced strategy development and performance',
 '{"primary_paradigm": "DL", "secondary_paradigm": "RL", "ensemble_method": "weighted_average", "weights": [0.6, 0.4]}',
 '["primary_paradigm", "secondary_paradigm", "ensemble_method", "weights", "combination_strategy"]',
 true, true, true, 4096, 16384, true)
ON CONFLICT (name) DO NOTHING;

-- Insert common FOREX instruments
INSERT INTO market_data.instruments (symbol, name, instrument_type, base_currency, quote_currency) VALUES
('EURUSD', 'Euro / US Dollar', 'FOREX', 'EUR', 'USD'),
('GBPUSD', 'British Pound / US Dollar', 'FOREX', 'GBP', 'USD'),
('USDJPY', 'US Dollar / Japanese Yen', 'FOREX', 'USD', 'JPY'),
('USDCHF', 'US Dollar / Swiss Franc', 'FOREX', 'USD', 'CHF'),
('AUDUSD', 'Australian Dollar / US Dollar', 'FOREX', 'AUD', 'USD'),
('USDCAD', 'US Dollar / Canadian Dollar', 'FOREX', 'USD', 'CAD'),
('NZDUSD', 'New Zealand Dollar / US Dollar', 'FOREX', 'NZD', 'USD'),
('EURGBP', 'Euro / British Pound', 'FOREX', 'EUR', 'GBP'),
('EURJPY', 'Euro / Japanese Yen', 'FOREX', 'EUR', 'JPY'),
('GBPJPY', 'British Pound / Japanese Yen', 'FOREX', 'GBP', 'JPY')
ON CONFLICT (symbol, exchange) DO NOTHING;
