"""
Reinforcement Learning module for trading strategy development using Ray/RLlib.
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Optional, Tuple, List, Callable
import ray
from ray import tune
from ray.rllib.algorithms.ppo import PPO, PPOConfig
from ray.rllib.algorithms.dqn import DQN, DQNConfig
from ray.rllib.algorithms.a3c import A3C, A3CConfig
from ray.rllib.env.env_context import EnvContext
import pickle
import os
from datetime import datetime

from app.core.config import settings
from app.schemas.strategy import ReinforcementLearningConfig, PerformanceMetrics

logger = logging.getLogger(__name__)


class TradingEnvironment(gym.Env):
    """Custom trading environment for reinforcement learning."""

    def __init__(self, config: EnvContext):
        """
        Initialize trading environment.

        Args:
            config: Environment configuration containing market data and parameters
        """
        super().__init__()

        # Extract configuration
        self.data = config.get("data")
        self.initial_capital = config.get("initial_capital", 10000.0)
        self.transaction_cost = config.get("transaction_cost", 0.001)
        self.max_position = config.get("max_position", 1.0)
        self.lookback_window = config.get("lookback_window", 20)

        # Trading state
        self.current_step = 0
        self.current_capital = self.initial_capital
        self.current_position = 0.0
        self.trades = []
        self.equity_curve = []

        # Precompute features
        self._precompute_features()

        # Define action and observation spaces
        # Actions: 0=Hold, 1=Buy, 2=Sell (discrete) or continuous position [-1, 1]
        self.action_space = spaces.Box(low=-1.0, high=1.0, shape=(1,), dtype=np.float32)

        # Observations: OHLCV + technical indicators + portfolio state
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.lookback_window, self.feature_dim),
            dtype=np.float32
        )

        # Episode tracking
        self.max_steps = len(self.data) - self.lookback_window - 1

    def _precompute_features(self):
        """Precompute technical indicators and features."""
        import talib

        close = self.data['close'].values
        high = self.data['high'].values
        low = self.data['low'].values
        volume = self.data['volume'].values

        # Price features (normalized)
        self.features = pd.DataFrame()
        self.features['close'] = (close - close.mean()) / close.std()
        self.features['high'] = (high - high.mean()) / high.std()
        self.features['low'] = (low - low.mean()) / low.std()
        self.features['volume'] = (volume - volume.mean()) / volume.std()

        # Returns
        self.features['returns'] = np.log(close / np.roll(close, 1))
        self.features['returns'].fillna(0, inplace=True)

        # Technical indicators
        self.features['sma_10'] = talib.SMA(close, timeperiod=10)
        self.features['sma_20'] = talib.SMA(close, timeperiod=20)
        self.features['ema_10'] = talib.EMA(close, timeperiod=10)
        self.features['rsi'] = talib.RSI(close, timeperiod=14)

        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close)
        self.features['macd'] = macd
        self.features['macd_signal'] = macd_signal
        self.features['macd_hist'] = macd_hist

        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20)
        self.features['bb_upper'] = bb_upper
        self.features['bb_lower'] = bb_lower
        self.features['bb_width'] = (bb_upper - bb_lower) / bb_middle

        # Volatility
        self.features['volatility'] = talib.ATR(high, low, close, timeperiod=14)

        # Normalize technical indicators
        for col in ['sma_10', 'sma_20', 'ema_10', 'bb_upper', 'bb_lower']:
            if col in self.features.columns:
                self.features[col] = (self.features[col] - close.mean()) / close.std()

        # Fill NaN values
        self.features.fillna(method='ffill', inplace=True)
        self.features.fillna(0, inplace=True)

        self.feature_dim = len(self.features.columns)

    def reset(self, *, seed=None, options=None):
        """Reset the environment to initial state."""
        super().reset(seed=seed)

        self.current_step = self.lookback_window
        self.current_capital = self.initial_capital
        self.current_position = 0.0
        self.trades = []
        self.equity_curve = [self.initial_capital]

        return self._get_observation(), {}

    def _get_observation(self) -> np.ndarray:
        """Get current observation (features + portfolio state)."""
        # Get feature window
        start_idx = self.current_step - self.lookback_window
        end_idx = self.current_step

        feature_window = self.features.iloc[start_idx:end_idx].values

        # Add portfolio state to each timestep
        portfolio_state = np.array([
            self.current_position,
            self.current_capital / self.initial_capital - 1.0,  # Capital change
            len(self.trades) / 100.0  # Number of trades (normalized)
        ])

        # Expand portfolio state to match window size
        portfolio_window = np.tile(portfolio_state, (self.lookback_window, 1))

        # Concatenate features and portfolio state
        observation = np.concatenate([feature_window, portfolio_window], axis=1)

        return observation.astype(np.float32)

    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """Execute one step in the environment."""
        # Extract action (target position)
        target_position = float(action[0])
        target_position = np.clip(target_position, -self.max_position, self.max_position)

        # Get current price
        current_price = self.data.iloc[self.current_step]['close']

        # Calculate position change
        position_change = target_position - self.current_position

        # Execute trade if significant change
        reward = 0.0
        if abs(position_change) > 0.01:  # Minimum trade threshold
            # Calculate transaction cost
            trade_cost = abs(position_change) * self.current_capital * self.transaction_cost
            self.current_capital -= trade_cost

            # Update position
            self.current_position = target_position

            # Record trade
            self.trades.append({
                'step': self.current_step,
                'price': current_price,
                'position_change': position_change,
                'new_position': self.current_position,
                'cost': trade_cost
            })

        # Calculate portfolio value
        position_value = self.current_position * self.current_capital * current_price
        total_equity = self.current_capital + position_value
        self.equity_curve.append(total_equity)

        # Calculate reward
        if len(self.equity_curve) > 1:
            # Return-based reward
            portfolio_return = (total_equity - self.equity_curve[-2]) / self.equity_curve[-2]
            reward = portfolio_return

            # Add risk penalty
            if len(self.equity_curve) > 10:
                recent_returns = np.diff(self.equity_curve[-10:]) / self.equity_curve[-11:-1]
                volatility = np.std(recent_returns)
                reward -= 0.1 * volatility  # Risk penalty

            # Add transaction cost penalty
            if abs(position_change) > 0.01:
                reward -= 0.01  # Transaction penalty

        # Move to next step
        self.current_step += 1

        # Check if episode is done
        done = self.current_step >= self.max_steps
        truncated = False

        # Get next observation
        observation = self._get_observation() if not done else np.zeros_like(self._get_observation())

        # Additional info
        info = {
            'equity': total_equity,
            'position': self.current_position,
            'trades': len(self.trades),
            'step': self.current_step
        }

        return observation, reward, done, truncated, info

    def calculate_performance(self) -> PerformanceMetrics:
        """Calculate performance metrics for the episode."""
        if len(self.equity_curve) < 2:
            return PerformanceMetrics()

        returns = np.diff(self.equity_curve) / self.equity_curve[:-1]

        # Basic metrics
        total_return = (self.equity_curve[-1] - self.equity_curve[0]) / self.equity_curve[0]
        volatility = np.std(returns) * np.sqrt(252)  # Annualized

        # Sharpe ratio
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0

        # Maximum drawdown
        peak = np.maximum.accumulate(self.equity_curve)
        drawdown = (self.equity_curve - peak) / peak
        max_drawdown = np.min(drawdown)

        # Win rate
        winning_trades = sum(1 for trade in self.trades if trade['position_change'] > 0)
        win_rate = winning_trades / len(self.trades) if self.trades else 0

        return PerformanceMetrics(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=abs(max_drawdown),
            win_rate=win_rate,
            volatility=volatility
        )


class ReinforcementLearningEngine:
    """Reinforcement Learning engine for trading strategy development."""

    def __init__(self, config: Optional[ReinforcementLearningConfig] = None):
        """Initialize RL engine."""
        self.config = config or ReinforcementLearningConfig()
        self.algorithm = None
        self.checkpoint_path = None
        self.training_results = []

        # Initialize Ray if not already initialized
        if not ray.is_initialized():
            ray.init(ignore_reinit_error=True)

    async def cleanup(self):
        """Cleanup Ray resources."""
        if ray.is_initialized():
            ray.shutdown()

    def create_algorithm_config(self) -> Dict[str, Any]:
        """Create algorithm configuration based on selected algorithm."""
        env_config = {
            "data": None,  # Will be set during training
            "initial_capital": 10000.0,
            "transaction_cost": 0.001,
            "max_position": 1.0,
            "lookback_window": 20
        }

        if self.config.algorithm == "PPO":
            config = (
                PPOConfig()
                .environment(TradingEnvironment, env_config=env_config)
                .framework("torch")
                .training(
                    lr=self.config.learning_rate,
                    gamma=self.config.discount_factor,
                    train_batch_size=self.config.batch_size * 4,
                    sgd_minibatch_size=self.config.batch_size,
                    num_sgd_iter=10,
                    clip_param=0.2,
                    entropy_coeff=0.01,
                    vf_loss_coeff=0.5,
                )
                .rollouts(
                    num_rollout_workers=2,
                    rollout_fragment_length=200,
                )
                .resources(
                    num_gpus=1 if ray.get_gpu_ids() else 0,
                    num_cpus_per_worker=1,
                )
            )
        elif self.config.algorithm == "DQN":
            config = (
                DQNConfig()
                .environment(TradingEnvironment, env_config=env_config)
                .framework("torch")
                .training(
                    lr=self.config.learning_rate,
                    gamma=self.config.discount_factor,
                    train_batch_size=self.config.batch_size,
                    replay_buffer_config={
                        "capacity": self.config.memory_size,
                    },
                    target_network_update_freq=self.config.target_update_freq,
                    exploration_config={
                        "type": "EpsilonGreedy",
                        "initial_epsilon": self.config.exploration_rate,
                        "final_epsilon": 0.01,
                        "epsilon_timesteps": 10000,
                    },
                )
                .rollouts(
                    num_rollout_workers=2,
                )
                .resources(
                    num_gpus=1 if ray.get_gpu_ids() else 0,
                )
            )
        elif self.config.algorithm == "A3C":
            config = (
                A3CConfig()
                .environment(TradingEnvironment, env_config=env_config)
                .framework("torch")
                .training(
                    lr=self.config.learning_rate,
                    gamma=self.config.discount_factor,
                    entropy_coeff=0.01,
                    vf_loss_coeff=0.5,
                )
                .rollouts(
                    num_rollout_workers=4,
                )
                .resources(
                    num_gpus=1 if ray.get_gpu_ids() else 0,
                )
            )
        else:
            raise ValueError(f"Unsupported algorithm: {self.config.algorithm}")

        return config

    def train_strategy(self, training_data: pd.DataFrame,
                      progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Train a trading strategy using reinforcement learning.

        Args:
            training_data: Historical market data for training
            progress_callback: Optional callback for progress updates

        Returns:
            Dict containing training results and performance metrics
        """
        logger.info(f"Starting RL training with {self.config.algorithm} for {self.config.episodes} episodes")

        try:
            # Create algorithm configuration
            config = self.create_algorithm_config()

            # Update environment config with training data
            env_config = config.env_config.copy()
            env_config["data"] = training_data
            config = config.environment(TradingEnvironment, env_config=env_config)

            # Create algorithm
            if self.config.algorithm == "PPO":
                self.algorithm = config.build()
            elif self.config.algorithm == "DQN":
                self.algorithm = config.build()
            elif self.config.algorithm == "A3C":
                self.algorithm = config.build()

            # Training loop
            self.training_results = []
            best_reward = float('-inf')

            for episode in range(self.config.episodes):
                # Train for one iteration
                result = self.algorithm.train()

                # Extract metrics
                episode_reward_mean = result.get("episode_reward_mean", 0)
                episode_len_mean = result.get("episode_len_mean", 0)

                # Track training progress
                training_info = {
                    'episode': episode,
                    'reward_mean': episode_reward_mean,
                    'episode_length': episode_len_mean,
                    'timesteps_total': result.get("timesteps_total", 0),
                    'time_total_s': result.get("time_total_s", 0)
                }

                self.training_results.append(training_info)

                # Save checkpoint if best performance
                if episode_reward_mean > best_reward:
                    best_reward = episode_reward_mean
                    self.checkpoint_path = self.algorithm.save(settings.MODEL_STORAGE_PATH)
                    logger.info(f"New best model saved at episode {episode}: reward = {episode_reward_mean:.4f}")

                # Progress callback
                if progress_callback:
                    progress_callback(episode, self.config.episodes, training_info)

                # Periodic logging
                if episode % 10 == 0:
                    logger.info(f"Episode {episode}: Reward = {episode_reward_mean:.4f}, Length = {episode_len_mean:.1f}")

            # Evaluate final performance
            final_performance = self._evaluate_strategy(training_data)

            return {
                'algorithm': self.config.algorithm,
                'episodes_trained': self.config.episodes,
                'best_reward': best_reward,
                'final_performance': final_performance.model_dump(),
                'training_results': self.training_results,
                'checkpoint_path': self.checkpoint_path,
                'config': self.config.model_dump()
            }

        except Exception as e:
            logger.error(f"Error during RL training: {e}")
            raise

    def _evaluate_strategy(self, test_data: pd.DataFrame) -> PerformanceMetrics:
        """Evaluate trained strategy on test data."""
        if not self.algorithm:
            return PerformanceMetrics()

        # Create test environment
        env_config = {
            "data": test_data,
            "initial_capital": 10000.0,
            "transaction_cost": 0.001,
            "max_position": 1.0,
            "lookback_window": 20
        }

        env = TradingEnvironment(env_config)
        obs, _ = env.reset()

        done = False
        while not done:
            action = self.algorithm.compute_single_action(obs)
            obs, _, done, _, _ = env.step(action)

        return env.calculate_performance()

    def predict_action(self, current_data: pd.DataFrame) -> float:
        """Generate trading action using trained strategy."""
        if not self.algorithm:
            logger.warning("No trained algorithm available for prediction")
            return 0.0

        try:
            # Create environment with current data
            env_config = {
                "data": current_data,
                "initial_capital": 10000.0,
                "transaction_cost": 0.001,
                "max_position": 1.0,
                "lookback_window": 20
            }

            env = TradingEnvironment(env_config)
            obs, _ = env.reset()

            # Get action from trained policy
            action = self.algorithm.compute_single_action(obs)

            # Return normalized action
            return float(np.clip(action[0], -1.0, 1.0))

        except Exception as e:
            logger.error(f"Error generating action: {e}")
            return 0.0

    def save_strategy(self, strategy_data: Dict[str, Any], filepath: str):
        """Save trained strategy to file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save algorithm checkpoint
        if self.algorithm:
            checkpoint_path = self.algorithm.save(os.path.dirname(filepath))
        else:
            checkpoint_path = self.checkpoint_path

        # Save strategy metadata
        with open(filepath, 'wb') as f:
            pickle.dump({
                'strategy_data': strategy_data,
                'config': self.config,
                'checkpoint_path': checkpoint_path,
                'training_results': self.training_results
            }, f)

        logger.info(f"RL strategy saved to {filepath}")

    def load_strategy(self, filepath: str) -> Dict[str, Any]:
        """Load trained strategy from file."""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        self.config = data['config']
        self.checkpoint_path = data['checkpoint_path']
        self.training_results = data.get('training_results', [])

        # Restore algorithm from checkpoint
        if self.checkpoint_path and os.path.exists(self.checkpoint_path):
            config = self.create_algorithm_config()
            if self.config.algorithm == "PPO":
                self.algorithm = config.build()
            elif self.config.algorithm == "DQN":
                self.algorithm = config.build()
            elif self.config.algorithm == "A3C":
                self.algorithm = config.build()

            self.algorithm.restore(self.checkpoint_path)

        logger.info(f"RL strategy loaded from {filepath}")
        return data['strategy_data']
