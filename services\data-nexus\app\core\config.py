"""
Configuration settings for Data Nexus service.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "AthenaTrader Data Nexus"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Database
    DATABASE_URL: str = "postgresql://athena_user:athena_password@localhost:5432/athena_trader"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # API Keys
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    
    # Data ingestion settings
    DATA_INGESTION_INTERVAL: int = 60  # seconds
    MAX_CONCURRENT_REQUESTS: int = 10
    REQUEST_TIMEOUT: int = 30  # seconds
    
    # Data retention settings
    HOT_DATA_RETENTION_DAYS: int = 30
    WARM_DATA_RETENTION_DAYS: int = 365
    COLD_DATA_RETENTION_YEARS: int = 7
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
