"""
Multi-strategy portfolio optimization engine with sophisticated optimization methods.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from decimal import Decimal
import cvxpy as cp
from scipy import optimize
from sklearn.covariance import LedoitWolf, OAS
import warnings

from app.core.config import settings
from app.schemas.portfolio import OptimizationMethod, ObjectiveFunction, OptimizationConfig, PortfolioConstraints

logger = logging.getLogger(__name__)

# Suppress optimization warnings
warnings.filterwarnings('ignore', category=UserWarning)


class CovarianceEstimator:
    """Advanced covariance matrix estimation with shrinkage."""
    
    def __init__(self, method: str = "ledoit_wolf"):
        """Initialize covariance estimator."""
        self.method = method
    
    def estimate(self, returns: pd.DataFrame, **kwargs) -> np.ndarray:
        """Estimate covariance matrix with shrinkage."""
        if self.method == "ledoit_wolf":
            estimator = LedoitWolf()
        elif self.method == "oas":
            estimator = OAS()
        else:
            # Sample covariance
            return returns.cov().values
        
        cov_matrix, _ = estimator.fit(returns.values).covariance_, estimator.shrinkage_
        return cov_matrix
    
    def estimate_with_shrinkage(
        self,
        returns: pd.DataFrame,
        shrinkage_target: str = "diagonal",
        shrinkage_intensity: Optional[float] = None
    ) -> np.ndarray:
        """Estimate covariance with custom shrinkage target."""
        sample_cov = returns.cov().values
        
        if shrinkage_target == "diagonal":
            target = np.diag(np.diag(sample_cov))
        elif shrinkage_target == "identity":
            target = np.eye(len(sample_cov)) * np.trace(sample_cov) / len(sample_cov)
        elif shrinkage_target == "constant_correlation":
            # Constant correlation model
            std_devs = np.sqrt(np.diag(sample_cov))
            avg_corr = (np.sum(sample_cov / np.outer(std_devs, std_devs)) - len(sample_cov)) / (len(sample_cov) * (len(sample_cov) - 1))
            target = avg_corr * np.outer(std_devs, std_devs)
            np.fill_diagonal(target, np.diag(sample_cov))
        else:
            target = sample_cov
        
        if shrinkage_intensity is None:
            # Optimal shrinkage intensity (Ledoit-Wolf)
            estimator = LedoitWolf()
            estimator.fit(returns.values)
            shrinkage_intensity = estimator.shrinkage_
        
        shrunk_cov = (1 - shrinkage_intensity) * sample_cov + shrinkage_intensity * target
        return shrunk_cov


class MeanVarianceOptimizer:
    """Mean-variance optimization using Modern Portfolio Theory."""
    
    def __init__(self, config: OptimizationConfig):
        """Initialize mean-variance optimizer."""
        self.config = config
        self.cov_estimator = CovarianceEstimator()
    
    def optimize(
        self,
        expected_returns: np.ndarray,
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints,
        objective: ObjectiveFunction = ObjectiveFunction.MAX_SHARPE
    ) -> Dict[str, Any]:
        """Perform mean-variance optimization."""
        n_assets = len(expected_returns)
        
        # Decision variables
        weights = cp.Variable(n_assets)
        
        # Portfolio return and risk
        portfolio_return = expected_returns.T @ weights
        portfolio_variance = cp.quad_form(weights, covariance_matrix)
        portfolio_volatility = cp.sqrt(portfolio_variance)
        
        # Constraints
        constraints_list = [
            cp.sum(weights) == 1,  # Weights sum to 1
            weights >= float(constraints.min_weight),  # Minimum weight
            weights <= float(constraints.max_weight),  # Maximum weight
        ]
        
        # Additional constraints
        if constraints.max_volatility:
            constraints_list.append(portfolio_volatility <= float(constraints.max_volatility))
        
        # Objective function
        if objective == ObjectiveFunction.MAX_SHARPE:
            # Maximize Sharpe ratio (equivalent to maximizing return/risk)
            risk_free_rate = float(self.config.risk_free_rate)
            objective_func = cp.Maximize((portfolio_return - risk_free_rate) / portfolio_volatility)
        elif objective == ObjectiveFunction.MIN_VARIANCE:
            objective_func = cp.Minimize(portfolio_variance)
        elif objective == ObjectiveFunction.MAX_RETURN:
            objective_func = cp.Maximize(portfolio_return)
        elif objective == ObjectiveFunction.TARGET_RETURN:
            target_return = float(constraints.custom_constraints.get("target_return", 0.08))
            constraints_list.append(portfolio_return >= target_return)
            objective_func = cp.Minimize(portfolio_variance)
        elif objective == ObjectiveFunction.TARGET_VOLATILITY:
            target_volatility = float(constraints.custom_constraints.get("target_volatility", 0.15))
            constraints_list.append(portfolio_volatility <= target_volatility)
            objective_func = cp.Maximize(portfolio_return)
        else:
            objective_func = cp.Maximize(portfolio_return - 0.5 * portfolio_variance)
        
        # Solve optimization problem
        problem = cp.Problem(objective_func, constraints_list)
        
        try:
            problem.solve(
                solver=getattr(cp, self.config.solver, cp.ECOS),
                verbose=False,
                max_iters=self.config.max_iterations,
                abstol=float(self.config.solver_tolerance),
                reltol=float(self.config.solver_tolerance)
            )
            
            if problem.status not in ["infeasible", "unbounded"]:
                optimal_weights = weights.value
                if optimal_weights is not None:
                    # Ensure weights are valid
                    optimal_weights = np.maximum(optimal_weights, 0)
                    optimal_weights = optimal_weights / np.sum(optimal_weights)
                    
                    # Calculate portfolio metrics
                    portfolio_ret = np.dot(expected_returns, optimal_weights)
                    portfolio_vol = np.sqrt(np.dot(optimal_weights, np.dot(covariance_matrix, optimal_weights)))
                    sharpe_ratio = (portfolio_ret - float(self.config.risk_free_rate)) / portfolio_vol if portfolio_vol > 0 else 0
                    
                    return {
                        "status": "optimal",
                        "weights": optimal_weights,
                        "expected_return": portfolio_ret,
                        "expected_volatility": portfolio_vol,
                        "expected_sharpe": sharpe_ratio,
                        "solver_status": problem.status,
                        "solver_iterations": getattr(problem, "solver_stats", {}).get("num_iters", None)
                    }
            
            return {
                "status": "failed",
                "error": f"Optimization failed with status: {problem.status}",
                "solver_status": problem.status
            }
            
        except Exception as e:
            logger.error(f"Mean-variance optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }


class RiskParityOptimizer:
    """Risk parity optimization for equal risk contribution."""
    
    def __init__(self, config: OptimizationConfig):
        """Initialize risk parity optimizer."""
        self.config = config
    
    def optimize(
        self,
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints,
        risk_budgets: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """Perform risk parity optimization."""
        n_assets = len(covariance_matrix)
        
        if risk_budgets is None:
            risk_budgets = np.ones(n_assets) / n_assets  # Equal risk budgets
        
        # Initial guess - equal weights
        x0 = np.ones(n_assets) / n_assets
        
        # Bounds
        bounds = [(float(constraints.min_weight), float(constraints.max_weight)) for _ in range(n_assets)]
        
        # Constraints
        constraints_list = [
            {"type": "eq", "fun": lambda x: np.sum(x) - 1.0}  # Weights sum to 1
        ]
        
        try:
            # Optimize using sequential least squares programming
            result = optimize.minimize(
                fun=self._risk_parity_objective,
                x0=x0,
                args=(covariance_matrix, risk_budgets),
                method="SLSQP",
                bounds=bounds,
                constraints=constraints_list,
                options={
                    "maxiter": self.config.rp_max_iterations or 1000,
                    "ftol": float(self.config.rp_tolerance or 0.01)
                }
            )
            
            if result.success:
                optimal_weights = result.x
                optimal_weights = np.maximum(optimal_weights, 0)
                optimal_weights = optimal_weights / np.sum(optimal_weights)
                
                # Calculate portfolio metrics
                portfolio_vol = np.sqrt(np.dot(optimal_weights, np.dot(covariance_matrix, optimal_weights)))
                risk_contributions = self._calculate_risk_contributions(optimal_weights, covariance_matrix)
                
                return {
                    "status": "optimal",
                    "weights": optimal_weights,
                    "expected_volatility": portfolio_vol,
                    "risk_contributions": risk_contributions,
                    "solver_status": "success",
                    "solver_iterations": result.nit,
                    "objective_value": result.fun
                }
            else:
                return {
                    "status": "failed",
                    "error": f"Risk parity optimization failed: {result.message}",
                    "solver_status": "failed"
                }
                
        except Exception as e:
            logger.error(f"Risk parity optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _risk_parity_objective(self, weights: np.ndarray, cov_matrix: np.ndarray, risk_budgets: np.ndarray) -> float:
        """Risk parity objective function."""
        portfolio_variance = np.dot(weights, np.dot(cov_matrix, weights))
        
        if portfolio_variance <= 0:
            return 1e6  # Large penalty for invalid portfolio
        
        # Calculate marginal risk contributions
        marginal_contrib = np.dot(cov_matrix, weights)
        risk_contrib = weights * marginal_contrib / portfolio_variance
        
        # Objective: minimize sum of squared deviations from target risk budgets
        objective = np.sum((risk_contrib - risk_budgets) ** 2)
        
        return objective
    
    def _calculate_risk_contributions(self, weights: np.ndarray, cov_matrix: np.ndarray) -> np.ndarray:
        """Calculate risk contributions for each asset."""
        portfolio_variance = np.dot(weights, np.dot(cov_matrix, weights))
        
        if portfolio_variance <= 0:
            return np.zeros_like(weights)
        
        marginal_contrib = np.dot(cov_matrix, weights)
        risk_contrib = weights * marginal_contrib / portfolio_variance
        
        return risk_contrib


class BlackLittermanOptimizer:
    """Black-Litterman model for incorporating market views."""
    
    def __init__(self, config: OptimizationConfig):
        """Initialize Black-Litterman optimizer."""
        self.config = config
        self.mv_optimizer = MeanVarianceOptimizer(config)
    
    def optimize(
        self,
        market_caps: np.ndarray,
        covariance_matrix: np.ndarray,
        views: Dict[str, Any],
        constraints: PortfolioConstraints
    ) -> Dict[str, Any]:
        """Perform Black-Litterman optimization."""
        try:
            # Calculate implied equilibrium returns
            risk_aversion = self._estimate_risk_aversion(market_caps, covariance_matrix)
            market_weights = market_caps / np.sum(market_caps)
            implied_returns = risk_aversion * np.dot(covariance_matrix, market_weights)
            
            # Process views
            P, Q, Omega = self._process_views(views, len(market_caps))
            
            if P is None or Q is None:
                # No views provided, use implied returns
                bl_returns = implied_returns
            else:
                # Black-Litterman formula
                tau = float(self.config.bl_tau or 0.025)
                
                # Calculate Black-Litterman returns
                M1 = np.linalg.inv(tau * covariance_matrix)
                M2 = np.dot(P.T, np.dot(np.linalg.inv(Omega), P))
                M3 = np.dot(np.linalg.inv(tau * covariance_matrix), implied_returns)
                M4 = np.dot(P.T, np.dot(np.linalg.inv(Omega), Q))
                
                bl_returns = np.dot(np.linalg.inv(M1 + M2), M3 + M4)
                
                # Update covariance matrix
                bl_covariance = np.linalg.inv(M1 + M2)
            
            # Optimize using Black-Litterman inputs
            result = self.mv_optimizer.optimize(
                bl_returns,
                bl_covariance if 'bl_covariance' in locals() else covariance_matrix,
                constraints,
                ObjectiveFunction.MAX_SHARPE
            )
            
            if result["status"] == "optimal":
                result["bl_returns"] = bl_returns
                result["implied_returns"] = implied_returns
                result["risk_aversion"] = risk_aversion
            
            return result
            
        except Exception as e:
            logger.error(f"Black-Litterman optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _estimate_risk_aversion(self, market_caps: np.ndarray, cov_matrix: np.ndarray) -> float:
        """Estimate market risk aversion parameter."""
        market_weights = market_caps / np.sum(market_caps)
        market_variance = np.dot(market_weights, np.dot(cov_matrix, market_weights))
        market_return = float(settings.MARKET_RETURN)
        risk_free_rate = float(self.config.risk_free_rate)
        
        # Risk aversion = (market_return - risk_free_rate) / market_variance
        risk_aversion = (market_return - risk_free_rate) / market_variance
        
        return max(risk_aversion, 1.0)  # Ensure positive risk aversion
    
    def _process_views(self, views: Dict[str, Any], n_assets: int) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]]:
        """Process market views into Black-Litterman format."""
        if not views or "views" not in views:
            return None, None, None
        
        view_list = views["views"]
        n_views = len(view_list)
        
        if n_views == 0:
            return None, None, None
        
        # Initialize matrices
        P = np.zeros((n_views, n_assets))  # Picking matrix
        Q = np.zeros(n_views)  # View returns
        Omega = np.eye(n_views)  # View uncertainty matrix
        
        for i, view in enumerate(view_list):
            # Process view specification
            if "assets" in view and "return" in view:
                asset_indices = view["assets"]
                view_return = view["return"]
                view_confidence = view.get("confidence", float(self.config.bl_confidence or 0.95))
                
                # Set picking matrix
                for asset_idx in asset_indices:
                    if 0 <= asset_idx < n_assets:
                        P[i, asset_idx] = 1.0 / len(asset_indices)  # Equal weight view
                
                # Set view return
                Q[i] = view_return
                
                # Set view uncertainty (lower confidence = higher uncertainty)
                view_uncertainty = (1 - view_confidence) * abs(view_return)
                Omega[i, i] = max(view_uncertainty, 0.001)  # Minimum uncertainty
        
        return P, Q, Omega


class HierarchicalRiskParityOptimizer:
    """Hierarchical Risk Parity using machine learning clustering."""
    
    def __init__(self, config: OptimizationConfig):
        """Initialize HRP optimizer."""
        self.config = config
    
    def optimize(
        self,
        covariance_matrix: np.ndarray,
        constraints: PortfolioConstraints
    ) -> Dict[str, Any]:
        """Perform Hierarchical Risk Parity optimization."""
        try:
            from scipy.cluster.hierarchy import linkage, dendrogram, cut_tree
            from scipy.spatial.distance import squareform
            
            # Convert covariance to correlation
            std_devs = np.sqrt(np.diag(covariance_matrix))
            correlation_matrix = covariance_matrix / np.outer(std_devs, std_devs)
            
            # Convert correlation to distance
            distance_matrix = np.sqrt(0.5 * (1 - correlation_matrix))
            
            # Hierarchical clustering
            condensed_distances = squareform(distance_matrix, checks=False)
            linkage_matrix = linkage(condensed_distances, method="ward")
            
            # Get cluster allocation
            weights = self._get_hrp_weights(linkage_matrix, covariance_matrix)
            
            # Apply constraints
            weights = np.maximum(weights, float(constraints.min_weight))
            weights = np.minimum(weights, float(constraints.max_weight))
            weights = weights / np.sum(weights)  # Renormalize
            
            # Calculate portfolio metrics
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(covariance_matrix, weights)))
            
            return {
                "status": "optimal",
                "weights": weights,
                "expected_volatility": portfolio_vol,
                "solver_status": "success",
                "linkage_matrix": linkage_matrix.tolist()
            }
            
        except Exception as e:
            logger.error(f"HRP optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _get_hrp_weights(self, linkage_matrix: np.ndarray, cov_matrix: np.ndarray) -> np.ndarray:
        """Calculate HRP weights using recursive bisection."""
        n_assets = len(cov_matrix)
        
        # Get sorted indices from clustering
        sorted_indices = self._get_quasi_diag(linkage_matrix)
        
        # Recursive bisection
        weights = pd.Series(1.0, index=sorted_indices)
        clustered_alphas = [sorted_indices]
        
        while len(clustered_alphas) > 0:
            clustered_alphas = [
                cluster[start:end] for cluster in clustered_alphas
                for start, end in ((0, len(cluster) // 2), (len(cluster) // 2, len(cluster)))
                if len(cluster) > 1
            ]
            
            for subcluster in clustered_alphas:
                if len(subcluster) > 1:
                    # Calculate cluster variance
                    left_cluster = subcluster[:len(subcluster)//2]
                    right_cluster = subcluster[len(subcluster)//2:]
                    
                    left_var = self._get_cluster_var(cov_matrix, left_cluster)
                    right_var = self._get_cluster_var(cov_matrix, right_cluster)
                    
                    # Allocate weight inversely proportional to variance
                    total_var = left_var + right_var
                    if total_var > 0:
                        left_weight = right_var / total_var
                        right_weight = left_var / total_var
                    else:
                        left_weight = right_weight = 0.5
                    
                    # Update weights
                    weights[left_cluster] *= left_weight
                    weights[right_cluster] *= right_weight
        
        return weights.values
    
    def _get_quasi_diag(self, linkage_matrix: np.ndarray) -> List[int]:
        """Get quasi-diagonal ordering from linkage matrix."""
        from scipy.cluster.hierarchy import dendrogram
        
        # Get dendrogram ordering
        dendro = dendrogram(linkage_matrix, no_plot=True)
        return dendro["leaves"]
    
    def _get_cluster_var(self, cov_matrix: np.ndarray, cluster_indices: List[int]) -> float:
        """Calculate cluster variance."""
        if len(cluster_indices) == 1:
            return cov_matrix[cluster_indices[0], cluster_indices[0]]
        
        # Equal weight within cluster
        cluster_weights = np.zeros(len(cov_matrix))
        for idx in cluster_indices:
            cluster_weights[idx] = 1.0 / len(cluster_indices)
        
        cluster_var = np.dot(cluster_weights, np.dot(cov_matrix, cluster_weights))
        return cluster_var


class PortfolioOptimizer:
    """Main portfolio optimization engine coordinating all methods."""
    
    def __init__(self):
        """Initialize portfolio optimizer."""
        self.optimizers = {}
    
    async def cleanup(self):
        """Cleanup optimizer resources."""
        pass
    
    def optimize_portfolio(
        self,
        method: OptimizationMethod,
        strategy_data: pd.DataFrame,
        config: OptimizationConfig,
        constraints: PortfolioConstraints,
        views: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Optimize portfolio using specified method."""
        
        try:
            # Prepare data
            returns = strategy_data.pct_change().dropna()
            
            if len(returns) < 30:
                return {
                    "status": "error",
                    "error": "Insufficient data for optimization (minimum 30 observations required)"
                }
            
            # Estimate covariance matrix
            cov_estimator = CovarianceEstimator()
            if config.enable_shrinkage:
                cov_matrix = cov_estimator.estimate_with_shrinkage(
                    returns,
                    config.shrinkage_target,
                    None
                )
            else:
                cov_matrix = returns.cov().values
            
            # Ensure positive definite
            cov_matrix = self._make_positive_definite(cov_matrix)
            
            # Route to appropriate optimizer
            if method == OptimizationMethod.MEAN_VARIANCE:
                expected_returns = returns.mean().values * settings.TRADING_DAYS_PER_YEAR
                optimizer = MeanVarianceOptimizer(config)
                result = optimizer.optimize(expected_returns, cov_matrix, constraints)
                
            elif method == OptimizationMethod.RISK_PARITY:
                optimizer = RiskParityOptimizer(config)
                result = optimizer.optimize(cov_matrix, constraints)
                
            elif method == OptimizationMethod.BLACK_LITTERMAN:
                # Use market cap weights as proxy (equal weights if not available)
                market_caps = np.ones(len(returns.columns))
                optimizer = BlackLittermanOptimizer(config)
                result = optimizer.optimize(market_caps, cov_matrix, views or {}, constraints)
                
            elif method == OptimizationMethod.HIERARCHICAL_RISK_PARITY:
                optimizer = HierarchicalRiskParityOptimizer(config)
                result = optimizer.optimize(cov_matrix, constraints)
                
            elif method == OptimizationMethod.MIN_VARIANCE:
                expected_returns = np.zeros(len(returns.columns))  # Don't care about returns
                optimizer = MeanVarianceOptimizer(config)
                result = optimizer.optimize(expected_returns, cov_matrix, constraints, ObjectiveFunction.MIN_VARIANCE)
                
            elif method == OptimizationMethod.MAX_SHARPE:
                expected_returns = returns.mean().values * settings.TRADING_DAYS_PER_YEAR
                optimizer = MeanVarianceOptimizer(config)
                result = optimizer.optimize(expected_returns, cov_matrix, constraints, ObjectiveFunction.MAX_SHARPE)
                
            else:
                return {
                    "status": "error",
                    "error": f"Unsupported optimization method: {method}"
                }
            
            # Add common metrics
            if result["status"] == "optimal":
                result["covariance_matrix"] = cov_matrix.tolist()
                result["strategy_names"] = returns.columns.tolist()
                result["optimization_method"] = method.value
                result["data_points"] = len(returns)
                
                # Calculate additional metrics
                weights = result["weights"]
                result["diversification_metrics"] = self._calculate_diversification_metrics(weights, cov_matrix)
            
            return result
            
        except Exception as e:
            logger.error(f"Portfolio optimization failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _make_positive_definite(self, matrix: np.ndarray, min_eigenvalue: float = 1e-8) -> np.ndarray:
        """Ensure matrix is positive definite."""
        eigenvalues, eigenvectors = np.linalg.eigh(matrix)
        eigenvalues = np.maximum(eigenvalues, min_eigenvalue)
        return eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
    
    def _calculate_diversification_metrics(self, weights: np.ndarray, cov_matrix: np.ndarray) -> Dict[str, float]:
        """Calculate portfolio diversification metrics."""
        # Effective number of strategies (inverse of Herfindahl index)
        herfindahl_index = np.sum(weights ** 2)
        effective_strategies = 1.0 / herfindahl_index if herfindahl_index > 0 else 0
        
        # Diversification ratio
        individual_vols = np.sqrt(np.diag(cov_matrix))
        weighted_avg_vol = np.dot(weights, individual_vols)
        portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
        diversification_ratio = weighted_avg_vol / portfolio_vol if portfolio_vol > 0 else 0
        
        # Average correlation
        std_devs = np.sqrt(np.diag(cov_matrix))
        correlation_matrix = cov_matrix / np.outer(std_devs, std_devs)
        
        # Weighted average correlation
        weight_products = np.outer(weights, weights)
        avg_correlation = np.sum(weight_products * correlation_matrix) - np.sum(weights ** 2)
        avg_correlation = avg_correlation / (1 - np.sum(weights ** 2)) if np.sum(weights ** 2) < 1 else 0
        
        return {
            "effective_strategies": effective_strategies,
            "concentration_index": herfindahl_index,
            "diversification_ratio": diversification_ratio,
            "correlation_avg": avg_correlation
        }
