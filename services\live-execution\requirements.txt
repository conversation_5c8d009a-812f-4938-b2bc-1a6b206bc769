# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Data processing and analysis
pandas==2.1.3
numpy==1.24.4
scipy==1.11.4

# HTTP client for broker APIs
httpx==0.25.2

# WebSocket support
websockets==12.0

# FIX protocol support
quickfix==1.15.1

# Configuration and environment
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# System monitoring
psutil==5.9.6

# Logging and utilities
structlog==23.2.0
rich==13.7.0
python-json-logger==2.0.7

# Redis for caching and pub/sub
redis==5.0.1

# Async task queue
celery==5.3.4

# Financial calculations
quantlib==1.32

# Time series analysis
pandas-ta==0.3.14b0

# Statistical analysis
statsmodels==0.14.0

# Machine learning for risk models
scikit-learn==1.3.2

# Cryptography for secure communications
cryptography==41.0.8

# JSON Web Tokens
pyjwt==2.8.0

# Email notifications
aiosmtplib==3.0.1

# Slack notifications
slack-sdk==3.26.1

# Market data providers
alpha-vantage==2.3.1
yfinance==0.2.28
polygon-api-client==1.12.3

# Interactive Brokers API
ib-insync==0.9.86

# Alpaca API
alpaca-trade-api==3.1.1

# Binance API
python-binance==1.0.19

# Coinbase API
coinbase==2.1.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance monitoring
prometheus-client==0.19.0

# Distributed tracing
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-instrumentation-httpx==0.42b0

# Circuit breaker pattern
pybreaker==1.0.1

# Rate limiting
slowapi==0.1.9

# Caching
cachetools==5.3.2

# Decimal arithmetic for financial calculations
decimal==1.70

# UUID utilities
shortuuid==1.0.11

# Date/time utilities
python-dateutil==2.8.2
pytz==2023.3

# Configuration validation
cerberus==1.3.5

# Health checks
healthcheck==1.3.3

# Metrics and monitoring
statsd==4.0.1

# Message queuing
pika==1.3.2

# Workflow orchestration
prefect==2.14.11

# Data validation
marshmallow==3.20.1

# API documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Load testing
locust==2.17.0
