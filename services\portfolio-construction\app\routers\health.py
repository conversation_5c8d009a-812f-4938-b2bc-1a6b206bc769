"""
Health check router for Portfolio Construction service.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import psutil
import os

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "AthenaTrader Portfolio Construction",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "capabilities": [
            "multi_strategy_optimization",
            "mean_variance_optimization",
            "risk_parity_allocation",
            "black_litterman_model",
            "hierarchical_risk_parity",
            "dynamic_rebalancing",
            "risk_management",
            "performance_attribution",
            "brinson_fachler_attribution",
            "factor_decomposition"
        ]
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """Detailed health check with system and dependency status."""
    health_status = {
        "status": "healthy",
        "service": "AthenaTrader Portfolio Construction",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database connectivity check
    try:
        await db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Check for resource warnings
        warnings = []
        if cpu_percent > 90:
            warnings.append("High CPU usage")
        if memory.percent > 90:
            warnings.append("High memory usage")
        if disk.percent > 90:
            warnings.append("Low disk space")
        
        if warnings:
            health_status["checks"]["system"]["warnings"] = warnings
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "error",
            "message": f"Failed to get system metrics: {str(e)}"
        }
    
    # Optimization engines check
    try:
        # Check if optimization libraries are available
        import cvxpy as cp
        import scipy
        import sklearn
        import numpy as np
        import pandas as pd
        
        # Check if results storage directory exists and is writable
        results_path = settings.RESULTS_STORAGE_PATH
        if os.path.exists(results_path) and os.access(results_path, os.W_OK):
            health_status["checks"]["optimization_engines"] = {
                "status": "healthy",
                "cvxpy_version": cp.__version__,
                "scipy_version": scipy.__version__,
                "sklearn_version": sklearn.__version__,
                "numpy_version": np.__version__,
                "pandas_version": pd.__version__,
                "results_storage": "accessible",
                "results_storage_path": results_path,
                "max_concurrent_optimizations": settings.MAX_CONCURRENT_OPTIMIZATIONS
            }
        else:
            health_status["checks"]["optimization_engines"] = {
                "status": "warning",
                "message": "Results storage not accessible",
                "results_storage_path": results_path
            }
    except ImportError as e:
        health_status["checks"]["optimization_engines"] = {
            "status": "error",
            "message": f"Optimization libraries not available: {str(e)}"
        }
    except Exception as e:
        health_status["checks"]["optimization_engines"] = {
            "status": "error",
            "message": f"Optimization engines check failed: {str(e)}"
        }
    
    # External services check
    try:
        import httpx
        external_services = {
            "data_nexus": settings.DATA_NEXUS_URL,
            "strategy_genesis": settings.STRATEGY_GENESIS_URL,
            "backtesting_engine": settings.BACKTESTING_ENGINE_URL
        }
        
        service_status = {}
        for service_name, service_url in external_services.items():
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{service_url}/health")
                    if response.status_code == 200:
                        service_status[service_name] = "healthy"
                    else:
                        service_status[service_name] = "unhealthy"
                        health_status["status"] = "degraded"
            except Exception:
                service_status[service_name] = "unreachable"
                health_status["status"] = "degraded"
        
        health_status["checks"]["external_services"] = {
            "status": "healthy" if all(s == "healthy" for s in service_status.values()) else "degraded",
            "services": service_status
        }
        
    except Exception as e:
        health_status["checks"]["external_services"] = {
            "status": "error",
            "message": f"External services check failed: {str(e)}"
        }
    
    return health_status


@router.get("/engines")
async def engines_status():
    """Check status of portfolio construction engines."""
    engines_status = {
        "timestamp": datetime.utcnow().isoformat(),
        "engines": {}
    }
    
    # Portfolio Optimization Engine
    try:
        from app.engine.optimization import PortfolioOptimizer
        engines_status["engines"]["portfolio_optimizer"] = {
            "status": "available",
            "supported_methods": [
                "MEAN_VARIANCE",
                "RISK_PARITY", 
                "BLACK_LITTERMAN",
                "MIN_VARIANCE",
                "MAX_SHARPE",
                "HIERARCHICAL_RISK_PARITY"
            ],
            "config": {
                "default_method": settings.DEFAULT_OPTIMIZATION_METHOD,
                "risk_free_rate": float(settings.RISK_FREE_RATE),
                "max_portfolio_volatility": float(settings.MAX_PORTFOLIO_VOLATILITY),
                "min_diversification": settings.MIN_DIVERSIFICATION
            }
        }
    except Exception as e:
        engines_status["engines"]["portfolio_optimizer"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Rebalancing Engine
    try:
        from app.engine.rebalancing import RebalancingEngine
        engines_status["engines"]["rebalancing_engine"] = {
            "status": "available",
            "config": {
                "rebalancing_frequency": settings.REBALANCING_FREQUENCY,
                "drift_threshold": float(settings.DRIFT_THRESHOLD),
                "volatility_threshold": float(settings.VOLATILITY_THRESHOLD),
                "transaction_cost_bps": float(settings.TRANSACTION_COST_BPS)
            }
        }
    except Exception as e:
        engines_status["engines"]["rebalancing_engine"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Risk Management Engine
    try:
        from app.engine.risk_management import RiskManager
        engines_status["engines"]["risk_manager"] = {
            "status": "available",
            "config": {
                "max_portfolio_volatility": float(settings.MAX_PORTFOLIO_VOLATILITY),
                "max_drawdown_limit": float(settings.MAX_DRAWDOWN_LIMIT),
                "max_correlation_threshold": float(settings.MAX_CORRELATION_THRESHOLD),
                "sector_limits": {k: float(v) for k, v in settings.SECTOR_LIMITS.items()},
                "strategy_type_limits": {k: float(v) for k, v in settings.STRATEGY_TYPE_LIMITS.items()}
            }
        }
    except Exception as e:
        engines_status["engines"]["risk_manager"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Performance Attribution Engine
    try:
        from app.engine.attribution import PerformanceAttributor
        engines_status["engines"]["performance_attributor"] = {
            "status": "available",
            "config": {
                "attribution_lookback_days": settings.ATTRIBUTION_LOOKBACK_DAYS,
                "enable_factor_attribution": settings.ENABLE_FACTOR_ATTRIBUTION,
                "factor_models": settings.FACTOR_MODELS
            }
        }
    except Exception as e:
        engines_status["engines"]["performance_attributor"] = {
            "status": "error",
            "message": str(e)
        }
    
    return engines_status


@router.get("/metrics")
async def get_metrics(db: AsyncSession = Depends(get_db)):
    """Get service metrics and statistics."""
    try:
        from app.models.portfolio import Portfolio, OptimizationRun, RebalancingEvent
        from sqlalchemy import func, select
        from datetime import timedelta
        
        # Get portfolio counts by status
        portfolio_counts = await db.execute(
            select(Portfolio.status, func.count(Portfolio.id))
            .group_by(Portfolio.status)
        )
        
        # Get recent activity
        recent_portfolios = await db.execute(
            select(func.count(Portfolio.id))
            .where(Portfolio.created_at >= datetime.utcnow() - timedelta(days=7))
        )
        
        # Get optimization statistics
        optimization_counts = await db.execute(
            select(OptimizationRun.status, func.count(OptimizationRun.id))
            .group_by(OptimizationRun.status)
        )
        
        # Get rebalancing statistics
        rebalancing_counts = await db.execute(
            select(RebalancingEvent.trigger_type, func.count(RebalancingEvent.id))
            .group_by(RebalancingEvent.trigger_type)
        )
        
        # Get performance statistics
        avg_performance = await db.execute(
            select(
                func.avg(Portfolio.total_return),
                func.avg(Portfolio.sharpe_ratio),
                func.avg(Portfolio.max_drawdown)
            )
            .where(Portfolio.status == "ACTIVE")
        )
        
        avg_stats = avg_performance.fetchone()
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "portfolios": {
                "by_status": dict(portfolio_counts.fetchall()),
                "recent_count": recent_portfolios.scalar() or 0
            },
            "optimizations": {
                "by_status": dict(optimization_counts.fetchall())
            },
            "rebalancing": {
                "by_trigger": dict(rebalancing_counts.fetchall())
            },
            "performance": {
                "avg_total_return": float(avg_stats[0]) if avg_stats[0] else None,
                "avg_sharpe_ratio": float(avg_stats[1]) if avg_stats[1] else None,
                "avg_max_drawdown": float(avg_stats[2]) if avg_stats[2] else None
            },
            "system": {
                "uptime_seconds": psutil.boot_time(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "max_concurrent_optimizations": settings.MAX_CONCURRENT_OPTIMIZATIONS
            },
            "configuration": {
                "default_optimization_method": settings.DEFAULT_OPTIMIZATION_METHOD,
                "risk_free_rate": float(settings.RISK_FREE_RATE),
                "max_portfolio_volatility": float(settings.MAX_PORTFOLIO_VOLATILITY),
                "rebalancing_frequency": settings.REBALANCING_FREQUENCY,
                "min_diversification": settings.MIN_DIVERSIFICATION,
                "max_strategies": settings.MAX_STRATEGIES
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )
