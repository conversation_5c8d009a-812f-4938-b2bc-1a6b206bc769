"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from main import app

client = TestClient(app)


def test_health_check():
    """Test basic health check endpoint."""
    response = client.get("/health/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert data["service"] == "api-gateway"
    assert data["version"] == "0.1.0"


def test_liveness_check():
    """Test liveness probe endpoint."""
    response = client.get("/health/live")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "alive"
    assert "timestamp" in data


@patch("app.routers.health.get_db")
def test_readiness_check_healthy(mock_get_db):
    """Test readiness check when all dependencies are healthy."""
    # Mock database session
    mock_db = AsyncMock()
    mock_db.execute.return_value.scalar.return_value = 1
    mock_get_db.return_value = mock_db
    
    response = client.get("/health/ready")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "ready"
    assert "checks" in data
    assert data["checks"]["database"]["status"] == "healthy"


def test_root_endpoint():
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    
    data = response.json()
    assert data["message"] == "AthenaTrader API Gateway"
    assert data["version"] == "0.1.0"
    assert data["status"] == "operational"


def test_info_endpoint():
    """Test info endpoint."""
    response = client.get("/info")
    assert response.status_code == 200
    
    data = response.json()
    assert data["name"] == "AthenaTrader API Gateway"
    assert data["version"] == "0.1.0"
    assert "services" in data
    assert "data_nexus" in data["services"]
