"""
Dynamic rebalancing framework for portfolio management.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum

from app.core.config import settings
from app.schemas.portfolio import TriggerType, RebalancingFrequency

logger = logging.getLogger(__name__)


class RebalancingTrigger(Enum):
    """Rebalancing trigger types."""
    SCHEDULED = "SCHEDULED"
    DRIFT = "DRIFT"
    VOLATILITY = "VOLATILITY"
    RISK_LIMIT = "RISK_LIMIT"
    CORRELATION = "CORRELATION"
    DRAWDOWN = "DRAWDOWN"
    MANUAL = "MANUAL"


class RebalancingDecision:
    """Rebalancing decision with rationale."""
    
    def __init__(
        self,
        should_rebalance: bool,
        trigger_type: RebalancingTrigger,
        trigger_value: float,
        threshold: float,
        urgency: str = "NORMAL",
        reason: str = "",
        recommended_method: str = "IMMEDIATE"
    ):
        self.should_rebalance = should_rebalance
        self.trigger_type = trigger_type
        self.trigger_value = trigger_value
        self.threshold = threshold
        self.urgency = urgency  # LOW, NORMAL, HIGH, CRITICAL
        self.reason = reason
        self.recommended_method = recommended_method
        self.timestamp = datetime.utcnow()


class DriftAnalyzer:
    """Analyze allocation drift from target weights."""
    
    def __init__(self, drift_threshold: float = 0.05):
        """Initialize drift analyzer."""
        self.drift_threshold = drift_threshold
    
    def analyze_drift(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Analyze allocation drift."""
        
        # Calculate individual drifts
        drifts = {}
        max_drift = 0.0
        total_absolute_drift = 0.0
        
        all_strategies = set(current_weights.keys()) | set(target_weights.keys())
        
        for strategy in all_strategies:
            current = current_weights.get(strategy, 0.0)
            target = target_weights.get(strategy, 0.0)
            drift = abs(current - target)
            
            drifts[strategy] = {
                "current_weight": current,
                "target_weight": target,
                "absolute_drift": drift,
                "relative_drift": drift / target if target > 0 else float('inf') if current > 0 else 0
            }
            
            max_drift = max(max_drift, drift)
            total_absolute_drift += drift
        
        # Calculate portfolio-level drift metrics
        tracking_error = np.sqrt(sum(d["absolute_drift"] ** 2 for d in drifts.values()))
        
        # Determine if rebalancing is needed
        needs_rebalancing = (
            max_drift > self.drift_threshold or
            total_absolute_drift > self.drift_threshold * 2 or
            tracking_error > self.drift_threshold * 1.5
        )
        
        analysis = {
            "needs_rebalancing": needs_rebalancing,
            "max_drift": max_drift,
            "total_absolute_drift": total_absolute_drift,
            "tracking_error": tracking_error,
            "drift_threshold": self.drift_threshold,
            "strategy_drifts": drifts,
            "strategies_over_threshold": [
                strategy for strategy, data in drifts.items()
                if data["absolute_drift"] > self.drift_threshold
            ]
        }
        
        return needs_rebalancing, analysis


class VolatilityMonitor:
    """Monitor portfolio volatility changes."""
    
    def __init__(self, volatility_threshold: float = 0.02, lookback_window: int = 20):
        """Initialize volatility monitor."""
        self.volatility_threshold = volatility_threshold
        self.lookback_window = lookback_window
    
    def analyze_volatility_regime(
        self,
        returns: pd.Series,
        current_volatility: float,
        target_volatility: Optional[float] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Analyze volatility regime changes."""
        
        if len(returns) < self.lookback_window:
            return False, {"error": "Insufficient data for volatility analysis"}
        
        # Calculate rolling volatility
        rolling_vol = returns.rolling(window=self.lookback_window).std() * np.sqrt(252)
        
        # Recent volatility statistics
        recent_vol = rolling_vol.iloc[-1]
        vol_trend = rolling_vol.iloc[-5:].mean() - rolling_vol.iloc[-10:-5].mean()
        vol_percentile = (rolling_vol.iloc[-1] > rolling_vol).mean()
        
        # Volatility regime detection
        vol_change = abs(recent_vol - current_volatility) / current_volatility if current_volatility > 0 else 0
        
        # Check against target volatility if provided
        target_breach = False
        if target_volatility:
            target_breach = recent_vol > target_volatility * 1.2  # 20% buffer
        
        # Determine if rebalancing is needed
        needs_rebalancing = (
            vol_change > self.volatility_threshold or
            target_breach or
            vol_percentile > 0.95 or  # Extreme volatility
            vol_percentile < 0.05     # Extremely low volatility
        )
        
        analysis = {
            "needs_rebalancing": needs_rebalancing,
            "current_volatility": current_volatility,
            "recent_volatility": recent_vol,
            "volatility_change": vol_change,
            "volatility_trend": vol_trend,
            "volatility_percentile": vol_percentile,
            "target_volatility": target_volatility,
            "target_breach": target_breach,
            "volatility_threshold": self.volatility_threshold
        }
        
        return needs_rebalancing, analysis


class RiskLimitMonitor:
    """Monitor portfolio risk limits."""
    
    def __init__(self):
        """Initialize risk limit monitor."""
        self.max_drawdown_limit = float(settings.MAX_DRAWDOWN_LIMIT)
        self.max_volatility_limit = float(settings.MAX_PORTFOLIO_VOLATILITY)
        self.max_correlation_limit = float(settings.MAX_CORRELATION_THRESHOLD)
    
    def check_risk_limits(
        self,
        portfolio_metrics: Dict[str, Any],
        correlation_matrix: Optional[np.ndarray] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Check if portfolio breaches risk limits."""
        
        violations = []
        
        # Drawdown limit
        current_drawdown = portfolio_metrics.get("max_drawdown", 0)
        if current_drawdown > self.max_drawdown_limit:
            violations.append({
                "type": "MAX_DRAWDOWN",
                "current": current_drawdown,
                "limit": self.max_drawdown_limit,
                "severity": "HIGH" if current_drawdown > self.max_drawdown_limit * 1.5 else "MEDIUM"
            })
        
        # Volatility limit
        current_volatility = portfolio_metrics.get("volatility", 0)
        if current_volatility > self.max_volatility_limit:
            violations.append({
                "type": "MAX_VOLATILITY",
                "current": current_volatility,
                "limit": self.max_volatility_limit,
                "severity": "MEDIUM"
            })
        
        # Correlation limit
        if correlation_matrix is not None:
            max_correlation = self._calculate_max_correlation(correlation_matrix)
            if max_correlation > self.max_correlation_limit:
                violations.append({
                    "type": "MAX_CORRELATION",
                    "current": max_correlation,
                    "limit": self.max_correlation_limit,
                    "severity": "LOW"
                })
        
        # VaR limit (if available)
        var_95 = portfolio_metrics.get("var_95")
        if var_95 and abs(var_95) > 0.05:  # 5% daily VaR limit
            violations.append({
                "type": "VAR_LIMIT",
                "current": abs(var_95),
                "limit": 0.05,
                "severity": "HIGH"
            })
        
        needs_rebalancing = len(violations) > 0
        
        analysis = {
            "needs_rebalancing": needs_rebalancing,
            "violations": violations,
            "risk_limits": {
                "max_drawdown": self.max_drawdown_limit,
                "max_volatility": self.max_volatility_limit,
                "max_correlation": self.max_correlation_limit
            }
        }
        
        return needs_rebalancing, analysis
    
    def _calculate_max_correlation(self, correlation_matrix: np.ndarray) -> float:
        """Calculate maximum pairwise correlation."""
        # Set diagonal to -1 to ignore self-correlation
        corr_copy = correlation_matrix.copy()
        np.fill_diagonal(corr_copy, -1)
        return np.max(corr_copy)


class TransactionCostEstimator:
    """Estimate transaction costs for rebalancing."""
    
    def __init__(self, base_cost_bps: float = 5.0):
        """Initialize transaction cost estimator."""
        self.base_cost_bps = base_cost_bps
    
    def estimate_rebalancing_costs(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        portfolio_value: float,
        strategy_liquidity: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """Estimate costs of rebalancing to target weights."""
        
        # Calculate turnover
        turnover = 0.0
        trade_costs = {}
        
        all_strategies = set(current_weights.keys()) | set(target_weights.keys())
        
        for strategy in all_strategies:
            current = current_weights.get(strategy, 0.0)
            target = target_weights.get(strategy, 0.0)
            trade_amount = abs(target - current) * portfolio_value
            
            # Adjust cost based on liquidity
            liquidity_multiplier = 1.0
            if strategy_liquidity and strategy in strategy_liquidity:
                # Higher cost for less liquid strategies
                liquidity_score = strategy_liquidity[strategy]
                liquidity_multiplier = 2.0 - liquidity_score  # Range: 1.0 to 2.0
            
            strategy_cost_bps = self.base_cost_bps * liquidity_multiplier
            strategy_cost = trade_amount * strategy_cost_bps / 10000
            
            trade_costs[strategy] = {
                "trade_amount": trade_amount,
                "cost_bps": strategy_cost_bps,
                "cost_dollars": strategy_cost,
                "liquidity_multiplier": liquidity_multiplier
            }
            
            turnover += abs(target - current)
        
        total_cost = sum(cost["cost_dollars"] for cost in trade_costs.values())
        total_cost_bps = (total_cost / portfolio_value) * 10000 if portfolio_value > 0 else 0
        
        return {
            "total_cost_dollars": total_cost,
            "total_cost_bps": total_cost_bps,
            "turnover": turnover,
            "trade_costs": trade_costs,
            "cost_efficiency": total_cost_bps / (turnover * 10000) if turnover > 0 else 0
        }


class RebalancingScheduler:
    """Schedule rebalancing based on frequency and conditions."""
    
    def __init__(self):
        """Initialize rebalancing scheduler."""
        pass
    
    def should_rebalance_scheduled(
        self,
        last_rebalance: datetime,
        frequency: RebalancingFrequency,
        current_time: Optional[datetime] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Check if scheduled rebalancing is due."""
        
        if current_time is None:
            current_time = datetime.utcnow()
        
        time_since_last = current_time - last_rebalance
        
        # Define rebalancing intervals
        intervals = {
            RebalancingFrequency.DAILY: timedelta(days=1),
            RebalancingFrequency.WEEKLY: timedelta(weeks=1),
            RebalancingFrequency.MONTHLY: timedelta(days=30),
            RebalancingFrequency.QUARTERLY: timedelta(days=90)
        }
        
        required_interval = intervals.get(frequency, timedelta(weeks=1))
        is_due = time_since_last >= required_interval
        
        # Calculate next scheduled rebalancing
        next_rebalance = last_rebalance + required_interval
        
        analysis = {
            "is_due": is_due,
            "last_rebalance": last_rebalance,
            "time_since_last": time_since_last.total_seconds() / 3600,  # hours
            "required_interval_hours": required_interval.total_seconds() / 3600,
            "next_scheduled": next_rebalance,
            "frequency": frequency.value
        }
        
        return is_due, analysis


class RebalancingEngine:
    """Main rebalancing engine coordinating all components."""
    
    def __init__(self):
        """Initialize rebalancing engine."""
        self.drift_analyzer = DriftAnalyzer()
        self.volatility_monitor = VolatilityMonitor()
        self.risk_monitor = RiskLimitMonitor()
        self.cost_estimator = TransactionCostEstimator()
        self.scheduler = RebalancingScheduler()
    
    async def cleanup(self):
        """Cleanup rebalancing engine resources."""
        pass
    
    def evaluate_rebalancing_need(
        self,
        portfolio_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame] = None
    ) -> RebalancingDecision:
        """Evaluate if portfolio needs rebalancing."""
        
        try:
            current_weights = portfolio_data.get("current_weights", {})
            target_weights = portfolio_data.get("target_weights", {})
            portfolio_metrics = portfolio_data.get("metrics", {})
            last_rebalance = portfolio_data.get("last_rebalanced_at", datetime.utcnow() - timedelta(days=30))
            frequency = portfolio_data.get("rebalancing_frequency", RebalancingFrequency.WEEKLY)
            
            # Check all rebalancing triggers
            triggers = []
            
            # 1. Scheduled rebalancing
            scheduled_due, scheduled_analysis = self.scheduler.should_rebalance_scheduled(
                last_rebalance, frequency
            )
            if scheduled_due:
                triggers.append({
                    "type": RebalancingTrigger.SCHEDULED,
                    "value": scheduled_analysis["time_since_last"],
                    "threshold": scheduled_analysis["required_interval_hours"],
                    "urgency": "NORMAL",
                    "analysis": scheduled_analysis
                })
            
            # 2. Drift-based rebalancing
            if current_weights and target_weights:
                drift_needed, drift_analysis = self.drift_analyzer.analyze_drift(
                    current_weights, target_weights
                )
                if drift_needed:
                    urgency = "HIGH" if drift_analysis["max_drift"] > 0.1 else "NORMAL"
                    triggers.append({
                        "type": RebalancingTrigger.DRIFT,
                        "value": drift_analysis["max_drift"],
                        "threshold": drift_analysis["drift_threshold"],
                        "urgency": urgency,
                        "analysis": drift_analysis
                    })
            
            # 3. Volatility-based rebalancing
            if market_data is not None and len(market_data) > 0:
                returns = market_data.pct_change().dropna().iloc[:, 0]  # Use first column as proxy
                current_vol = portfolio_metrics.get("volatility", 0)
                target_vol = portfolio_data.get("max_volatility")
                
                vol_needed, vol_analysis = self.volatility_monitor.analyze_volatility_regime(
                    returns, current_vol, target_vol
                )
                if vol_needed:
                    urgency = "HIGH" if vol_analysis.get("target_breach", False) else "NORMAL"
                    triggers.append({
                        "type": RebalancingTrigger.VOLATILITY,
                        "value": vol_analysis["volatility_change"],
                        "threshold": vol_analysis["volatility_threshold"],
                        "urgency": urgency,
                        "analysis": vol_analysis
                    })
            
            # 4. Risk limit breaches
            risk_needed, risk_analysis = self.risk_monitor.check_risk_limits(portfolio_metrics)
            if risk_needed:
                max_severity = max([v["severity"] for v in risk_analysis["violations"]], default="LOW")
                urgency = "CRITICAL" if max_severity == "HIGH" else "HIGH"
                triggers.append({
                    "type": RebalancingTrigger.RISK_LIMIT,
                    "value": len(risk_analysis["violations"]),
                    "threshold": 0,
                    "urgency": urgency,
                    "analysis": risk_analysis
                })
            
            # Determine overall rebalancing decision
            if not triggers:
                return RebalancingDecision(
                    should_rebalance=False,
                    trigger_type=RebalancingTrigger.SCHEDULED,
                    trigger_value=0,
                    threshold=0,
                    reason="No rebalancing triggers detected"
                )
            
            # Select highest priority trigger
            priority_order = {
                "CRITICAL": 4,
                "HIGH": 3,
                "NORMAL": 2,
                "LOW": 1
            }
            
            primary_trigger = max(triggers, key=lambda t: priority_order.get(t["urgency"], 0))
            
            # Estimate transaction costs
            if current_weights and target_weights:
                portfolio_value = portfolio_data.get("total_value", 100000)
                cost_analysis = self.cost_estimator.estimate_rebalancing_costs(
                    current_weights, target_weights, portfolio_value
                )
                
                # Adjust decision based on costs
                if cost_analysis["total_cost_bps"] > 50:  # High cost threshold
                    if primary_trigger["urgency"] in ["LOW", "NORMAL"]:
                        return RebalancingDecision(
                            should_rebalance=False,
                            trigger_type=primary_trigger["type"],
                            trigger_value=primary_trigger["value"],
                            threshold=primary_trigger["threshold"],
                            reason=f"Rebalancing costs too high: {cost_analysis['total_cost_bps']:.1f} bps"
                        )
            
            # Determine execution method
            execution_method = "IMMEDIATE"
            if primary_trigger["urgency"] == "LOW":
                execution_method = "GRADUAL"
            elif primary_trigger["urgency"] == "CRITICAL":
                execution_method = "IMMEDIATE"
            
            return RebalancingDecision(
                should_rebalance=True,
                trigger_type=primary_trigger["type"],
                trigger_value=primary_trigger["value"],
                threshold=primary_trigger["threshold"],
                urgency=primary_trigger["urgency"],
                reason=f"Triggered by {primary_trigger['type'].value}: {primary_trigger.get('analysis', {})}",
                recommended_method=execution_method
            )
            
        except Exception as e:
            logger.error(f"Error evaluating rebalancing need: {e}")
            return RebalancingDecision(
                should_rebalance=False,
                trigger_type=RebalancingTrigger.MANUAL,
                trigger_value=0,
                threshold=0,
                reason=f"Error in evaluation: {str(e)}"
            )
    
    def calculate_optimal_rebalancing_weights(
        self,
        current_weights: Dict[str, float],
        target_weights: Dict[str, float],
        transaction_costs: Dict[str, float],
        execution_method: str = "IMMEDIATE"
    ) -> Dict[str, float]:
        """Calculate optimal rebalancing weights considering transaction costs."""
        
        if execution_method == "IMMEDIATE":
            return target_weights.copy()
        
        elif execution_method == "GRADUAL":
            # Move partially toward target to minimize costs
            rebalancing_weights = {}
            
            for strategy in set(current_weights.keys()) | set(target_weights.keys()):
                current = current_weights.get(strategy, 0.0)
                target = target_weights.get(strategy, 0.0)
                cost = transaction_costs.get(strategy, 0.0)
                
                # Move 50% toward target for gradual rebalancing
                # Adjust based on transaction costs
                cost_factor = max(0.3, 1.0 - cost / 100)  # Reduce movement for high-cost strategies
                movement_factor = 0.5 * cost_factor
                
                new_weight = current + (target - current) * movement_factor
                rebalancing_weights[strategy] = new_weight
            
            # Normalize weights
            total_weight = sum(rebalancing_weights.values())
            if total_weight > 0:
                rebalancing_weights = {k: v / total_weight for k, v in rebalancing_weights.items()}
            
            return rebalancing_weights
        
        else:
            return target_weights.copy()
