"""
Multi-broker execution gateway with FIX protocol and REST API support.
"""

import logging
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
import json

# FIX protocol support
try:
    import quickfix as fix
    FIX_AVAILABLE = True
except ImportError:
    FIX_AVAILABLE = False
    logging.warning("QuickFIX not available - install for FIX protocol support")

# HTTP client for REST APIs
import httpx

from app.core.config import settings
from app.schemas.execution import (
    OrderRequest, OrderResponse, ExecutionReport, OrderStatus, 
    AssetClass, OrderSide, OrderType, BrokerConnection
)

logger = logging.getLogger(__name__)


class FIXApplication(fix.Application if FIX_AVAILABLE else object):
    """FIX application for institutional broker connectivity."""
    
    def __init__(self, execution_gateway):
        """Initialize FIX application."""
        if FIX_AVAILABLE:
            super().__init__()
        self.execution_gateway = execution_gateway
        self.session_id = None
        self.logged_on = False
        
    def onCreate(self, session_id):
        """Called when session is created."""
        self.session_id = session_id
        logger.info(f"FIX session created: {session_id}")
        
    def onLogon(self, session_id):
        """Called when logon is successful."""
        self.session_id = session_id
        self.logged_on = True
        logger.info(f"FIX session logged on: {session_id}")
        
    def onLogout(self, session_id):
        """Called when logout occurs."""
        self.logged_on = False
        logger.info(f"FIX session logged out: {session_id}")
        
    def toAdmin(self, message, session_id):
        """Called for outgoing admin messages."""
        logger.debug(f"FIX admin message sent: {message}")
        
    def fromAdmin(self, message, session_id):
        """Called for incoming admin messages."""
        logger.debug(f"FIX admin message received: {message}")
        
    def toApp(self, message, session_id):
        """Called for outgoing application messages."""
        logger.debug(f"FIX app message sent: {message}")
        
    def fromApp(self, message, session_id):
        """Called for incoming application messages."""
        try:
            msg_type = fix.MsgType()
            message.getHeader().getField(msg_type)
            
            if msg_type.getValue() == fix.MsgType_ExecutionReport:
                self._handle_execution_report(message)
            elif msg_type.getValue() == fix.MsgType_OrderCancelReject:
                self._handle_cancel_reject(message)
            elif msg_type.getValue() == fix.MsgType_Reject:
                self._handle_reject(message)
                
        except Exception as e:
            logger.error(f"Error processing FIX message: {e}")
    
    def _handle_execution_report(self, message):
        """Handle execution report from broker."""
        try:
            # Extract execution details
            cl_ord_id = fix.ClOrdID()
            order_id = fix.OrderID()
            exec_id = fix.ExecID()
            exec_type = fix.ExecType()
            ord_status = fix.OrdStatus()
            
            message.getField(cl_ord_id)
            message.getField(order_id)
            message.getField(exec_id)
            message.getField(exec_type)
            message.getField(ord_status)
            
            # Extract quantities and prices
            last_qty = fix.LastQty()
            last_px = fix.LastPx()
            cum_qty = fix.CumQty()
            avg_px = fix.AvgPx()
            
            if message.isSetField(last_qty):
                message.getField(last_qty)
            if message.isSetField(last_px):
                message.getField(last_px)
            if message.isSetField(cum_qty):
                message.getField(cum_qty)
            if message.isSetField(avg_px):
                message.getField(avg_px)
            
            # Create execution report
            execution_data = {
                "client_order_id": cl_ord_id.getValue(),
                "broker_order_id": order_id.getValue(),
                "execution_id": exec_id.getValue(),
                "exec_type": exec_type.getValue(),
                "order_status": ord_status.getValue(),
                "last_quantity": float(last_qty.getValue()) if message.isSetField(last_qty) else 0,
                "last_price": float(last_px.getValue()) if message.isSetField(last_px) else 0,
                "cumulative_quantity": float(cum_qty.getValue()) if message.isSetField(cum_qty) else 0,
                "average_price": float(avg_px.getValue()) if message.isSetField(avg_px) else 0,
                "execution_time": datetime.utcnow()
            }
            
            # Forward to execution gateway
            asyncio.create_task(
                self.execution_gateway._process_execution_report(execution_data)
            )
            
        except Exception as e:
            logger.error(f"Error handling execution report: {e}")
    
    def _handle_cancel_reject(self, message):
        """Handle order cancel reject."""
        try:
            cl_ord_id = fix.ClOrdID()
            cxl_rej_reason = fix.CxlRejReason()
            
            message.getField(cl_ord_id)
            message.getField(cxl_rej_reason)
            
            logger.warning(f"Order cancel rejected: {cl_ord_id.getValue()}, reason: {cxl_rej_reason.getValue()}")
            
        except Exception as e:
            logger.error(f"Error handling cancel reject: {e}")
    
    def _handle_reject(self, message):
        """Handle order reject."""
        try:
            cl_ord_id = fix.ClOrdID()
            text = fix.Text()
            
            message.getField(cl_ord_id)
            if message.isSetField(text):
                message.getField(text)
            
            logger.warning(f"Order rejected: {cl_ord_id.getValue()}, reason: {text.getValue()}")
            
        except Exception as e:
            logger.error(f"Error handling reject: {e}")


class InteractiveBrokersAdapter:
    """Interactive Brokers REST API adapter."""
    
    def __init__(self):
        """Initialize IB adapter."""
        self.base_url = f"https://localhost:{settings.IB_PORT}/v1/api"
        self.client = httpx.AsyncClient(verify=False)  # IB uses self-signed cert
        self.account = settings.IB_ACCOUNT
        
    async def initialize(self):
        """Initialize IB connection."""
        try:
            # Check authentication status
            response = await self.client.get(f"{self.base_url}/iserver/auth/status")
            if response.status_code == 200:
                auth_data = response.json()
                if auth_data.get("authenticated"):
                    logger.info("IB connection authenticated")
                    return True
                else:
                    logger.warning("IB connection not authenticated")
                    return False
            else:
                logger.error(f"IB auth check failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"IB initialization failed: {e}")
            return False
    
    async def submit_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Submit order to Interactive Brokers."""
        try:
            # Convert order to IB format
            ib_order = {
                "conid": await self._get_contract_id(order_request.symbol),
                "orderType": self._convert_order_type(order_request.order_type),
                "side": self._convert_side(order_request.side),
                "quantity": float(order_request.quantity),
                "tif": self._convert_time_in_force(order_request.time_in_force)
            }
            
            # Add price for limit orders
            if order_request.price:
                ib_order["price"] = float(order_request.price)
            
            # Submit order
            response = await self.client.post(
                f"{self.base_url}/iserver/account/{self.account}/orders",
                json={"orders": [ib_order]}
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "broker_order_id": result[0].get("order_id"),
                    "status": "SUBMITTED"
                }
            else:
                return {
                    "success": False,
                    "error": f"IB order submission failed: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"IB order submission error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cancel_order(self, broker_order_id: str) -> Dict[str, Any]:
        """Cancel order at Interactive Brokers."""
        try:
            response = await self.client.delete(
                f"{self.base_url}/iserver/account/{self.account}/order/{broker_order_id}"
            )
            
            if response.status_code == 200:
                return {"success": True, "status": "CANCELLED"}
            else:
                return {
                    "success": False,
                    "error": f"IB order cancellation failed: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"IB order cancellation error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_order_status(self, broker_order_id: str) -> Dict[str, Any]:
        """Get order status from Interactive Brokers."""
        try:
            response = await self.client.get(
                f"{self.base_url}/iserver/account/{self.account}/order/{broker_order_id}"
            )
            
            if response.status_code == 200:
                order_data = response.json()
                return {
                    "success": True,
                    "status": order_data.get("status"),
                    "filled_quantity": order_data.get("filled", 0),
                    "avg_price": order_data.get("avg_price")
                }
            else:
                return {
                    "success": False,
                    "error": f"IB order status failed: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"IB order status error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_contract_id(self, symbol: str) -> int:
        """Get contract ID for symbol."""
        try:
            response = await self.client.get(
                f"{self.base_url}/iserver/secdef/search",
                params={"symbol": symbol}
            )
            
            if response.status_code == 200:
                contracts = response.json()
                if contracts:
                    return contracts[0].get("conid")
            
            raise ValueError(f"Contract not found for symbol: {symbol}")
            
        except Exception as e:
            logger.error(f"Contract ID lookup failed: {e}")
            raise
    
    def _convert_order_type(self, order_type: OrderType) -> str:
        """Convert order type to IB format."""
        mapping = {
            OrderType.MARKET: "MKT",
            OrderType.LIMIT: "LMT",
            OrderType.STOP: "STP",
            OrderType.STOP_LIMIT: "STP LMT"
        }
        return mapping.get(order_type, "MKT")
    
    def _convert_side(self, side: OrderSide) -> str:
        """Convert order side to IB format."""
        mapping = {
            OrderSide.BUY: "BUY",
            OrderSide.SELL: "SELL",
            OrderSide.SHORT: "SSHORT"
        }
        return mapping.get(side, "BUY")
    
    def _convert_time_in_force(self, tif: str) -> str:
        """Convert time in force to IB format."""
        mapping = {
            "DAY": "DAY",
            "GTC": "GTC",
            "IOC": "IOC",
            "FOK": "FOK"
        }
        return mapping.get(tif, "DAY")


class AlpacaAdapter:
    """Alpaca REST API adapter."""
    
    def __init__(self):
        """Initialize Alpaca adapter."""
        self.base_url = settings.ALPACA_BASE_URL
        self.api_key = settings.ALPACA_API_KEY
        self.secret_key = settings.ALPACA_SECRET_KEY
        self.client = httpx.AsyncClient()
        
    async def initialize(self):
        """Initialize Alpaca connection."""
        try:
            headers = {
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.secret_key
            }
            
            response = await self.client.get(
                f"{self.base_url}/v2/account",
                headers=headers
            )
            
            if response.status_code == 200:
                logger.info("Alpaca connection authenticated")
                return True
            else:
                logger.error(f"Alpaca auth failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Alpaca initialization failed: {e}")
            return False
    
    async def submit_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Submit order to Alpaca."""
        try:
            headers = {
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.secret_key,
                "Content-Type": "application/json"
            }
            
            alpaca_order = {
                "symbol": order_request.symbol,
                "qty": str(order_request.quantity),
                "side": order_request.side.value.lower(),
                "type": order_request.order_type.value.lower(),
                "time_in_force": order_request.time_in_force.value.lower()
            }
            
            if order_request.price:
                alpaca_order["limit_price"] = str(order_request.price)
            
            if order_request.stop_price:
                alpaca_order["stop_price"] = str(order_request.stop_price)
            
            response = await self.client.post(
                f"{self.base_url}/v2/orders",
                headers=headers,
                json=alpaca_order
            )
            
            if response.status_code == 201:
                result = response.json()
                return {
                    "success": True,
                    "broker_order_id": result.get("id"),
                    "status": "SUBMITTED"
                }
            else:
                return {
                    "success": False,
                    "error": f"Alpaca order submission failed: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"Alpaca order submission error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cancel_order(self, broker_order_id: str) -> Dict[str, Any]:
        """Cancel order at Alpaca."""
        try:
            headers = {
                "APCA-API-KEY-ID": self.api_key,
                "APCA-API-SECRET-KEY": self.secret_key
            }
            
            response = await self.client.delete(
                f"{self.base_url}/v2/orders/{broker_order_id}",
                headers=headers
            )
            
            if response.status_code == 204:
                return {"success": True, "status": "CANCELLED"}
            else:
                return {
                    "success": False,
                    "error": f"Alpaca order cancellation failed: {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"Alpaca order cancellation error: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class ExecutionGateway:
    """Main execution gateway coordinating multi-broker execution."""
    
    def __init__(self):
        """Initialize execution gateway."""
        self.brokers = {}
        self.fix_application = None
        self.fix_initiator = None
        self.active_orders = {}
        self.execution_callbacks = []
        
    async def initialize(self):
        """Initialize execution gateway and broker connections."""
        try:
            # Initialize FIX protocol if available
            if FIX_AVAILABLE and settings.FIX_CONFIG_FILE:
                await self._initialize_fix()
            
            # Initialize broker adapters
            if "INTERACTIVE_BROKERS" in settings.SUPPORTED_BROKERS:
                ib_adapter = InteractiveBrokersAdapter()
                if await ib_adapter.initialize():
                    self.brokers["INTERACTIVE_BROKERS"] = ib_adapter
                    logger.info("Interactive Brokers adapter initialized")
            
            if "ALPACA" in settings.SUPPORTED_BROKERS:
                alpaca_adapter = AlpacaAdapter()
                if await alpaca_adapter.initialize():
                    self.brokers["ALPACA"] = alpaca_adapter
                    logger.info("Alpaca adapter initialized")
            
            logger.info(f"Execution gateway initialized with {len(self.brokers)} brokers")
            
        except Exception as e:
            logger.error(f"Execution gateway initialization failed: {e}")
            raise
    
    async def _initialize_fix(self):
        """Initialize FIX protocol connection."""
        try:
            if not FIX_AVAILABLE:
                logger.warning("FIX protocol not available")
                return
            
            # Create FIX application
            self.fix_application = FIXApplication(self)
            
            # Create session settings
            settings_obj = fix.SessionSettings(settings.FIX_CONFIG_FILE)
            
            # Create message store and log factories
            store_factory = fix.FileStoreFactory(settings_obj)
            log_factory = fix.FileLogFactory(settings_obj)
            
            # Create initiator
            self.fix_initiator = fix.SocketInitiator(
                self.fix_application, store_factory, settings_obj, log_factory
            )
            
            # Start FIX session
            self.fix_initiator.start()
            logger.info("FIX protocol initialized")
            
        except Exception as e:
            logger.error(f"FIX initialization failed: {e}")
            raise
    
    async def submit_order(self, order_request: OrderRequest) -> OrderResponse:
        """Submit order through appropriate broker."""
        try:
            # Select broker
            broker = self._select_broker(order_request)
            
            if broker not in self.brokers:
                raise ValueError(f"Broker {broker} not available")
            
            # Generate order ID
            order_id = str(uuid.uuid4())
            client_order_id = order_request.client_order_id or f"ATHENA_{order_id[:8]}"
            
            # Submit to broker
            broker_adapter = self.brokers[broker]
            result = await broker_adapter.submit_order(order_request)
            
            if result["success"]:
                # Store active order
                self.active_orders[client_order_id] = {
                    "order_request": order_request,
                    "broker": broker,
                    "broker_order_id": result.get("broker_order_id"),
                    "status": result.get("status", "SUBMITTED"),
                    "submitted_at": datetime.utcnow()
                }
                
                # Create response
                return OrderResponse(
                    id=uuid.UUID(order_id),
                    order_id=order_id,
                    client_order_id=client_order_id,
                    strategy_id=order_request.strategy_id,
                    user_id=uuid.uuid4(),  # TODO: Get from context
                    symbol=order_request.symbol,
                    asset_class=order_request.asset_class,
                    side=order_request.side,
                    order_type=order_request.order_type,
                    time_in_force=order_request.time_in_force,
                    quantity=order_request.quantity,
                    filled_quantity=Decimal("0"),
                    remaining_quantity=order_request.quantity,
                    price=order_request.price,
                    status=OrderStatus.SUBMITTED,
                    broker=broker,
                    broker_order_id=result.get("broker_order_id"),
                    risk_check_status="PASSED",
                    created_at=datetime.utcnow(),
                    submitted_at=datetime.utcnow()
                )
            else:
                raise ValueError(f"Order submission failed: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"Order submission failed: {e}")
            raise
    
    async def cancel_order(self, client_order_id: str) -> Dict[str, Any]:
        """Cancel order."""
        try:
            if client_order_id not in self.active_orders:
                raise ValueError(f"Order {client_order_id} not found")
            
            order_info = self.active_orders[client_order_id]
            broker = order_info["broker"]
            broker_order_id = order_info["broker_order_id"]
            
            # Cancel at broker
            broker_adapter = self.brokers[broker]
            result = await broker_adapter.cancel_order(broker_order_id)
            
            if result["success"]:
                # Update order status
                order_info["status"] = "CANCELLED"
                return {"success": True, "status": "CANCELLED"}
            else:
                return result
                
        except Exception as e:
            logger.error(f"Order cancellation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_order_status(self, client_order_id: str) -> Dict[str, Any]:
        """Get order status."""
        try:
            if client_order_id not in self.active_orders:
                raise ValueError(f"Order {client_order_id} not found")
            
            order_info = self.active_orders[client_order_id]
            broker = order_info["broker"]
            broker_order_id = order_info["broker_order_id"]
            
            # Get status from broker
            broker_adapter = self.brokers[broker]
            result = await broker_adapter.get_order_status(broker_order_id)
            
            if result["success"]:
                # Update local status
                order_info["status"] = result["status"]
                return result
            else:
                return result
                
        except Exception as e:
            logger.error(f"Order status check failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _select_broker(self, order_request: OrderRequest) -> str:
        """Select appropriate broker for order."""
        # Use preferred broker if specified and available
        if order_request.preferred_broker and order_request.preferred_broker in self.brokers:
            return order_request.preferred_broker
        
        # Default broker selection logic
        if order_request.asset_class == AssetClass.EQUITY:
            if "INTERACTIVE_BROKERS" in self.brokers:
                return "INTERACTIVE_BROKERS"
            elif "ALPACA" in self.brokers:
                return "ALPACA"
        
        elif order_request.asset_class == AssetClass.CRYPTO:
            # Prefer crypto-specific brokers
            for broker in ["BINANCE", "COINBASE"]:
                if broker in self.brokers:
                    return broker
        
        # Fallback to first available broker
        if self.brokers:
            return list(self.brokers.keys())[0]
        
        raise ValueError("No brokers available")
    
    async def _process_execution_report(self, execution_data: Dict[str, Any]):
        """Process execution report from broker."""
        try:
            client_order_id = execution_data["client_order_id"]
            
            if client_order_id in self.active_orders:
                order_info = self.active_orders[client_order_id]
                
                # Update order status
                order_info["status"] = execution_data["order_status"]
                order_info["filled_quantity"] = execution_data.get("cumulative_quantity", 0)
                order_info["avg_price"] = execution_data.get("average_price")
                
                # Notify callbacks
                for callback in self.execution_callbacks:
                    try:
                        await callback(execution_data)
                    except Exception as e:
                        logger.error(f"Execution callback failed: {e}")
                
                logger.info(f"Processed execution report for order {client_order_id}")
            
        except Exception as e:
            logger.error(f"Error processing execution report: {e}")
    
    def add_execution_callback(self, callback):
        """Add execution callback."""
        self.execution_callbacks.append(callback)
    
    async def get_broker_connections(self) -> List[BrokerConnection]:
        """Get broker connection status."""
        connections = []
        
        for broker_name, adapter in self.brokers.items():
            # Basic connection info
            connection = BrokerConnection(
                broker_name=broker_name,
                connection_type="REST",  # Default
                status="CONNECTED",
                supported_asset_classes=[AssetClass.EQUITY],  # Default
                supported_order_types=[OrderType.MARKET, OrderType.LIMIT],  # Default
                connected_at=datetime.utcnow()
            )
            
            connections.append(connection)
        
        return connections
    
    async def cleanup(self):
        """Cleanup execution gateway."""
        try:
            # Stop FIX initiator
            if self.fix_initiator:
                self.fix_initiator.stop()
            
            # Close broker connections
            for adapter in self.brokers.values():
                if hasattr(adapter, 'client'):
                    await adapter.client.aclose()
            
            logger.info("Execution gateway cleaned up")
            
        except Exception as e:
            logger.error(f"Execution gateway cleanup failed: {e}")
