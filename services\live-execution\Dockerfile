# Use Python 3.11 slim image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies for trading libraries
RUN apt-get update && apt-get install -y \
    build-essential \
    wget \
    curl \
    git \
    pkg-config \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    tcl8.6-dev \
    tk8.6-dev \
    python3-tk \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    && rm -rf /var/lib/apt/lists/*

# Install QuickFIX dependencies
RUN apt-get update && apt-get install -y \
    cmake \
    libboost-all-dev \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/execution_logs /app/strategy_artifacts /app/risk_reports /app/execution_reports /app/logs /app/config

# Copy application code
COPY . .

# Create FIX configuration directory
RUN mkdir -p /app/config/fix

# Create sample FIX configuration file
RUN echo "[DEFAULT]" > /app/config/fix_config.cfg && \
    echo "ConnectionType=initiator" >> /app/config/fix_config.cfg && \
    echo "ReconnectInterval=60" >> /app/config/fix_config.cfg && \
    echo "FileStorePath=/app/logs/fix" >> /app/config/fix_config.cfg && \
    echo "FileLogPath=/app/logs/fix" >> /app/config/fix_config.cfg && \
    echo "StartTime=00:00:00" >> /app/config/fix_config.cfg && \
    echo "EndTime=00:00:00" >> /app/config/fix_config.cfg && \
    echo "UseDataDictionary=Y" >> /app/config/fix_config.cfg && \
    echo "DataDictionary=FIX44.xml" >> /app/config/fix_config.cfg && \
    echo "ValidateUserDefinedFields=Y" >> /app/config/fix_config.cfg && \
    echo "" >> /app/config/fix_config.cfg && \
    echo "[SESSION]" >> /app/config/fix_config.cfg && \
    echo "BeginString=FIX.4.4" >> /app/config/fix_config.cfg && \
    echo "SenderCompID=ATHENA" >> /app/config/fix_config.cfg && \
    echo "TargetCompID=BROKER" >> /app/config/fix_config.cfg && \
    echo "SocketConnectPort=9878" >> /app/config/fix_config.cfg && \
    echo "SocketConnectHost=127.0.0.1" >> /app/config/fix_config.cfg && \
    echo "HeartBtInt=30" >> /app/config/fix_config.cfg

# Create non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
