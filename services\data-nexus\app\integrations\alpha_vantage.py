"""
Alpha Vantage API integration for FOREX data ingestion.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional, <PERSON>ple
import json

import httpx
from fastapi import HTTPException

from app.core.config import settings
from app.schemas.market_data import OHLCVCreate, Timeframe

logger = logging.getLogger(__name__)


class AlphaVantageClient:
    """Alpha Vantage API client for FOREX data."""
    
    BASE_URL = "https://www.alphavantage.co/query"
    
    # Alpha Vantage function mappings
    TIMEFRAME_FUNCTIONS = {
        Timeframe.ONE_MINUTE: "FX_INTRADAY",
        Timeframe.FIVE_MINUTES: "FX_INTRADAY", 
        Timeframe.FIFTEEN_MINUTES: "FX_INTRADAY",
        Timeframe.THIRTY_MINUTES: "FX_INTRADAY",
        Timeframe.ONE_HOUR: "FX_INTRADAY",
        Timeframe.ONE_DAY: "FX_DAILY",
        Timeframe.ONE_WEEK: "FX_WEEKLY",
        Timeframe.ONE_MONTH: "FX_MONTHLY",
    }
    
    # Alpha Vantage interval mappings
    INTERVAL_MAPPING = {
        Timeframe.ONE_MINUTE: "1min",
        Timeframe.FIVE_MINUTES: "5min",
        Timeframe.FIFTEEN_MINUTES: "15min",
        Timeframe.THIRTY_MINUTES: "30min",
        Timeframe.ONE_HOUR: "60min",
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Alpha Vantage client.
        
        Args:
            api_key: Alpha Vantage API key
        """
        self.api_key = api_key or settings.ALPHA_VANTAGE_API_KEY
        if not self.api_key:
            raise ValueError("Alpha Vantage API key is required")
        
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(settings.REQUEST_TIMEOUT),
            limits=httpx.Limits(max_connections=settings.MAX_CONCURRENT_REQUESTS)
        )
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def get_forex_data(
        self,
        from_symbol: str,
        to_symbol: str,
        timeframe: Timeframe,
        outputsize: str = "compact"
    ) -> List[OHLCVCreate]:
        """
        Get FOREX data from Alpha Vantage.
        
        Args:
            from_symbol: Base currency (e.g., 'EUR')
            to_symbol: Quote currency (e.g., 'USD')
            timeframe: Data timeframe
            outputsize: 'compact' (last 100 data points) or 'full' (20+ years)
            
        Returns:
            List[OHLCVCreate]: List of OHLCV data points
            
        Raises:
            HTTPException: If API request fails
        """
        function = self.TIMEFRAME_FUNCTIONS.get(timeframe)
        if not function:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        params = {
            "function": function,
            "from_symbol": from_symbol,
            "to_symbol": to_symbol,
            "apikey": self.api_key,
            "outputsize": outputsize,
        }
        
        # Add interval for intraday data
        if function == "FX_INTRADAY":
            interval = self.INTERVAL_MAPPING.get(timeframe)
            if interval:
                params["interval"] = interval
        
        try:
            logger.info(f"Fetching {from_symbol}/{to_symbol} data for {timeframe} from Alpha Vantage")
            response = await self.client.get(self.BASE_URL, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API errors
            if "Error Message" in data:
                raise HTTPException(status_code=400, detail=f"Alpha Vantage error: {data['Error Message']}")
            
            if "Note" in data:
                logger.warning(f"Alpha Vantage note: {data['Note']}")
                # Rate limit hit, return empty list
                return []
            
            # Parse the response
            return self._parse_forex_response(data, timeframe)
            
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching data from Alpha Vantage: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise HTTPException(status_code=500, detail="Invalid response from Alpha Vantage")
    
    def _parse_forex_response(self, data: Dict, timeframe: Timeframe) -> List[OHLCVCreate]:
        """
        Parse Alpha Vantage FOREX response.
        
        Args:
            data: Raw API response
            timeframe: Data timeframe
            
        Returns:
            List[OHLCVCreate]: Parsed OHLCV data
        """
        ohlcv_data = []
        
        # Determine the time series key based on function
        function = self.TIMEFRAME_FUNCTIONS[timeframe]
        
        if function == "FX_INTRADAY":
            interval = self.INTERVAL_MAPPING[timeframe]
            time_series_key = f"Time Series FX ({interval})"
        elif function == "FX_DAILY":
            time_series_key = "Time Series FX (Daily)"
        elif function == "FX_WEEKLY":
            time_series_key = "Time Series FX (Weekly)"
        elif function == "FX_MONTHLY":
            time_series_key = "Time Series FX (Monthly)"
        else:
            raise ValueError(f"Unknown function: {function}")
        
        time_series = data.get(time_series_key, {})
        
        for timestamp_str, values in time_series.items():
            try:
                # Parse timestamp
                if function == "FX_INTRADAY":
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                else:
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d")
                
                # Extract OHLCV values
                ohlcv = OHLCVCreate(
                    time=timestamp,
                    timeframe=timeframe,
                    open=Decimal(values["1. open"]),
                    high=Decimal(values["2. high"]),
                    low=Decimal(values["3. low"]),
                    close=Decimal(values["4. close"]),
                    volume=Decimal("0"),  # Alpha Vantage doesn't provide volume for FOREX
                    instrument_id=None  # Will be set by the caller
                )
                
                ohlcv_data.append(ohlcv)
                
            except (KeyError, ValueError, TypeError) as e:
                logger.warning(f"Failed to parse data point {timestamp_str}: {e}")
                continue
        
        # Sort by timestamp (oldest first)
        ohlcv_data.sort(key=lambda x: x.time)
        
        logger.info(f"Parsed {len(ohlcv_data)} data points")
        return ohlcv_data
    
    async def get_currency_exchange_rate(self, from_currency: str, to_currency: str) -> Dict:
        """
        Get real-time currency exchange rate.
        
        Args:
            from_currency: Base currency
            to_currency: Quote currency
            
        Returns:
            Dict: Exchange rate information
        """
        params = {
            "function": "CURRENCY_EXCHANGE_RATE",
            "from_currency": from_currency,
            "to_currency": to_currency,
            "apikey": self.api_key,
        }
        
        try:
            response = await self.client.get(self.BASE_URL, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if "Error Message" in data:
                raise HTTPException(status_code=400, detail=f"Alpha Vantage error: {data['Error Message']}")
            
            return data.get("Realtime Currency Exchange Rate", {})
            
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching exchange rate: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch exchange rate: {str(e)}")
    
    async def get_available_currencies(self) -> List[str]:
        """
        Get list of available currencies.
        Note: Alpha Vantage doesn't provide a direct endpoint for this,
        so we return a predefined list of major currencies.
        
        Returns:
            List[str]: List of currency codes
        """
        return [
            "USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY",
            "SEK", "NZD", "MXN", "SGD", "HKD", "NOK", "TRY", "ZAR",
            "BRL", "TWD", "DKK", "PLN", "THB", "ILS", "KRW", "CLP",
            "PHP", "AED", "COP", "SAR", "MYR", "RON"
        ]
