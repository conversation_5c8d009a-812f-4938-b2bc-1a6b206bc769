"""
Pydantic schemas for strategy operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, Field, validator


class AIParadigm(str, Enum):
    """AI paradigm enumeration."""
    GENETIC_PROGRAMMING = "GP"
    REINFORCEMENT_LEARNING = "RL"
    DEEP_LEARNING = "DL"
    HYBRID = "HYBRID"


class StrategyStatus(str, Enum):
    """Strategy status enumeration."""
    CREATED = "CREATED"
    TRAINING = "TRAINING"
    TRAINED = "TRAINED"
    DEPLOYED = "DEPLOYED"
    ARCHIVED = "ARCHIVED"


class TrainingStatus(str, Enum):
    """Training run status enumeration."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class StrategyBase(BaseModel):
    """Base strategy schema."""
    name: str = Field(..., min_length=1, max_length=255, description="Strategy name")
    description: Optional[str] = Field(None, description="Strategy description")
    ai_paradigm: AIParadigm = Field(..., description="AI paradigm used")
    paradigm_config: Dict[str, Any] = Field(default_factory=dict, description="Paradigm-specific configuration")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Strategy parameters")


class StrategyCreate(StrategyBase):
    """Schema for creating a new strategy."""
    user_id: uuid.UUID = Field(..., description="User ID who owns the strategy")
    
    @validator("parameters")
    def validate_parameters(cls, v, values):
        """Validate parameters based on AI paradigm."""
        paradigm = values.get("ai_paradigm")
        if paradigm == AIParadigm.GENETIC_PROGRAMMING:
            required_params = ["population_size", "generations", "tournament_size"]
            for param in required_params:
                if param not in v:
                    raise ValueError(f"Missing required GP parameter: {param}")
        elif paradigm == AIParadigm.REINFORCEMENT_LEARNING:
            required_params = ["learning_rate", "discount_factor", "exploration_rate"]
            for param in required_params:
                if param not in v:
                    raise ValueError(f"Missing required RL parameter: {param}")
        elif paradigm == AIParadigm.DEEP_LEARNING:
            required_params = ["batch_size", "epochs", "learning_rate"]
            for param in required_params:
                if param not in v:
                    raise ValueError(f"Missing required DL parameter: {param}")
        return v


class StrategyUpdate(BaseModel):
    """Schema for updating strategy information."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class Strategy(StrategyBase):
    """Public strategy schema."""
    id: uuid.UUID
    user_id: uuid.UUID
    status: StrategyStatus
    performance_metrics: Dict[str, Any]
    performance_score: Optional[Decimal]
    training_data_start: Optional[datetime]
    training_data_end: Optional[datetime]
    training_duration_seconds: Optional[int]
    is_active: bool
    version: int
    created_at: datetime
    updated_at: datetime
    trained_at: Optional[datetime]
    deployed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class StrategyVersionCreate(BaseModel):
    """Schema for creating a strategy version."""
    strategy_id: uuid.UUID
    parameters: Dict[str, Any]
    trading_rules: Optional[Dict[str, Any]] = None
    model_architecture: Optional[Dict[str, Any]] = None
    change_description: Optional[str] = None


class StrategyVersion(BaseModel):
    """Strategy version schema."""
    id: uuid.UUID
    strategy_id: uuid.UUID
    version_number: int
    parameters: Dict[str, Any]
    trading_rules: Optional[Dict[str, Any]]
    model_architecture: Optional[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    performance_score: Optional[Decimal]
    change_description: Optional[str]
    is_baseline: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class TrainingRunCreate(BaseModel):
    """Schema for creating a training run."""
    strategy_id: uuid.UUID
    training_config: Dict[str, Any]
    hyperparameters: Dict[str, Any] = Field(default_factory=dict)
    data_start_date: datetime
    data_end_date: datetime
    instruments_used: List[uuid.UUID]
    total_epochs: int = Field(..., gt=0)
    total_generations: Optional[int] = Field(None, gt=0)


class TrainingRun(BaseModel):
    """Training run schema."""
    id: uuid.UUID
    strategy_id: uuid.UUID
    training_config: Dict[str, Any]
    hyperparameters: Dict[str, Any]
    data_start_date: datetime
    data_end_date: datetime
    instruments_used: List[uuid.UUID]
    status: TrainingStatus
    current_epoch: int
    total_epochs: int
    current_generation: int
    total_generations: Optional[int]
    training_metrics: Dict[str, Any]
    validation_metrics: Dict[str, Any]
    best_performance: Optional[Decimal]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    estimated_completion: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TrainingProgress(BaseModel):
    """Training progress update schema."""
    current_epoch: int
    current_generation: int
    training_metrics: Dict[str, Any]
    validation_metrics: Dict[str, Any]
    estimated_completion: Optional[datetime]


class PerformanceMetrics(BaseModel):
    """Strategy performance metrics schema."""
    total_return: Optional[Decimal] = None
    sharpe_ratio: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    win_rate: Optional[Decimal] = None
    profit_factor: Optional[Decimal] = None
    calmar_ratio: Optional[Decimal] = None
    sortino_ratio: Optional[Decimal] = None
    volatility: Optional[Decimal] = None
    beta: Optional[Decimal] = None
    alpha: Optional[Decimal] = None
    information_ratio: Optional[Decimal] = None
    treynor_ratio: Optional[Decimal] = None


class GeneticProgrammingConfig(BaseModel):
    """Genetic Programming specific configuration."""
    population_size: int = Field(default=100, ge=10, le=1000)
    generations: int = Field(default=50, ge=5, le=500)
    tournament_size: int = Field(default=3, ge=2, le=10)
    crossover_prob: float = Field(default=0.8, ge=0.0, le=1.0)
    mutation_prob: float = Field(default=0.2, ge=0.0, le=1.0)
    max_tree_depth: int = Field(default=10, ge=3, le=20)
    elitism_size: int = Field(default=5, ge=1, le=50)


class ReinforcementLearningConfig(BaseModel):
    """Reinforcement Learning specific configuration."""
    algorithm: str = Field(default="PPO", description="RL algorithm (PPO, A3C, DQN, etc.)")
    learning_rate: float = Field(default=0.001, ge=1e-6, le=1.0)
    discount_factor: float = Field(default=0.99, ge=0.0, le=1.0)
    exploration_rate: float = Field(default=0.1, ge=0.0, le=1.0)
    batch_size: int = Field(default=32, ge=1, le=1024)
    memory_size: int = Field(default=10000, ge=100, le=1000000)
    target_update_freq: int = Field(default=100, ge=1, le=10000)
    episodes: int = Field(default=1000, ge=10, le=100000)


class DeepLearningConfig(BaseModel):
    """Deep Learning specific configuration."""
    model_type: str = Field(default="LSTM", description="Model architecture (LSTM, GRU, Transformer, etc.)")
    batch_size: int = Field(default=64, ge=1, le=1024)
    epochs: int = Field(default=100, ge=1, le=1000)
    learning_rate: float = Field(default=0.001, ge=1e-6, le=1.0)
    dropout_rate: float = Field(default=0.2, ge=0.0, le=0.9)
    lstm_units: int = Field(default=128, ge=16, le=1024)
    attention_heads: int = Field(default=8, ge=1, le=32)
    sequence_length: int = Field(default=60, ge=10, le=1000)
    layers: int = Field(default=2, ge=1, le=10)


class StrategyDeployment(BaseModel):
    """Strategy deployment configuration."""
    strategy_id: uuid.UUID
    version_number: Optional[int] = None  # If None, deploy latest version
    allocation_percentage: float = Field(..., ge=0.0, le=100.0)
    risk_limits: Dict[str, Any] = Field(default_factory=dict)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class StrategyComparison(BaseModel):
    """Strategy comparison result."""
    strategy_a: Strategy
    strategy_b: Strategy
    comparison_metrics: Dict[str, Any]
    statistical_significance: Dict[str, float]
    recommendation: str
