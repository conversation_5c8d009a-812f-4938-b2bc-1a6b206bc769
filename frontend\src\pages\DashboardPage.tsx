import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  Psychology,
  AccountBalance,
  Assessment,
} from '@mui/icons-material';
import { useAuth } from '@/hooks/useAuth';

interface StatCard {
  title: string;
  value: string;
  icon: React.ReactElement;
  color: 'primary' | 'secondary' | 'success' | 'warning';
}

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  const stats: StatCard[] = [
    {
      title: 'Active Strategies',
      value: '12',
      icon: <Psychology />,
      color: 'primary',
    },
    {
      title: 'Portfolio Value',
      value: '$125,430',
      icon: <AccountBalance />,
      color: 'success',
    },
    {
      title: 'Daily P&L',
      value: '+$2,340',
      icon: <TrendingUp />,
      color: 'success',
    },
    {
      title: 'Running Backtests',
      value: '3',
      icon: <Assessment />,
      color: 'warning',
    },
  ];

  const recentActivities = [
    { id: 1, action: 'Strategy "EURUSD Momentum" completed backtest', time: '2 minutes ago', status: 'success' },
    { id: 2, action: 'New market data ingested for GBPUSD', time: '5 minutes ago', status: 'info' },
    { id: 3, action: 'Portfolio rebalancing triggered', time: '15 minutes ago', status: 'warning' },
    { id: 4, action: 'Strategy "Multi-Pair Arbitrage" deployed', time: '1 hour ago', status: 'success' },
    { id: 5, action: 'Risk limit breach detected and resolved', time: '2 hours ago', status: 'error' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome back, {user?.full_name || user?.username}!
      </Typography>
      
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Here's what's happening with your trading platform today.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {/* Statistics Cards */}
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h5" component="div">
                      {stat.value}
                    </Typography>
                  </Box>
                  <Box color={`${stat.color}.main`}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}

        {/* Market Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Market Overview
              </Typography>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography color="text.secondary">
                  Market data visualization will be implemented here
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List dense>
                {recentActivities.map((activity) => (
                  <ListItem key={activity.id} sx={{ px: 0 }}>
                    <ListItemText
                      primary={activity.action}
                      secondary={
                        <Box display="flex" alignItems="center" justifyContent="space-between" mt={1}>
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                          <Chip
                            size="small"
                            label={activity.status}
                            color={getStatusColor(activity.status) as any}
                            variant="outlined"
                          />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item>
                <Chip
                  label="Create New Strategy"
                  clickable
                  color="primary"
                  variant="outlined"
                />
              </Grid>
              <Grid item>
                <Chip
                  label="Run Backtest"
                  clickable
                  color="secondary"
                  variant="outlined"
                />
              </Grid>
              <Grid item>
                <Chip
                  label="View Market Data"
                  clickable
                  color="info"
                  variant="outlined"
                />
              </Grid>
              <Grid item>
                <Chip
                  label="Portfolio Analysis"
                  clickable
                  color="success"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
