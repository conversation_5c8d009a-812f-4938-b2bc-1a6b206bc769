"""
AthenaTrader API Gateway

Central API gateway service that handles authentication, authorization,
rate limiting, and request routing to microservices.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.middleware.auth import AuthMiddleware
from app.middleware.rate_limit import RateLimitMiddleware
from app.middleware.request_id import RequestIDMiddleware
from app.routers import auth, users, health
from app.core.database import init_db

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader API Gateway...")
    await init_db()
    logger.info("Database initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader API Gateway...")


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader API Gateway",
    description="Central API gateway for AthenaTrader AI trading platform",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(RequestIDMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(AuthMiddleware)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "request_id": getattr(request.state, "request_id", None)
            }
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": 500,
                "message": "Internal server error",
                "request_id": getattr(request.state, "request_id", None)
            }
        }
    )


# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(auth.router, prefix="/auth", tags=["authentication"])
app.include_router(users.router, prefix="/users", tags=["users"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader API Gateway",
        "version": "0.1.0",
        "status": "operational"
    }


@app.get("/info")
async def info():
    """Application information endpoint."""
    return {
        "name": "AthenaTrader API Gateway",
        "version": "0.1.0",
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "services": {
            "data_nexus": settings.DATA_NEXUS_URL,
            "strategy_genesis": settings.STRATEGY_GENESIS_URL,
            "backtesting_engine": settings.BACKTESTING_ENGINE_URL,
            "portfolio_optimization": settings.PORTFOLIO_OPTIMIZATION_URL,
            "xai": settings.XAI_URL,
            "execution_engine": settings.EXECUTION_ENGINE_URL,
            "learning_hub": settings.LEARNING_HUB_URL,
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
