"""
Tests for portfolio optimization functionality.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal

from app.engine.optimization import (
    PortfolioOptimizer, MeanVarianceOptimizer, RiskParityOptimizer,
    BlackLittermanOptimizer, HierarchicalRiskParityOptimizer, CovarianceEstimator
)
from app.schemas.portfolio import OptimizationMethod, ObjectiveFunction, OptimizationConfig, PortfolioConstraints


@pytest.fixture
def sample_strategy_data():
    """Create sample strategy return data for testing."""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    
    # Generate correlated strategy returns
    n_strategies = 5
    base_returns = np.random.randn(252, n_strategies) * 0.01
    
    # Add some correlation structure
    correlation_matrix = np.array([
        [1.0, 0.3, 0.1, 0.2, 0.0],
        [0.3, 1.0, 0.4, 0.1, 0.2],
        [0.1, 0.4, 1.0, 0.3, 0.1],
        [0.2, 0.1, 0.3, 1.0, 0.2],
        [0.0, 0.2, 0.1, 0.2, 1.0]
    ])
    
    # Apply correlation structure
    L = np.linalg.cholesky(correlation_matrix)
    correlated_returns = base_returns @ L.T
    
    # Add different expected returns
    expected_returns = np.array([0.08, 0.10, 0.06, 0.12, 0.09]) / 252  # Daily returns
    correlated_returns += expected_returns
    
    strategy_names = [f"Strategy_{i+1}" for i in range(n_strategies)]
    
    return pd.DataFrame(
        correlated_returns,
        index=dates,
        columns=strategy_names
    )


@pytest.fixture
def optimization_config():
    """Create optimization configuration for testing."""
    return OptimizationConfig(
        optimization_method=OptimizationMethod.MEAN_VARIANCE,
        objective_function=ObjectiveFunction.MAX_SHARPE,
        lookback_days=252,
        risk_free_rate=Decimal("0.02"),
        solver="ECOS",
        enable_shrinkage=True
    )


@pytest.fixture
def portfolio_constraints():
    """Create portfolio constraints for testing."""
    return PortfolioConstraints(
        min_weight=Decimal("0.0"),
        max_weight=Decimal("0.4"),
        max_concentration=Decimal("0.3"),
        min_strategies=3,
        max_strategies=10,
        max_volatility=Decimal("0.15")
    )


class TestCovarianceEstimator:
    """Test covariance matrix estimation."""
    
    def test_sample_covariance(self, sample_strategy_data):
        """Test sample covariance estimation."""
        estimator = CovarianceEstimator("sample")
        returns = sample_strategy_data.pct_change().dropna()
        
        cov_matrix = estimator.estimate(returns)
        
        assert cov_matrix.shape == (5, 5)
        assert np.allclose(cov_matrix, cov_matrix.T)  # Symmetric
        assert np.all(np.linalg.eigvals(cov_matrix) > -1e-8)  # Positive semi-definite
    
    def test_ledoit_wolf_shrinkage(self, sample_strategy_data):
        """Test Ledoit-Wolf shrinkage estimation."""
        estimator = CovarianceEstimator("ledoit_wolf")
        returns = sample_strategy_data.pct_change().dropna()
        
        cov_matrix = estimator.estimate(returns)
        
        assert cov_matrix.shape == (5, 5)
        assert np.allclose(cov_matrix, cov_matrix.T)
        assert np.all(np.linalg.eigvals(cov_matrix) > -1e-8)
    
    def test_shrinkage_with_target(self, sample_strategy_data):
        """Test shrinkage with different targets."""
        estimator = CovarianceEstimator()
        returns = sample_strategy_data.pct_change().dropna()
        
        # Test different shrinkage targets
        targets = ["diagonal", "identity", "constant_correlation"]
        
        for target in targets:
            cov_matrix = estimator.estimate_with_shrinkage(
                returns, shrinkage_target=target, shrinkage_intensity=0.2
            )
            
            assert cov_matrix.shape == (5, 5)
            assert np.allclose(cov_matrix, cov_matrix.T)
            assert np.all(np.linalg.eigvals(cov_matrix) > -1e-8)


class TestMeanVarianceOptimizer:
    """Test mean-variance optimization."""
    
    def test_max_sharpe_optimization(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test maximum Sharpe ratio optimization."""
        optimizer = MeanVarianceOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        
        expected_returns = returns.mean().values * 252
        cov_matrix = returns.cov().values
        
        result = optimizer.optimize(
            expected_returns, cov_matrix, portfolio_constraints, ObjectiveFunction.MAX_SHARPE
        )
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert len(result["weights"]) == 5
        assert abs(np.sum(result["weights"]) - 1.0) < 0.001  # Weights sum to 1
        assert np.all(result["weights"] >= float(portfolio_constraints.min_weight))
        assert np.all(result["weights"] <= float(portfolio_constraints.max_weight))
        assert result["expected_sharpe"] > 0
    
    def test_min_variance_optimization(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test minimum variance optimization."""
        optimizer = MeanVarianceOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        
        expected_returns = returns.mean().values * 252
        cov_matrix = returns.cov().values
        
        result = optimizer.optimize(
            expected_returns, cov_matrix, portfolio_constraints, ObjectiveFunction.MIN_VARIANCE
        )
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert abs(np.sum(result["weights"]) - 1.0) < 0.001
        assert result["expected_volatility"] > 0
    
    def test_target_return_optimization(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test target return optimization."""
        optimizer = MeanVarianceOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        
        expected_returns = returns.mean().values * 252
        cov_matrix = returns.cov().values
        
        # Set target return constraint
        portfolio_constraints.custom_constraints = {"target_return": 0.08}
        
        result = optimizer.optimize(
            expected_returns, cov_matrix, portfolio_constraints, ObjectiveFunction.TARGET_RETURN
        )
        
        if result["status"] == "optimal":
            assert "weights" in result
            assert abs(np.sum(result["weights"]) - 1.0) < 0.001


class TestRiskParityOptimizer:
    """Test risk parity optimization."""
    
    def test_equal_risk_contribution(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test equal risk contribution optimization."""
        optimizer = RiskParityOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        cov_matrix = returns.cov().values
        
        result = optimizer.optimize(cov_matrix, portfolio_constraints)
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert len(result["weights"]) == 5
        assert abs(np.sum(result["weights"]) - 1.0) < 0.001
        assert "risk_contributions" in result
        
        # Check that risk contributions are approximately equal
        risk_contribs = result["risk_contributions"]
        target_contrib = 1.0 / len(risk_contribs)
        for contrib in risk_contribs:
            assert abs(contrib - target_contrib) < 0.1  # Allow some tolerance
    
    def test_custom_risk_budgets(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test risk parity with custom risk budgets."""
        optimizer = RiskParityOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        cov_matrix = returns.cov().values
        
        # Custom risk budgets
        custom_budgets = np.array([0.3, 0.25, 0.2, 0.15, 0.1])
        
        result = optimizer.optimize(cov_matrix, portfolio_constraints, custom_budgets)
        
        if result["status"] == "optimal":
            assert "weights" in result
            assert abs(np.sum(result["weights"]) - 1.0) < 0.001


class TestBlackLittermanOptimizer:
    """Test Black-Litterman optimization."""
    
    def test_black_litterman_no_views(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test Black-Litterman without views (should default to market equilibrium)."""
        optimizer = BlackLittermanOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        cov_matrix = returns.cov().values
        
        # Equal market caps
        market_caps = np.ones(5)
        views = {}
        
        result = optimizer.optimize(market_caps, cov_matrix, views, portfolio_constraints)
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert abs(np.sum(result["weights"]) - 1.0) < 0.001
        assert "implied_returns" in result
        assert "risk_aversion" in result
    
    def test_black_litterman_with_views(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test Black-Litterman with market views."""
        optimizer = BlackLittermanOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        cov_matrix = returns.cov().values
        
        market_caps = np.ones(5)
        
        # Add market views
        views = {
            "views": [
                {
                    "assets": [0, 1],  # First two strategies
                    "return": 0.10,    # Expected 10% return
                    "confidence": 0.8
                },
                {
                    "assets": [2],     # Third strategy
                    "return": 0.05,    # Expected 5% return
                    "confidence": 0.6
                }
            ]
        }
        
        result = optimizer.optimize(market_caps, cov_matrix, views, portfolio_constraints)
        
        if result["status"] == "optimal":
            assert "weights" in result
            assert abs(np.sum(result["weights"]) - 1.0) < 0.001
            assert "bl_returns" in result


class TestHierarchicalRiskParityOptimizer:
    """Test Hierarchical Risk Parity optimization."""
    
    def test_hrp_optimization(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test HRP optimization."""
        optimizer = HierarchicalRiskParityOptimizer(optimization_config)
        returns = sample_strategy_data.pct_change().dropna()
        cov_matrix = returns.cov().values
        
        result = optimizer.optimize(cov_matrix, portfolio_constraints)
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert len(result["weights"]) == 5
        assert abs(np.sum(result["weights"]) - 1.0) < 0.001
        assert "linkage_matrix" in result


class TestPortfolioOptimizer:
    """Test main portfolio optimizer."""
    
    def test_mean_variance_method(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test portfolio optimization with mean-variance method."""
        optimizer = PortfolioOptimizer()
        
        result = optimizer.optimize_portfolio(
            OptimizationMethod.MEAN_VARIANCE,
            sample_strategy_data,
            optimization_config,
            portfolio_constraints
        )
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert "optimization_method" in result
        assert result["optimization_method"] == "MEAN_VARIANCE"
        assert "diversification_metrics" in result
    
    def test_risk_parity_method(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test portfolio optimization with risk parity method."""
        optimizer = PortfolioOptimizer()
        
        result = optimizer.optimize_portfolio(
            OptimizationMethod.RISK_PARITY,
            sample_strategy_data,
            optimization_config,
            portfolio_constraints
        )
        
        assert result["status"] == "optimal"
        assert "weights" in result
        assert result["optimization_method"] == "RISK_PARITY"
    
    def test_insufficient_data(self, optimization_config, portfolio_constraints):
        """Test optimization with insufficient data."""
        optimizer = PortfolioOptimizer()
        
        # Create very small dataset
        small_data = pd.DataFrame({
            'Strategy_1': [0.01, 0.02],
            'Strategy_2': [0.015, 0.018]
        })
        
        result = optimizer.optimize_portfolio(
            OptimizationMethod.MEAN_VARIANCE,
            small_data,
            optimization_config,
            portfolio_constraints
        )
        
        assert result["status"] == "error"
        assert "Insufficient data" in result["error"]
    
    def test_diversification_metrics(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test diversification metrics calculation."""
        optimizer = PortfolioOptimizer()
        
        result = optimizer.optimize_portfolio(
            OptimizationMethod.MEAN_VARIANCE,
            sample_strategy_data,
            optimization_config,
            portfolio_constraints
        )
        
        if result["status"] == "optimal":
            div_metrics = result["diversification_metrics"]
            
            assert "effective_strategies" in div_metrics
            assert "concentration_index" in div_metrics
            assert "diversification_ratio" in div_metrics
            assert "correlation_avg" in div_metrics
            
            assert div_metrics["effective_strategies"] > 1
            assert 0 <= div_metrics["concentration_index"] <= 1
            assert div_metrics["diversification_ratio"] >= 1


@pytest.mark.integration
class TestOptimizationIntegration:
    """Integration tests for optimization engine."""
    
    def test_multiple_optimization_methods(self, sample_strategy_data, optimization_config, portfolio_constraints):
        """Test multiple optimization methods on same data."""
        optimizer = PortfolioOptimizer()
        
        methods = [
            OptimizationMethod.MEAN_VARIANCE,
            OptimizationMethod.RISK_PARITY,
            OptimizationMethod.MIN_VARIANCE
        ]
        
        results = {}
        
        for method in methods:
            result = optimizer.optimize_portfolio(
                method, sample_strategy_data, optimization_config, portfolio_constraints
            )
            results[method.value] = result
            
            if result["status"] == "optimal":
                assert "weights" in result
                assert abs(np.sum(result["weights"]) - 1.0) < 0.001
        
        # Compare results
        if all(r["status"] == "optimal" for r in results.values()):
            # Different methods should produce different allocations
            mv_weights = results["MEAN_VARIANCE"]["weights"]
            rp_weights = results["RISK_PARITY"]["weights"]
            
            # Weights should be different (not identical)
            assert not np.allclose(mv_weights, rp_weights, atol=0.01)
