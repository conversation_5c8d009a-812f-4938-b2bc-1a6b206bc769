"""
Health check router for Backtesting Engine service.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import psutil
import os

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "AthenaTrader Backtesting Engine",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "capabilities": [
            "high_fidelity_simulation",
            "market_friction_modeling",
            "performance_analytics",
            "robustness_testing",
            "monte_carlo_simulation",
            "walk_forward_analysis",
            "stress_testing"
        ]
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """Detailed health check with system and dependency status."""
    health_status = {
        "status": "healthy",
        "service": "AthenaTrader Backtesting Engine",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database connectivity check
    try:
        await db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Check for resource warnings
        warnings = []
        if cpu_percent > 90:
            warnings.append("High CPU usage")
        if memory.percent > 90:
            warnings.append("High memory usage")
        if disk.percent > 90:
            warnings.append("Low disk space")
        
        if warnings:
            health_status["checks"]["system"]["warnings"] = warnings
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "error",
            "message": f"Failed to get system metrics: {str(e)}"
        }
    
    # Simulation engines check
    try:
        # Check if results storage directory exists and is writable
        results_path = settings.RESULTS_STORAGE_PATH
        if os.path.exists(results_path) and os.access(results_path, os.W_OK):
            health_status["checks"]["simulation_engines"] = {
                "status": "healthy",
                "results_storage": "accessible",
                "results_storage_path": results_path,
                "max_concurrent_backtests": settings.MAX_CONCURRENT_BACKTESTS
            }
        else:
            health_status["checks"]["simulation_engines"] = {
                "status": "warning",
                "results_storage": "not accessible",
                "results_storage_path": results_path
            }
    except Exception as e:
        health_status["checks"]["simulation_engines"] = {
            "status": "error",
            "message": f"Simulation engines check failed: {str(e)}"
        }
    
    # External services check
    try:
        import httpx
        external_services = {
            "data_nexus": settings.DATA_NEXUS_URL,
            "strategy_genesis": settings.STRATEGY_GENESIS_URL
        }
        
        service_status = {}
        for service_name, service_url in external_services.items():
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{service_url}/health")
                    if response.status_code == 200:
                        service_status[service_name] = "healthy"
                    else:
                        service_status[service_name] = "unhealthy"
                        health_status["status"] = "degraded"
            except Exception:
                service_status[service_name] = "unreachable"
                health_status["status"] = "degraded"
        
        health_status["checks"]["external_services"] = {
            "status": "healthy" if all(s == "healthy" for s in service_status.values()) else "degraded",
            "services": service_status
        }
        
    except Exception as e:
        health_status["checks"]["external_services"] = {
            "status": "error",
            "message": f"External services check failed: {str(e)}"
        }
    
    return health_status


@router.get("/engines")
async def engines_status():
    """Check status of backtesting engines."""
    engines_status = {
        "timestamp": datetime.utcnow().isoformat(),
        "engines": {}
    }
    
    # Simulation Engine
    try:
        from app.engine.simulation import SimulationEngine
        engines_status["engines"]["simulation"] = {
            "status": "available",
            "config": {
                "default_spread_bps": float(settings.DEFAULT_SPREAD_BPS),
                "default_commission_bps": float(settings.DEFAULT_COMMISSION_BPS),
                "default_slippage_bps": float(settings.DEFAULT_SLIPPAGE_BPS),
                "market_impact_factor": float(settings.MARKET_IMPACT_FACTOR)
            }
        }
    except Exception as e:
        engines_status["engines"]["simulation"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Performance Analytics Engine
    try:
        from app.engine.performance import PerformanceAnalytics
        engines_status["engines"]["performance_analytics"] = {
            "status": "available",
            "config": {
                "risk_free_rate": float(settings.RISK_FREE_RATE),
                "trading_days_per_year": settings.TRADING_DAYS_PER_YEAR,
                "confidence_levels": settings.CONFIDENCE_LEVELS
            }
        }
    except Exception as e:
        engines_status["engines"]["performance_analytics"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Robustness Testing Framework
    try:
        from app.engine.robustness import RobustnessTestingFramework
        engines_status["engines"]["robustness_testing"] = {
            "status": "available",
            "config": {
                "mc_simulation_runs": settings.MC_SIMULATION_RUNS,
                "bootstrap_samples": settings.BOOTSTRAP_SAMPLES,
                "stress_test_scenarios": list(settings.STRESS_TEST_SCENARIOS.keys())
            }
        }
    except Exception as e:
        engines_status["engines"]["robustness_testing"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Strategy Integration
    try:
        from app.engine.strategy_integration import StrategyIntegrator
        engines_status["engines"]["strategy_integration"] = {
            "status": "available",
            "config": {
                "strategy_genesis_url": settings.STRATEGY_GENESIS_URL,
                "max_position_size": float(settings.MAX_POSITION_SIZE),
                "max_leverage": float(settings.MAX_LEVERAGE)
            }
        }
    except Exception as e:
        engines_status["engines"]["strategy_integration"] = {
            "status": "error",
            "message": str(e)
        }
    
    return engines_status


@router.get("/metrics")
async def get_metrics(db: AsyncSession = Depends(get_db)):
    """Get service metrics and statistics."""
    try:
        from app.models.backtest import Backtest, BacktestTrade
        from sqlalchemy import func, select
        from datetime import timedelta
        
        # Get backtest counts by status
        backtest_counts = await db.execute(
            select(Backtest.status, func.count(Backtest.id))
            .group_by(Backtest.status)
        )
        
        # Get recent activity
        recent_backtests = await db.execute(
            select(func.count(Backtest.id))
            .where(Backtest.created_at >= datetime.utcnow() - timedelta(days=7))
        )
        
        # Get trade statistics
        total_trades = await db.execute(
            select(func.count(BacktestTrade.id))
        )
        
        # Get performance statistics
        avg_performance = await db.execute(
            select(
                func.avg(Backtest.total_return),
                func.avg(Backtest.sharpe_ratio),
                func.avg(Backtest.max_drawdown)
            )
            .where(Backtest.status == "COMPLETED")
        )
        
        avg_stats = avg_performance.fetchone()
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "backtests": {
                "by_status": dict(backtest_counts.fetchall()),
                "recent_count": recent_backtests.scalar() or 0
            },
            "trades": {
                "total_count": total_trades.scalar() or 0
            },
            "performance": {
                "avg_total_return": float(avg_stats[0]) if avg_stats[0] else None,
                "avg_sharpe_ratio": float(avg_stats[1]) if avg_stats[1] else None,
                "avg_max_drawdown": float(avg_stats[2]) if avg_stats[2] else None
            },
            "system": {
                "uptime_seconds": psutil.boot_time(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "max_concurrent_backtests": settings.MAX_CONCURRENT_BACKTESTS
            },
            "configuration": {
                "default_spread_bps": float(settings.DEFAULT_SPREAD_BPS),
                "default_commission_bps": float(settings.DEFAULT_COMMISSION_BPS),
                "min_backtest_days": settings.MIN_BACKTEST_DAYS,
                "max_backtest_days": settings.MAX_BACKTEST_DAYS,
                "supported_timeframes": settings.SUPPORTED_TIMEFRAMES
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )
