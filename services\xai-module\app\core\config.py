"""
Configuration settings for XAI Module service.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, validator
from decimal import Decimal


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "AthenaTrader XAI Module"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Database
    DATABASE_URL: str = "postgresql://athena_user:athena_password@localhost:5432/athena_trader"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # External Services
    DATA_NEXUS_URL: str = "http://localhost:8001"
    STRATEGY_GENESIS_URL: str = "http://localhost:8002"
    BACKTESTING_ENGINE_URL: str = "http://localhost:8003"
    PORTFOLIO_CONSTRUCTION_URL: str = "http://localhost:8004"
    
    # SHAP Configuration
    SHAP_SAMPLE_SIZE: int = 1000  # Number of samples for SHAP explanations
    SHAP_MAX_EVALS: int = 2000  # Maximum evaluations for SHAP
    SHAP_BATCH_SIZE: int = 50  # Batch size for SHAP calculations
    SHAP_BACKGROUND_SIZE: int = 100  # Background dataset size for SHAP
    ENABLE_SHAP_CACHING: bool = True  # Cache SHAP explanations
    SHAP_CACHE_TTL_HOURS: int = 24  # SHAP cache time-to-live
    
    # LIME Configuration
    LIME_NUM_FEATURES: int = 10  # Number of features to explain with LIME
    LIME_NUM_SAMPLES: int = 5000  # Number of samples for LIME
    LIME_KERNEL_WIDTH: float = 0.25  # Kernel width for LIME
    LIME_FEATURE_SELECTION: str = "auto"  # Feature selection method
    ENABLE_LIME_CACHING: bool = True  # Cache LIME explanations
    
    # Feature Importance Configuration
    PERMUTATION_IMPORTANCE_REPEATS: int = 10  # Number of permutation repeats
    ABLATION_STUDY_ITERATIONS: int = 50  # Ablation study iterations
    FEATURE_IMPORTANCE_THRESHOLD: float = 0.01  # Minimum importance threshold
    MAX_FEATURES_TO_ANALYZE: int = 50  # Maximum features for importance analysis
    
    # Model Interpretability Configuration
    ENABLE_ATTENTION_VISUALIZATION: bool = True  # Enable attention mechanism visualization
    ENABLE_GRADIENT_ANALYSIS: bool = True  # Enable gradient-based analysis
    ENABLE_ACTIVATION_ANALYSIS: bool = True  # Enable activation analysis
    MAX_LAYERS_TO_ANALYZE: int = 10  # Maximum layers for deep model analysis
    
    # Performance Attribution Configuration
    ATTRIBUTION_LOOKBACK_DAYS: int = 252  # Lookback period for attribution
    ATTRIBUTION_MIN_TRADES: int = 10  # Minimum trades for attribution analysis
    FACTOR_ATTRIBUTION_MODELS: List[str] = ["FAMA_FRENCH", "CAPM", "CUSTOM"]
    REGIME_DETECTION_WINDOW: int = 60  # Window for regime detection
    ENABLE_TEMPORAL_ATTRIBUTION: bool = True  # Enable temporal attribution
    
    # Audit Trail Configuration
    ENABLE_COMPREHENSIVE_LOGGING: bool = True  # Enable comprehensive audit logging
    AUDIT_RETENTION_DAYS: int = 2555  # 7 years retention for compliance
    LOG_EXPLANATION_REQUESTS: bool = True  # Log all explanation requests
    LOG_MODEL_PREDICTIONS: bool = True  # Log model predictions
    LOG_FEATURE_IMPORTANCE: bool = True  # Log feature importance calculations
    ENABLE_BIAS_DETECTION: bool = True  # Enable bias detection
    
    # Compliance Configuration
    REGULATORY_FRAMEWORKS: List[str] = ["MIFID_II", "GDPR", "FINRA", "SEC"]
    ENABLE_MODEL_VALIDATION: bool = True  # Enable model validation reports
    VALIDATION_CONFIDENCE_LEVEL: float = 0.95  # Confidence level for validation
    ENABLE_FAIRNESS_ANALYSIS: bool = True  # Enable fairness analysis
    BIAS_DETECTION_THRESHOLD: float = 0.1  # Bias detection threshold
    
    # Visualization Configuration
    ENABLE_INTERACTIVE_DASHBOARDS: bool = True  # Enable interactive dashboards
    DASHBOARD_REFRESH_INTERVAL_SECONDS: int = 30  # Dashboard refresh interval
    MAX_VISUALIZATION_POINTS: int = 10000  # Maximum points in visualizations
    ENABLE_REAL_TIME_UPDATES: bool = True  # Enable real-time dashboard updates
    CHART_EXPORT_FORMATS: List[str] = ["PNG", "SVG", "PDF", "HTML"]
    
    # Performance and Caching
    ENABLE_EXPLANATION_CACHING: bool = True  # Cache explanations
    EXPLANATION_CACHE_TTL_HOURS: int = 12  # Explanation cache TTL
    MAX_CONCURRENT_EXPLANATIONS: int = 5  # Maximum concurrent explanations
    EXPLANATION_TIMEOUT_MINUTES: int = 15  # Explanation timeout
    ENABLE_ASYNC_EXPLANATIONS: bool = True  # Enable async explanation generation
    
    # Data Processing Configuration
    MAX_FEATURES_PER_EXPLANATION: int = 100  # Maximum features per explanation
    MIN_DATA_POINTS_FOR_EXPLANATION: int = 50  # Minimum data points
    FEATURE_CORRELATION_THRESHOLD: float = 0.95  # Feature correlation threshold
    OUTLIER_DETECTION_METHOD: str = "IQR"  # Outlier detection method
    ENABLE_DATA_PREPROCESSING: bool = True  # Enable data preprocessing
    
    # Model-Specific Configuration
    # Genetic Programming
    GP_TREE_VISUALIZATION_DEPTH: int = 5  # Maximum tree depth for visualization
    GP_FEATURE_IMPORTANCE_METHOD: str = "frequency"  # GP feature importance method
    
    # Reinforcement Learning
    RL_ACTION_EXPLANATION_WINDOW: int = 10  # Window for RL action explanations
    RL_STATE_IMPORTANCE_METHOD: str = "gradient"  # RL state importance method
    
    # Deep Learning
    DL_LAYER_WISE_ANALYSIS: bool = True  # Enable layer-wise analysis
    DL_GRADIENT_METHOD: str = "integrated_gradients"  # Gradient method for DL
    DL_ATTENTION_HEADS_TO_ANALYZE: int = 8  # Number of attention heads to analyze
    
    # API Configuration
    MAX_EXPLANATION_REQUESTS_PER_MINUTE: int = 60  # Rate limiting
    ENABLE_API_KEY_AUTHENTICATION: bool = False  # API key authentication
    API_RESPONSE_TIMEOUT_SECONDS: int = 300  # API response timeout
    
    # Storage Configuration
    EXPLANATIONS_STORAGE_PATH: str = "/app/explanations"
    VISUALIZATIONS_STORAGE_PATH: str = "/app/visualizations"
    AUDIT_LOGS_STORAGE_PATH: str = "/app/audit_logs"
    MODEL_ARTIFACTS_STORAGE_PATH: str = "/app/model_artifacts"
    
    # Monitoring and Alerting
    ENABLE_PERFORMANCE_MONITORING: bool = True  # Enable performance monitoring
    EXPLANATION_QUALITY_THRESHOLD: float = 0.8  # Explanation quality threshold
    ALERT_ON_EXPLANATION_FAILURES: bool = True  # Alert on explanation failures
    ALERT_ON_BIAS_DETECTION: bool = True  # Alert on bias detection
    MONITORING_METRICS_INTERVAL_SECONDS: int = 60  # Monitoring interval
    
    # Security Configuration
    ENABLE_EXPLANATION_ENCRYPTION: bool = True  # Encrypt stored explanations
    ENABLE_AUDIT_LOG_ENCRYPTION: bool = True  # Encrypt audit logs
    EXPLANATION_ACCESS_CONTROL: bool = True  # Enable access control
    SENSITIVE_FEATURE_MASKING: bool = True  # Mask sensitive features
    
    # Advanced Features
    ENABLE_COUNTERFACTUAL_EXPLANATIONS: bool = True  # Enable counterfactual explanations
    ENABLE_WHAT_IF_ANALYSIS: bool = True  # Enable what-if analysis
    ENABLE_CONCEPT_DRIFT_DETECTION: bool = True  # Enable concept drift detection
    CONCEPT_DRIFT_THRESHOLD: float = 0.1  # Concept drift threshold
    ENABLE_EXPLANATION_CONSISTENCY_CHECK: bool = True  # Check explanation consistency
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("EXPLANATIONS_STORAGE_PATH", "VISUALIZATIONS_STORAGE_PATH", 
              "AUDIT_LOGS_STORAGE_PATH", "MODEL_ARTIFACTS_STORAGE_PATH", pre=True)
    def create_storage_paths(cls, v):
        """Ensure storage paths exist."""
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator("SHAP_SAMPLE_SIZE", "LIME_NUM_SAMPLES", "PERMUTATION_IMPORTANCE_REPEATS")
    def validate_positive_integers(cls, v):
        """Validate positive integer values."""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    @validator("ATTRIBUTION_LOOKBACK_DAYS", "AUDIT_RETENTION_DAYS")
    def validate_retention_periods(cls, v):
        """Validate retention periods are reasonable."""
        if v < 1 or v > 3650:  # 1 day to 10 years
            raise ValueError("Retention period must be between 1 and 3650 days")
        return v
    
    @validator("FEATURE_IMPORTANCE_THRESHOLD", "BIAS_DETECTION_THRESHOLD", 
              "CONCEPT_DRIFT_THRESHOLD")
    def validate_thresholds(cls, v):
        """Validate threshold values are between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Threshold must be between 0 and 1")
        return v
    
    @validator("VALIDATION_CONFIDENCE_LEVEL")
    def validate_confidence_level(cls, v):
        """Validate confidence level is between 0 and 1."""
        if not 0 < v < 1:
            raise ValueError("Confidence level must be between 0 and 1")
        return v
    
    @validator("REGULATORY_FRAMEWORKS")
    def validate_regulatory_frameworks(cls, v):
        """Validate regulatory frameworks are supported."""
        supported_frameworks = ["MIFID_II", "GDPR", "FINRA", "SEC", "BASEL_III", "CFTC"]
        for framework in v:
            if framework not in supported_frameworks:
                raise ValueError(f"Unsupported regulatory framework: {framework}")
        return v
    
    @validator("FACTOR_ATTRIBUTION_MODELS")
    def validate_attribution_models(cls, v):
        """Validate attribution models are supported."""
        supported_models = ["FAMA_FRENCH", "CAPM", "CUSTOM", "APT", "BARRA"]
        for model in v:
            if model not in supported_models:
                raise ValueError(f"Unsupported attribution model: {model}")
        return v
    
    @validator("OUTLIER_DETECTION_METHOD")
    def validate_outlier_method(cls, v):
        """Validate outlier detection method is supported."""
        supported_methods = ["IQR", "Z_SCORE", "ISOLATION_FOREST", "LOCAL_OUTLIER_FACTOR"]
        if v not in supported_methods:
            raise ValueError(f"Unsupported outlier detection method: {v}")
        return v
    
    @validator("DL_GRADIENT_METHOD")
    def validate_gradient_method(cls, v):
        """Validate gradient method is supported."""
        supported_methods = ["integrated_gradients", "gradient_shap", "deeplift", "guided_backprop"]
        if v not in supported_methods:
            raise ValueError(f"Unsupported gradient method: {v}")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
