"""
Pydantic schemas for live execution operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class OrderSide(str, Enum):
    """Order side enumeration."""
    BUY = "BUY"
    SELL = "SELL"
    SHORT = "SHORT"
    COVER = "COVER"


class OrderType(str, Enum):
    """Order type enumeration."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"
    TRAILING_STOP = "TRAILING_STOP"
    ICEBERG = "ICEBERG"
    HIDDEN = "HIDDEN"


class TimeInForce(str, Enum):
    """Time in force enumeration."""
    DAY = "DAY"
    GTC = "GTC"  # Good Till Cancelled
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill
    GTD = "GTD"  # Good Till Date


class OrderStatus(str, Enum):
    """Order status enumeration."""
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    PARTIAL = "PARTIAL"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


class AssetClass(str, Enum):
    """Asset class enumeration."""
    EQUITY = "EQUITY"
    FUTURES = "FUTURES"
    FOREX = "FOREX"
    CRYPTO = "CRYPTO"
    OPTIONS = "OPTIONS"
    BONDS = "BONDS"


class ExecutionAlgorithm(str, Enum):
    """Execution algorithm enumeration."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    TWAP = "TWAP"  # Time Weighted Average Price
    VWAP = "VWAP"  # Volume Weighted Average Price
    IMPLEMENTATION_SHORTFALL = "IMPLEMENTATION_SHORTFALL"
    POV = "POV"    # Percentage of Volume
    ICEBERG = "ICEBERG"
    SNIPER = "SNIPER"


class RiskEventType(str, Enum):
    """Risk event type enumeration."""
    POSITION_LIMIT = "POSITION_LIMIT"
    LOSS_LIMIT = "LOSS_LIMIT"
    CONCENTRATION_LIMIT = "CONCENTRATION_LIMIT"
    LEVERAGE_LIMIT = "LEVERAGE_LIMIT"
    VOLATILITY_SPIKE = "VOLATILITY_SPIKE"
    CIRCUIT_BREAKER = "CIRCUIT_BREAKER"
    MARKET_DISRUPTION = "MARKET_DISRUPTION"
    OPERATIONAL_ERROR = "OPERATIONAL_ERROR"


class DeploymentType(str, Enum):
    """Deployment type enumeration."""
    FULL = "FULL"
    CANARY = "CANARY"
    A_B_TEST = "A_B_TEST"
    SHADOW = "SHADOW"
    BLUE_GREEN = "BLUE_GREEN"


class OrderRequest(BaseModel):
    """Order request schema."""
    
    # Strategy and identification
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    client_order_id: Optional[str] = Field(None, description="Client order ID")
    parent_order_id: Optional[uuid.UUID] = Field(None, description="Parent order ID")
    
    # Instrument details
    symbol: str = Field(..., description="Trading symbol")
    asset_class: AssetClass = Field(..., description="Asset class")
    exchange: Optional[str] = Field(None, description="Exchange")
    currency: str = Field(default="USD", description="Currency")
    
    # Order specifications
    side: OrderSide = Field(..., description="Order side")
    order_type: OrderType = Field(..., description="Order type")
    time_in_force: TimeInForce = Field(default=TimeInForce.DAY, description="Time in force")
    
    # Quantities and prices
    quantity: Decimal = Field(..., gt=0, description="Order quantity")
    price: Optional[Decimal] = Field(None, description="Limit price")
    stop_price: Optional[Decimal] = Field(None, description="Stop price")
    
    # Execution configuration
    execution_algo: ExecutionAlgorithm = Field(default=ExecutionAlgorithm.MARKET, description="Execution algorithm")
    algo_params: Optional[Dict[str, Any]] = Field(None, description="Algorithm parameters")
    
    # Broker preferences
    preferred_broker: Optional[str] = Field(None, description="Preferred broker")
    broker_account: Optional[str] = Field(None, description="Broker account")
    
    # Risk and compliance
    bypass_risk_checks: bool = Field(default=False, description="Bypass pre-trade risk checks")
    order_reason: Optional[str] = Field(None, description="Order reason")
    tags: Optional[Dict[str, Any]] = Field(None, description="Order tags")
    
    # Timing
    good_till_date: Optional[datetime] = Field(None, description="Good till date")
    
    @validator('price')
    def validate_price(cls, v, values):
        """Validate price based on order type."""
        order_type = values.get('order_type')
        if order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and v is None:
            raise ValueError(f"Price required for {order_type} orders")
        return v
    
    @validator('stop_price')
    def validate_stop_price(cls, v, values):
        """Validate stop price based on order type."""
        order_type = values.get('order_type')
        if order_type in [OrderType.STOP, OrderType.STOP_LIMIT, OrderType.TRAILING_STOP] and v is None:
            raise ValueError(f"Stop price required for {order_type} orders")
        return v


class OrderResponse(BaseModel):
    """Order response schema."""
    
    id: uuid.UUID = Field(..., description="Order ID")
    order_id: str = Field(..., description="Order ID")
    client_order_id: str = Field(..., description="Client order ID")
    
    # Strategy and user information
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    user_id: uuid.UUID = Field(..., description="User ID")
    
    # Instrument details
    symbol: str = Field(..., description="Trading symbol")
    asset_class: AssetClass = Field(..., description="Asset class")
    
    # Order specifications
    side: OrderSide = Field(..., description="Order side")
    order_type: OrderType = Field(..., description="Order type")
    time_in_force: TimeInForce = Field(..., description="Time in force")
    
    # Quantities and prices
    quantity: Decimal = Field(..., description="Order quantity")
    filled_quantity: Decimal = Field(..., description="Filled quantity")
    remaining_quantity: Decimal = Field(..., description="Remaining quantity")
    
    price: Optional[Decimal] = Field(None, description="Limit price")
    avg_fill_price: Optional[Decimal] = Field(None, description="Average fill price")
    
    # Status
    status: OrderStatus = Field(..., description="Order status")
    broker: str = Field(..., description="Broker")
    broker_order_id: Optional[str] = Field(None, description="Broker order ID")
    
    # Risk check
    risk_check_status: str = Field(..., description="Risk check status")
    
    # Timing
    created_at: datetime = Field(..., description="Creation timestamp")
    submitted_at: Optional[datetime] = Field(None, description="Submission timestamp")
    
    # Performance
    slippage: Optional[Decimal] = Field(None, description="Slippage")
    commission: Optional[Decimal] = Field(None, description="Commission")
    
    class Config:
        from_attributes = True


class ExecutionReport(BaseModel):
    """Execution report schema."""
    
    id: uuid.UUID = Field(..., description="Execution ID")
    execution_id: str = Field(..., description="Execution ID")
    order_id: uuid.UUID = Field(..., description="Order ID")
    
    # Execution details
    fill_quantity: Decimal = Field(..., description="Fill quantity")
    fill_price: Decimal = Field(..., description="Fill price")
    fill_value: Decimal = Field(..., description="Fill value")
    
    # Venue and liquidity
    execution_venue: Optional[str] = Field(None, description="Execution venue")
    liquidity_flag: Optional[str] = Field(None, description="Liquidity flag")
    
    # Timing
    execution_time: datetime = Field(..., description="Execution timestamp")
    order_to_execution_latency_ms: Optional[int] = Field(None, description="Order to execution latency")
    
    # Costs
    commission: Optional[Decimal] = Field(None, description="Commission")
    total_fees: Optional[Decimal] = Field(None, description="Total fees")
    
    # Market data
    bid_price: Optional[Decimal] = Field(None, description="Bid price at execution")
    ask_price: Optional[Decimal] = Field(None, description="Ask price at execution")
    spread: Optional[Decimal] = Field(None, description="Spread at execution")
    
    # Quality metrics
    price_improvement: Optional[Decimal] = Field(None, description="Price improvement")
    effective_spread: Optional[Decimal] = Field(None, description="Effective spread")
    
    class Config:
        from_attributes = True


class PositionSnapshot(BaseModel):
    """Position snapshot schema."""
    
    id: uuid.UUID = Field(..., description="Position ID")
    position_id: str = Field(..., description="Position ID")
    
    # Strategy and portfolio
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    portfolio_id: uuid.UUID = Field(..., description="Portfolio ID")
    
    # Instrument
    symbol: str = Field(..., description="Trading symbol")
    asset_class: AssetClass = Field(..., description="Asset class")
    
    # Position details
    quantity: Decimal = Field(..., description="Position quantity")
    market_value: Decimal = Field(..., description="Market value")
    notional_value: Decimal = Field(..., description="Notional value")
    
    # P&L
    avg_cost: Optional[Decimal] = Field(None, description="Average cost")
    unrealized_pnl: Decimal = Field(..., description="Unrealized P&L")
    realized_pnl: Decimal = Field(..., description="Realized P&L")
    total_pnl: Decimal = Field(..., description="Total P&L")
    
    # Risk metrics
    var_1d: Optional[Decimal] = Field(None, description="1-day VaR")
    beta: Optional[Decimal] = Field(None, description="Beta")
    
    # Market data
    last_price: Optional[Decimal] = Field(None, description="Last price")
    
    # Status
    status: str = Field(..., description="Position status")
    position_type: str = Field(..., description="Position type")
    
    # Timing
    opened_at: datetime = Field(..., description="Position opened timestamp")
    last_updated: datetime = Field(..., description="Last updated timestamp")
    
    class Config:
        from_attributes = True


class RiskAlert(BaseModel):
    """Risk alert schema."""
    
    id: uuid.UUID = Field(..., description="Risk event ID")
    event_id: str = Field(..., description="Event ID")
    
    # Classification
    event_type: RiskEventType = Field(..., description="Event type")
    severity: str = Field(..., description="Severity level")
    category: str = Field(..., description="Risk category")
    
    # Associated entities
    strategy_id: Optional[uuid.UUID] = Field(None, description="Strategy ID")
    portfolio_id: Optional[uuid.UUID] = Field(None, description="Portfolio ID")
    
    # Risk details
    risk_metric: str = Field(..., description="Risk metric")
    threshold_value: Optional[Decimal] = Field(None, description="Threshold value")
    actual_value: Optional[Decimal] = Field(None, description="Actual value")
    breach_amount: Optional[Decimal] = Field(None, description="Breach amount")
    
    # Description
    title: str = Field(..., description="Alert title")
    description: Optional[str] = Field(None, description="Alert description")
    
    # Actions
    action_taken: Optional[str] = Field(None, description="Action taken")
    auto_resolved: bool = Field(..., description="Auto resolved flag")
    
    # Status
    status: str = Field(..., description="Alert status")
    
    # Timing
    detected_at: datetime = Field(..., description="Detection timestamp")
    
    class Config:
        from_attributes = True


class StrategyDeploymentRequest(BaseModel):
    """Strategy deployment request schema."""
    
    # Strategy information
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    strategy_version: str = Field(..., description="Strategy version")
    
    # Deployment configuration
    deployment_type: DeploymentType = Field(..., description="Deployment type")
    traffic_percentage: Decimal = Field(default=Decimal("100.0"), ge=0, le=100, description="Traffic percentage")
    target_allocation: Optional[Decimal] = Field(None, description="Target allocation")
    
    # Environment
    environment: str = Field(default="PRODUCTION", description="Deployment environment")
    deployment_region: Optional[str] = Field(None, description="Deployment region")
    
    # Configuration
    deployment_config: Optional[Dict[str, Any]] = Field(None, description="Deployment configuration")
    
    @validator('traffic_percentage')
    def validate_traffic_percentage(cls, v, values):
        """Validate traffic percentage for canary deployments."""
        deployment_type = values.get('deployment_type')
        if deployment_type == DeploymentType.CANARY and v > 50:
            raise ValueError("Canary deployments should not exceed 50% traffic")
        return v


class StrategyDeploymentResponse(BaseModel):
    """Strategy deployment response schema."""
    
    id: uuid.UUID = Field(..., description="Deployment ID")
    deployment_id: str = Field(..., description="Deployment ID")
    
    # Strategy information
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    strategy_version: str = Field(..., description="Strategy version")
    strategy_name: str = Field(..., description="Strategy name")
    ai_paradigm: str = Field(..., description="AI paradigm")
    
    # Deployment configuration
    deployment_type: DeploymentType = Field(..., description="Deployment type")
    traffic_percentage: Decimal = Field(..., description="Traffic percentage")
    target_allocation: Optional[Decimal] = Field(None, description="Target allocation")
    
    # Status
    status: str = Field(..., description="Deployment status")
    health_status: str = Field(..., description="Health status")
    
    # Performance
    current_allocation: Optional[Decimal] = Field(None, description="Current allocation")
    total_pnl: Decimal = Field(..., description="Total P&L")
    daily_pnl: Decimal = Field(..., description="Daily P&L")
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    
    # Risk
    current_var: Optional[Decimal] = Field(None, description="Current VaR")
    risk_score: Optional[Decimal] = Field(None, description="Risk score")
    
    # Timing
    deployed_at: Optional[datetime] = Field(None, description="Deployment timestamp")
    last_health_check: Optional[datetime] = Field(None, description="Last health check")
    
    class Config:
        from_attributes = True


class ExecutionMetricsReport(BaseModel):
    """Execution metrics report schema."""
    
    # Time period
    metric_date: datetime = Field(..., description="Metric date")
    period_type: str = Field(..., description="Period type")
    
    # Scope
    strategy_id: Optional[uuid.UUID] = Field(None, description="Strategy ID")
    broker: Optional[str] = Field(None, description="Broker")
    asset_class: Optional[AssetClass] = Field(None, description="Asset class")
    
    # Volume metrics
    total_orders: int = Field(..., description="Total orders")
    filled_orders: int = Field(..., description="Filled orders")
    total_volume: Decimal = Field(..., description="Total volume")
    
    # Performance metrics
    fill_rate: Optional[Decimal] = Field(None, description="Fill rate")
    avg_fill_time_seconds: Optional[Decimal] = Field(None, description="Average fill time")
    avg_slippage_bps: Optional[Decimal] = Field(None, description="Average slippage in bps")
    
    # Latency metrics
    avg_order_latency_ms: Optional[Decimal] = Field(None, description="Average order latency")
    p95_order_latency_ms: Optional[Decimal] = Field(None, description="95th percentile latency")
    
    # Cost metrics
    total_commission: Decimal = Field(..., description="Total commission")
    avg_commission_bps: Optional[Decimal] = Field(None, description="Average commission in bps")
    
    class Config:
        from_attributes = True


class MarketDataSnapshot(BaseModel):
    """Market data snapshot schema."""
    
    symbol: str = Field(..., description="Trading symbol")
    asset_class: AssetClass = Field(..., description="Asset class")
    exchange: Optional[str] = Field(None, description="Exchange")
    
    # Price data
    last_price: Optional[Decimal] = Field(None, description="Last price")
    bid_price: Optional[Decimal] = Field(None, description="Bid price")
    ask_price: Optional[Decimal] = Field(None, description="Ask price")
    bid_size: Optional[Decimal] = Field(None, description="Bid size")
    ask_size: Optional[Decimal] = Field(None, description="Ask size")
    
    # Daily statistics
    open_price: Optional[Decimal] = Field(None, description="Open price")
    high_price: Optional[Decimal] = Field(None, description="High price")
    low_price: Optional[Decimal] = Field(None, description="Low price")
    close_price: Optional[Decimal] = Field(None, description="Close price")
    
    # Volume and activity
    volume: Optional[Decimal] = Field(None, description="Volume")
    avg_volume: Optional[Decimal] = Field(None, description="Average volume")
    
    # Derived metrics
    spread: Optional[Decimal] = Field(None, description="Bid-ask spread")
    spread_bps: Optional[Decimal] = Field(None, description="Spread in basis points")
    volatility: Optional[Decimal] = Field(None, description="Volatility")
    
    # Timing
    timestamp: datetime = Field(..., description="Data timestamp")
    market_status: str = Field(..., description="Market status")
    
    class Config:
        from_attributes = True


class BrokerConnection(BaseModel):
    """Broker connection schema."""
    
    broker_name: str = Field(..., description="Broker name")
    connection_type: str = Field(..., description="Connection type")  # FIX, REST, WEBSOCKET
    status: str = Field(..., description="Connection status")
    
    # Connection details
    host: Optional[str] = Field(None, description="Host")
    port: Optional[int] = Field(None, description="Port")
    account: Optional[str] = Field(None, description="Account")
    
    # Capabilities
    supported_asset_classes: List[AssetClass] = Field(..., description="Supported asset classes")
    supported_order_types: List[OrderType] = Field(..., description="Supported order types")
    
    # Performance
    avg_latency_ms: Optional[Decimal] = Field(None, description="Average latency")
    uptime_percentage: Optional[Decimal] = Field(None, description="Uptime percentage")
    
    # Status
    last_heartbeat: Optional[datetime] = Field(None, description="Last heartbeat")
    connected_at: Optional[datetime] = Field(None, description="Connected timestamp")
    
    class Config:
        from_attributes = True
