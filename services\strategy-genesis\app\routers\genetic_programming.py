"""
Genetic Programming router for GP-based strategy development.
"""

import logging
from typing import Dict, Any, Optional
import uuid
import asyncio
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import httpx
import pandas as pd

from app.core.database import get_db
from app.core.config import settings
from app.models.strategy import Strategy, StrategyTrainingRun
from app.schemas.strategy import (
    GeneticProgrammingConfig,
    TrainingRunCreate,
    TrainingRun as TrainingRunSchema,
    TrainingProgress
)
from app.ai.genetic_programming import GeneticProgrammingEngine

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


async def fetch_market_data(instrument_ids: list, start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """Fetch market data from Data Nexus service."""
    try:
        async with httpx.AsyncClient() as client:
            # Get instrument data
            instruments_data = []
            for instrument_id in instrument_ids:
                response = await client.get(
                    f"{settings.DATA_NEXUS_URL}/market-data/",
                    params={
                        "instrument_id": str(instrument_id),
                        "timeframe": "1h",  # Use hourly data for training
                        "start_time": start_date.isoformat(),
                        "end_time": end_date.isoformat(),
                        "limit": 10000
                    }
                )
                response.raise_for_status()
                data = response.json()
                
                if data:
                    df = pd.DataFrame(data)
                    df['time'] = pd.to_datetime(df['time'])
                    df = df.sort_values('time')
                    instruments_data.append(df)
            
            if not instruments_data:
                raise ValueError("No market data available")
            
            # For now, use the first instrument's data
            # TODO: Implement multi-instrument strategies
            market_data = instruments_data[0]
            
            # Ensure required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in market_data.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            return market_data
            
    except Exception as e:
        logger.error(f"Failed to fetch market data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch market data: {str(e)}"
        )


async def run_gp_training(
    training_run_id: uuid.UUID,
    strategy_id: uuid.UUID,
    config: GeneticProgrammingConfig,
    market_data: pd.DataFrame,
    db: AsyncSession
):
    """Run GP training in background."""
    try:
        # Update training run status
        stmt = select(StrategyTrainingRun).where(StrategyTrainingRun.id == training_run_id)
        result = await db.execute(stmt)
        training_run = result.scalar_one()
        
        training_run.status = "RUNNING"
        training_run.start_time = datetime.utcnow()
        await db.commit()
        
        # Initialize GP engine
        gp_engine = GeneticProgrammingEngine(config)
        
        # Progress callback
        async def progress_callback(generation: int, total_generations: int, stats: Dict[str, Any]):
            training_run.current_generation = generation
            training_run.total_generations = total_generations
            training_run.training_metrics = {
                "generation": generation,
                "best_fitness": float(stats.get("max", 0)),
                "avg_fitness": float(stats.get("avg", 0)),
                "std_fitness": float(stats.get("std", 0))
            }
            
            # Estimate completion time
            if generation > 0:
                elapsed = (datetime.utcnow() - training_run.start_time).total_seconds()
                estimated_total = elapsed * total_generations / generation
                training_run.estimated_completion = training_run.start_time + timedelta(seconds=estimated_total)
            
            await db.commit()
        
        # Run evolution
        result = gp_engine.evolve_strategy(market_data, progress_callback)
        
        # Update strategy with results
        stmt = select(Strategy).where(Strategy.id == strategy_id)
        strategy_result = await db.execute(stmt)
        strategy = strategy_result.scalar_one()
        
        strategy.status = "TRAINED"
        strategy.trained_at = datetime.utcnow()
        strategy.trading_rules = {"expression": result["best_individual"]}
        strategy.performance_metrics = result["performance_metrics"]
        strategy.performance_score = result["best_fitness"]
        
        # Save model
        model_path = f"{settings.MODEL_STORAGE_PATH}/gp_strategy_{strategy_id}.pkl"
        gp_engine.save_strategy(result, model_path)
        strategy.model_path = model_path
        
        # Update training run
        training_run.status = "COMPLETED"
        training_run.end_time = datetime.utcnow()
        training_run.training_duration_seconds = int((training_run.end_time - training_run.start_time).total_seconds())
        training_run.training_metrics = result
        training_run.best_performance = result["best_fitness"]
        
        await db.commit()
        
        logger.info(f"GP training completed for strategy {strategy_id}")
        
    except Exception as e:
        logger.error(f"GP training failed for strategy {strategy_id}: {e}")
        
        # Update training run with error
        training_run.status = "FAILED"
        training_run.end_time = datetime.utcnow()
        training_run.error_message = str(e)
        await db.commit()


@router.post("/train/{strategy_id}")
async def train_gp_strategy(
    strategy_id: uuid.UUID,
    training_config: TrainingRunCreate,
    background_tasks: BackgroundTasks,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Start GP training for a strategy.
    
    Args:
        strategy_id: Strategy ID to train
        training_config: Training configuration
        background_tasks: FastAPI background tasks
        user_id: Current user ID
        db: Database session
        
    Returns:
        dict: Training run information
        
    Raises:
        HTTPException: If strategy not found or training fails to start
    """
    # Verify strategy ownership and paradigm
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    if strategy.ai_paradigm != "GP":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Strategy is not a Genetic Programming strategy"
        )
    
    # Create training run
    training_run = StrategyTrainingRun(
        strategy_id=strategy_id,
        training_config=training_config.training_config,
        hyperparameters=training_config.hyperparameters,
        data_start_date=training_config.data_start_date,
        data_end_date=training_config.data_end_date,
        instruments_used=training_config.instruments_used,
        total_epochs=training_config.total_epochs,
        total_generations=training_config.total_generations,
        status="PENDING"
    )
    
    db.add(training_run)
    await db.commit()
    await db.refresh(training_run)
    
    try:
        # Fetch market data
        market_data = await fetch_market_data(
            training_config.instruments_used,
            training_config.data_start_date,
            training_config.data_end_date
        )
        
        # Create GP configuration
        gp_config = GeneticProgrammingConfig(**training_config.hyperparameters)
        
        # Start training in background
        background_tasks.add_task(
            run_gp_training,
            training_run.id,
            strategy_id,
            gp_config,
            market_data,
            db
        )
        
        logger.info(f"Started GP training for strategy {strategy_id}")
        
        return {
            "training_run_id": training_run.id,
            "status": "PENDING",
            "message": "GP training started successfully"
        }
        
    except Exception as e:
        # Update training run with error
        training_run.status = "FAILED"
        training_run.error_message = str(e)
        await db.commit()
        
        logger.error(f"Failed to start GP training: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start training: {str(e)}"
        )


@router.get("/training-run/{training_run_id}", response_model=TrainingRunSchema)
async def get_gp_training_run(
    training_run_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Get GP training run status and progress.
    
    Args:
        training_run_id: Training run ID
        user_id: Current user ID
        db: Database session
        
    Returns:
        TrainingRunSchema: Training run information
        
    Raises:
        HTTPException: If training run not found or access denied
    """
    # Get training run with strategy verification
    stmt = (
        select(StrategyTrainingRun)
        .join(Strategy)
        .where(
            and_(
                StrategyTrainingRun.id == training_run_id,
                Strategy.user_id == user_id
            )
        )
    )
    result = await db.execute(stmt)
    training_run = result.scalar_one_or_none()
    
    if not training_run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Training run not found"
        )
    
    return TrainingRunSchema.from_orm(training_run)


@router.post("/predict/{strategy_id}")
async def predict_gp_signal(
    strategy_id: uuid.UUID,
    market_data: Dict[str, Any],
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate trading signal using trained GP strategy.
    
    Args:
        strategy_id: Strategy ID
        market_data: Current market data
        user_id: Current user ID
        db: Database session
        
    Returns:
        dict: Trading signal and metadata
        
    Raises:
        HTTPException: If strategy not found or not trained
    """
    # Verify strategy ownership and status
    stmt = select(Strategy).where(
        and_(Strategy.id == strategy_id, Strategy.user_id == user_id)
    )
    result = await db.execute(stmt)
    strategy = result.scalar_one_or_none()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    if strategy.status != "TRAINED" or not strategy.model_path:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Strategy is not trained"
        )
    
    try:
        # Load GP engine and strategy
        gp_engine = GeneticProgrammingEngine()
        strategy_data = gp_engine.load_strategy(strategy.model_path)
        
        # Convert market data to DataFrame
        df = pd.DataFrame([market_data])
        
        # Generate signal
        signal = gp_engine.predict_signal(df, strategy_data["best_individual"])
        
        return {
            "signal": signal,
            "strategy_id": strategy_id,
            "timestamp": datetime.utcnow().isoformat(),
            "confidence": abs(signal),  # Use absolute value as confidence
            "metadata": {
                "ai_paradigm": "GP",
                "strategy_name": strategy.name,
                "performance_score": float(strategy.performance_score) if strategy.performance_score else None
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to generate GP signal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate signal: {str(e)}"
        )
