body {
  margin: 0;
  font-family: 'Roboto Mono', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0e1a;
  color: #ffffff;
}

code {
  font-family: 'Roboto Mono', 'Courier New', monospace;
}

* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1f2e;
}

::-webkit-scrollbar-thumb {
  background: #2a3441;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a4451;
}

/* Loading animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 2s infinite;
}
