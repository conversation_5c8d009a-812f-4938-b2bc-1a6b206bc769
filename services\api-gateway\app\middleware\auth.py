"""
Authentication middleware for request processing.
"""

import logging
from typing import Optional

from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.auth import verify_token, get_user_id_from_token

logger = logging.getLogger(__name__)

# Security scheme for Bearer token
security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware for handling authentication."""
    
    # Paths that don't require authentication
    EXEMPT_PATHS = {
        "/",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/health",
        "/health/",
        "/health/ready",
        "/health/live",
        "/auth/login",
        "/auth/register",
        "/info",
    }
    
    async def dispatch(self, request: Request, call_next):
        """Process request through authentication middleware."""
        
        # Skip authentication for exempt paths
        if request.url.path in self.EXEMPT_PATHS:
            return await call_next(request)
        
        # Skip authentication for OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return await call_next(request)
        
        # Extract token from Authorization header
        authorization: Optional[str] = request.headers.get("Authorization")
        
        if not authorization:
            return self._unauthorized_response("Missing authorization header")
        
        try:
            # Parse Bearer token
            scheme, token = authorization.split(" ", 1)
            if scheme.lower() != "bearer":
                return self._unauthorized_response("Invalid authorization scheme")
            
            # Verify token and extract user information
            payload = verify_token(token)
            user_id = get_user_id_from_token(token)
            
            # Add user information to request state
            request.state.user_id = user_id
            request.state.user_payload = payload
            
            logger.debug(f"Authenticated user: {user_id}")
            
        except HTTPException as e:
            return self._unauthorized_response(e.detail)
        except ValueError as e:
            return self._unauthorized_response(f"Invalid token format: {str(e)}")
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return self._unauthorized_response("Authentication failed")
        
        # Continue with the request
        response = await call_next(request)
        return response
    
    def _unauthorized_response(self, detail: str) -> Response:
        """Return unauthorized response."""
        return Response(
            content=f'{{"error": {{"code": 401, "message": "{detail}"}}}}',
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"},
            media_type="application/json"
        )
