"""
Execution quality analytics with market microstructure analysis.
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
import numpy as np
import pandas as pd
from collections import defaultdict, deque

from app.core.config import settings
from app.schemas.execution import (
    ExecutionReport, OrderResponse, ExecutionMetricsReport, 
    AssetClass, OrderType, MarketDataSnapshot
)

logger = logging.getLogger(__name__)


class ExecutionQualityAnalyzer:
    """Analyze execution quality metrics."""
    
    def __init__(self):
        """Initialize execution quality analyzer."""
        self.execution_history = deque(maxlen=10000)
        self.market_data_cache = {}
        
    def analyze_execution(
        self,
        execution: ExecutionReport,
        order: OrderResponse,
        market_data: Optional[MarketDataSnapshot] = None
    ) -> Dict[str, Any]:
        """Analyze individual execution quality."""
        try:
            analysis = {
                "execution_id": execution.execution_id,
                "order_id": str(execution.order_id),
                "symbol": order.symbol,
                "execution_time": execution.execution_time,
                "metrics": {}
            }
            
            # Basic execution metrics
            analysis["metrics"]["fill_quantity"] = float(execution.fill_quantity)
            analysis["metrics"]["fill_price"] = float(execution.fill_price)
            analysis["metrics"]["fill_value"] = float(execution.fill_value)
            
            # Latency analysis
            if execution.order_to_execution_latency_ms:
                analysis["metrics"]["latency_ms"] = execution.order_to_execution_latency_ms
                analysis["metrics"]["latency_category"] = self._categorize_latency(
                    execution.order_to_execution_latency_ms
                )
            
            # Market data analysis
            if market_data:
                market_analysis = self._analyze_market_conditions(execution, market_data)
                analysis["metrics"].update(market_analysis)
            
            # Price improvement analysis
            if execution.price_improvement:
                analysis["metrics"]["price_improvement_bps"] = float(execution.price_improvement) * 10000
            
            # Effective spread analysis
            if execution.effective_spread:
                analysis["metrics"]["effective_spread_bps"] = float(execution.effective_spread) * 10000
            
            # Cost analysis
            total_cost = Decimal("0")
            if execution.commission:
                total_cost += execution.commission
            if execution.total_fees:
                total_cost += execution.total_fees
            
            if total_cost > 0:
                cost_bps = (total_cost / execution.fill_value) * 10000
                analysis["metrics"]["total_cost_bps"] = float(cost_bps)
            
            # Store for historical analysis
            self.execution_history.append(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Execution analysis failed: {e}")
            return {"error": str(e)}
    
    def _categorize_latency(self, latency_ms: int) -> str:
        """Categorize execution latency."""
        if latency_ms <= 10:
            return "EXCELLENT"
        elif latency_ms <= 50:
            return "GOOD"
        elif latency_ms <= 100:
            return "ACCEPTABLE"
        elif latency_ms <= 500:
            return "POOR"
        else:
            return "UNACCEPTABLE"
    
    def _analyze_market_conditions(
        self,
        execution: ExecutionReport,
        market_data: MarketDataSnapshot
    ) -> Dict[str, Any]:
        """Analyze market conditions at execution time."""
        analysis = {}
        
        # Spread analysis
        if market_data.bid_price and market_data.ask_price:
            spread = market_data.ask_price - market_data.bid_price
            mid_price = (market_data.bid_price + market_data.ask_price) / 2
            
            analysis["market_spread"] = float(spread)
            analysis["market_spread_bps"] = float(spread / mid_price * 10000)
            analysis["mid_price"] = float(mid_price)
            
            # Price relative to mid
            price_vs_mid = (execution.fill_price - mid_price) / mid_price
            analysis["price_vs_mid_bps"] = float(price_vs_mid * 10000)
        
        # Volume analysis
        if market_data.volume and market_data.avg_volume:
            volume_ratio = market_data.volume / market_data.avg_volume
            analysis["volume_ratio"] = float(volume_ratio)
            
            if volume_ratio > 2.0:
                analysis["volume_condition"] = "HIGH"
            elif volume_ratio < 0.5:
                analysis["volume_condition"] = "LOW"
            else:
                analysis["volume_condition"] = "NORMAL"
        
        # Volatility analysis
        if market_data.volatility:
            analysis["market_volatility"] = float(market_data.volatility)
            
            if market_data.volatility > 0.3:
                analysis["volatility_condition"] = "HIGH"
            elif market_data.volatility < 0.1:
                analysis["volatility_condition"] = "LOW"
            else:
                analysis["volatility_condition"] = "NORMAL"
        
        return analysis
    
    def get_execution_summary(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        symbol: Optional[str] = None,
        broker: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get execution quality summary."""
        try:
            # Filter executions
            filtered_executions = self._filter_executions(start_time, end_time, symbol, broker)
            
            if not filtered_executions:
                return {"error": "No executions found for criteria"}
            
            # Calculate summary metrics
            summary = {
                "period": {
                    "start": start_time.isoformat() if start_time else None,
                    "end": end_time.isoformat() if end_time else None
                },
                "total_executions": len(filtered_executions),
                "total_volume": 0,
                "metrics": {}
            }
            
            # Aggregate metrics
            latencies = []
            slippages = []
            costs = []
            price_improvements = []
            
            for execution in filtered_executions:
                metrics = execution.get("metrics", {})
                
                summary["total_volume"] += metrics.get("fill_value", 0)
                
                if "latency_ms" in metrics:
                    latencies.append(metrics["latency_ms"])
                
                if "slippage_bps" in metrics:
                    slippages.append(metrics["slippage_bps"])
                
                if "total_cost_bps" in metrics:
                    costs.append(metrics["total_cost_bps"])
                
                if "price_improvement_bps" in metrics:
                    price_improvements.append(metrics["price_improvement_bps"])
            
            # Calculate statistics
            if latencies:
                summary["metrics"]["avg_latency_ms"] = np.mean(latencies)
                summary["metrics"]["p95_latency_ms"] = np.percentile(latencies, 95)
                summary["metrics"]["p99_latency_ms"] = np.percentile(latencies, 99)
            
            if slippages:
                summary["metrics"]["avg_slippage_bps"] = np.mean(slippages)
                summary["metrics"]["median_slippage_bps"] = np.median(slippages)
            
            if costs:
                summary["metrics"]["avg_cost_bps"] = np.mean(costs)
                summary["metrics"]["total_cost"] = summary["total_volume"] * np.mean(costs) / 10000
            
            if price_improvements:
                summary["metrics"]["avg_price_improvement_bps"] = np.mean(price_improvements)
                summary["metrics"]["price_improvement_rate"] = len([x for x in price_improvements if x > 0]) / len(price_improvements)
            
            return summary
            
        except Exception as e:
            logger.error(f"Execution summary failed: {e}")
            return {"error": str(e)}
    
    def _filter_executions(
        self,
        start_time: Optional[datetime],
        end_time: Optional[datetime],
        symbol: Optional[str],
        broker: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Filter executions by criteria."""
        filtered = []
        
        for execution in self.execution_history:
            # Time filter
            if start_time and execution["execution_time"] < start_time:
                continue
            if end_time and execution["execution_time"] > end_time:
                continue
            
            # Symbol filter
            if symbol and execution.get("symbol") != symbol:
                continue
            
            # Broker filter (would need to be added to execution data)
            if broker and execution.get("broker") != broker:
                continue
            
            filtered.append(execution)
        
        return filtered


class MarketMicrostructureAnalyzer:
    """Analyze market microstructure for optimal execution."""
    
    def __init__(self):
        """Initialize market microstructure analyzer."""
        self.order_book_history = defaultdict(deque)
        self.trade_history = defaultdict(deque)
        self.market_impact_models = {}
        
    def update_order_book(self, symbol: str, order_book_data: Dict[str, Any]):
        """Update order book data for symbol."""
        timestamp = datetime.utcnow()
        
        order_book_entry = {
            "timestamp": timestamp,
            "bid_price": order_book_data.get("bid_price"),
            "ask_price": order_book_data.get("ask_price"),
            "bid_size": order_book_data.get("bid_size"),
            "ask_size": order_book_data.get("ask_size"),
            "spread": order_book_data.get("spread")
        }
        
        # Keep last 1000 entries
        self.order_book_history[symbol].append(order_book_entry)
        if len(self.order_book_history[symbol]) > 1000:
            self.order_book_history[symbol].popleft()
    
    def analyze_market_impact(
        self,
        symbol: str,
        order_size: Decimal,
        order_type: OrderType
    ) -> Dict[str, Any]:
        """Analyze expected market impact."""
        try:
            if symbol not in self.order_book_history:
                return {"error": "No order book data available"}
            
            recent_data = list(self.order_book_history[symbol])[-10:]  # Last 10 entries
            
            if not recent_data:
                return {"error": "Insufficient order book data"}
            
            # Calculate average spread
            spreads = [entry["spread"] for entry in recent_data if entry["spread"]]
            avg_spread = np.mean(spreads) if spreads else 0
            
            # Calculate average depth
            bid_sizes = [entry["bid_size"] for entry in recent_data if entry["bid_size"]]
            ask_sizes = [entry["ask_size"] for entry in recent_data if entry["ask_size"]]
            avg_depth = (np.mean(bid_sizes) + np.mean(ask_sizes)) / 2 if bid_sizes and ask_sizes else 0
            
            # Estimate market impact
            if avg_depth > 0:
                participation_rate = float(order_size) / avg_depth
                
                # Simple linear impact model
                impact_bps = participation_rate * avg_spread * 0.5 * 10000
                
                # Adjust for order type
                if order_type == OrderType.MARKET:
                    impact_bps *= 1.5  # Market orders have higher impact
                elif order_type == OrderType.LIMIT:
                    impact_bps *= 0.8  # Limit orders have lower impact
                
                return {
                    "expected_impact_bps": impact_bps,
                    "participation_rate": participation_rate,
                    "avg_spread_bps": avg_spread * 10000,
                    "avg_depth": avg_depth,
                    "confidence": min(len(recent_data) / 10, 1.0)
                }
            else:
                return {"error": "Insufficient depth data"}
                
        except Exception as e:
            logger.error(f"Market impact analysis failed: {e}")
            return {"error": str(e)}
    
    def recommend_execution_strategy(
        self,
        symbol: str,
        order_size: Decimal,
        urgency: str = "NORMAL"
    ) -> Dict[str, Any]:
        """Recommend optimal execution strategy."""
        try:
            # Analyze current market conditions
            impact_analysis = self.analyze_market_impact(symbol, order_size, OrderType.MARKET)
            
            if "error" in impact_analysis:
                return impact_analysis
            
            recommendations = {
                "symbol": symbol,
                "order_size": float(order_size),
                "urgency": urgency,
                "recommendations": []
            }
            
            impact_bps = impact_analysis["expected_impact_bps"]
            participation_rate = impact_analysis["participation_rate"]
            
            # Recommend based on impact and urgency
            if urgency == "HIGH":
                if impact_bps < 5:
                    recommendations["recommendations"].append({
                        "strategy": "MARKET",
                        "reason": "Low impact, high urgency - use market order",
                        "expected_impact_bps": impact_bps
                    })
                else:
                    recommendations["recommendations"].append({
                        "strategy": "ICEBERG",
                        "reason": "High impact, high urgency - use iceberg order",
                        "slice_size": float(order_size) * 0.1,
                        "expected_impact_bps": impact_bps * 0.7
                    })
            
            elif urgency == "LOW":
                if participation_rate > 0.2:
                    recommendations["recommendations"].append({
                        "strategy": "TWAP",
                        "reason": "Large order, low urgency - use TWAP",
                        "duration_minutes": 60,
                        "expected_impact_bps": impact_bps * 0.5
                    })
                else:
                    recommendations["recommendations"].append({
                        "strategy": "LIMIT",
                        "reason": "Small order, low urgency - use limit order",
                        "limit_price_offset_bps": -2,
                        "expected_impact_bps": impact_bps * 0.3
                    })
            
            else:  # NORMAL urgency
                if impact_bps < 10 and participation_rate < 0.1:
                    recommendations["recommendations"].append({
                        "strategy": "MARKET",
                        "reason": "Low impact, normal urgency - use market order",
                        "expected_impact_bps": impact_bps
                    })
                else:
                    recommendations["recommendations"].append({
                        "strategy": "VWAP",
                        "reason": "Moderate impact, normal urgency - use VWAP",
                        "duration_minutes": 30,
                        "expected_impact_bps": impact_bps * 0.6
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Execution strategy recommendation failed: {e}")
            return {"error": str(e)}


class BrokerPerformanceAnalyzer:
    """Analyze broker execution performance."""
    
    def __init__(self):
        """Initialize broker performance analyzer."""
        self.broker_metrics = defaultdict(lambda: {
            "executions": [],
            "total_volume": 0,
            "total_orders": 0,
            "fill_rate": 0,
            "avg_latency": 0,
            "avg_slippage": 0
        })
        
    def update_broker_performance(
        self,
        broker: str,
        execution_analysis: Dict[str, Any]
    ):
        """Update broker performance metrics."""
        metrics = self.broker_metrics[broker]
        metrics["executions"].append(execution_analysis)
        
        # Keep only recent executions (last 1000)
        if len(metrics["executions"]) > 1000:
            metrics["executions"].pop(0)
        
        # Recalculate metrics
        self._recalculate_broker_metrics(broker)
    
    def _recalculate_broker_metrics(self, broker: str):
        """Recalculate broker performance metrics."""
        metrics = self.broker_metrics[broker]
        executions = metrics["executions"]
        
        if not executions:
            return
        
        # Volume and count
        metrics["total_volume"] = sum(
            exec_data.get("metrics", {}).get("fill_value", 0) 
            for exec_data in executions
        )
        metrics["total_orders"] = len(executions)
        
        # Latency
        latencies = [
            exec_data.get("metrics", {}).get("latency_ms", 0)
            for exec_data in executions
            if exec_data.get("metrics", {}).get("latency_ms")
        ]
        metrics["avg_latency"] = np.mean(latencies) if latencies else 0
        
        # Slippage
        slippages = [
            exec_data.get("metrics", {}).get("slippage_bps", 0)
            for exec_data in executions
            if exec_data.get("metrics", {}).get("slippage_bps")
        ]
        metrics["avg_slippage"] = np.mean(slippages) if slippages else 0
    
    def compare_brokers(self) -> Dict[str, Any]:
        """Compare broker performance."""
        comparison = {
            "brokers": {},
            "rankings": {
                "best_latency": None,
                "best_fill_rate": None,
                "lowest_slippage": None,
                "highest_volume": None
            }
        }
        
        best_latency = float('inf')
        best_fill_rate = 0
        lowest_slippage = float('inf')
        highest_volume = 0
        
        for broker, metrics in self.broker_metrics.items():
            broker_summary = {
                "total_volume": metrics["total_volume"],
                "total_orders": metrics["total_orders"],
                "avg_latency_ms": metrics["avg_latency"],
                "avg_slippage_bps": metrics["avg_slippage"],
                "fill_rate": metrics["fill_rate"]
            }
            
            comparison["brokers"][broker] = broker_summary
            
            # Track best performers
            if metrics["avg_latency"] < best_latency and metrics["avg_latency"] > 0:
                best_latency = metrics["avg_latency"]
                comparison["rankings"]["best_latency"] = broker
            
            if metrics["fill_rate"] > best_fill_rate:
                best_fill_rate = metrics["fill_rate"]
                comparison["rankings"]["best_fill_rate"] = broker
            
            if metrics["avg_slippage"] < lowest_slippage and metrics["avg_slippage"] > 0:
                lowest_slippage = metrics["avg_slippage"]
                comparison["rankings"]["lowest_slippage"] = broker
            
            if metrics["total_volume"] > highest_volume:
                highest_volume = metrics["total_volume"]
                comparison["rankings"]["highest_volume"] = broker
        
        return comparison


class ExecutionAnalytics:
    """Main execution analytics coordinator."""
    
    def __init__(self):
        """Initialize execution analytics."""
        self.quality_analyzer = ExecutionQualityAnalyzer()
        self.microstructure_analyzer = MarketMicrostructureAnalyzer()
        self.broker_analyzer = BrokerPerformanceAnalyzer()
        self.analytics_callbacks = []
        
    async def analyze_execution(
        self,
        execution: ExecutionReport,
        order: OrderResponse,
        market_data: Optional[MarketDataSnapshot] = None
    ) -> Dict[str, Any]:
        """Comprehensive execution analysis."""
        try:
            # Quality analysis
            quality_analysis = self.quality_analyzer.analyze_execution(execution, order, market_data)
            
            # Update broker performance
            if "broker" in order.__dict__:
                self.broker_analyzer.update_broker_performance(order.broker, quality_analysis)
            
            # Market microstructure analysis
            if market_data:
                self.microstructure_analyzer.update_order_book(order.symbol, {
                    "bid_price": market_data.bid_price,
                    "ask_price": market_data.ask_price,
                    "bid_size": market_data.bid_size,
                    "ask_size": market_data.ask_size,
                    "spread": market_data.spread
                })
            
            # Notify callbacks
            for callback in self.analytics_callbacks:
                try:
                    await callback(quality_analysis)
                except Exception as e:
                    logger.error(f"Analytics callback failed: {e}")
            
            return quality_analysis
            
        except Exception as e:
            logger.error(f"Execution analysis failed: {e}")
            return {"error": str(e)}
    
    async def get_execution_metrics(
        self,
        period_type: str = "DAY",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        symbol: Optional[str] = None,
        broker: Optional[str] = None
    ) -> ExecutionMetricsReport:
        """Get execution metrics report."""
        try:
            # Get execution summary
            summary = self.quality_analyzer.get_execution_summary(start_time, end_time, symbol, broker)
            
            if "error" in summary:
                raise ValueError(summary["error"])
            
            # Create metrics report
            return ExecutionMetricsReport(
                metric_date=end_time or datetime.utcnow(),
                period_type=period_type,
                strategy_id=None,  # TODO: Add strategy filtering
                broker=broker,
                asset_class=None,  # TODO: Add asset class filtering
                symbol=symbol,
                total_orders=summary["total_executions"],
                filled_orders=summary["total_executions"],  # Assuming all analyzed executions are filled
                total_volume=Decimal(str(summary["total_volume"])),
                filled_volume=Decimal(str(summary["total_volume"])),
                fill_rate=Decimal("1.0"),  # 100% for filled executions
                avg_slippage_bps=Decimal(str(summary["metrics"].get("avg_slippage_bps", 0))),
                avg_order_latency_ms=Decimal(str(summary["metrics"].get("avg_latency_ms", 0))),
                p95_order_latency_ms=Decimal(str(summary["metrics"].get("p95_latency_ms", 0))),
                total_commission=Decimal(str(summary["metrics"].get("total_cost", 0))),
                avg_commission_bps=Decimal(str(summary["metrics"].get("avg_cost_bps", 0)))
            )
            
        except Exception as e:
            logger.error(f"Execution metrics report failed: {e}")
            raise
    
    async def recommend_execution_strategy(
        self,
        symbol: str,
        order_size: Decimal,
        urgency: str = "NORMAL"
    ) -> Dict[str, Any]:
        """Get execution strategy recommendation."""
        return self.microstructure_analyzer.recommend_execution_strategy(symbol, order_size, urgency)
    
    async def compare_broker_performance(self) -> Dict[str, Any]:
        """Compare broker execution performance."""
        return self.broker_analyzer.compare_brokers()
    
    def add_analytics_callback(self, callback):
        """Add analytics callback."""
        self.analytics_callbacks.append(callback)
    
    async def cleanup(self):
        """Cleanup execution analytics."""
        logger.info("Execution analytics cleaned up")
