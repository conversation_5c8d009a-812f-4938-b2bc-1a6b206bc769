# AthenaTrader

AI-native trading platform with primary focus on FOREX markets - Expert trader private platform

## Overview

AthenaTrader is a sophisticated AI-driven trading platform designed for expert algorithmic traders. The platform provides comprehensive tools for strategy development, backtesting, portfolio optimization, and live execution with explainable AI at its core.

### Key Features

- **Multi-Paradigm AI**: Genetic Programming, Reinforcement Learning, Deep Learning, and Hybrid approaches
- **Explainable AI (XAI)**: Comprehensive explainability framework for regulatory compliance and user trust
- **Advanced Backtesting**: High-fidelity historical simulations with realistic market friction modeling
- **Portfolio Optimization**: Sophisticated multi-strategy portfolio construction and risk management
- **Live Execution**: Multi-broker execution gateway with advanced risk controls
- **FOREX Focus**: Primary focus on FOREX markets with support for other financial instruments

## Architecture

AthenaTrader follows a modular microservice architecture with 7 core services:

1. **Data Nexus**: Central data management and processing
2. **Strategy Genesis**: AI-driven strategy creation and optimization
3. **Backtesting Engine**: Comprehensive strategy validation and simulation
4. **Portfolio Construction**: Multi-strategy portfolio optimization
5. **XAI Module**: AI explainability and trust framework
6. **Execution Engine**: Live trading execution and monitoring
7. **Learning Hub**: Knowledge management and collaboration

## Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Frontend**: React 18+ with TypeScript
- **Database**: PostgreSQL with TimescaleDB extension
- **AI/ML**: scikit-learn, DEAP, TensorFlow, SHAP/LIME
- **Deployment**: Docker, Kubernetes
- **Cloud**: AWS (ECS, RDS, S3, ElastiCache)

## Getting Started

### Prerequisites

- Python 3.11+
- Node.js 18+
- Docker and Docker Compose
- PostgreSQL 15+ with TimescaleDB

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Foxernn/athena-trader.git
   cd athena-trader
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys and configuration
   ```

3. **Start the development environment**
   ```bash
   # On Linux/macOS
   chmod +x scripts/setup/dev-setup.sh
   ./scripts/setup/dev-setup.sh

   # On Windows
   # Run the commands manually or use WSL
   ```

4. **Start all services**
   ```bash
   docker-compose up -d
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Default Login Credentials

For development, use these expert trader accounts:
- **Username**: <EMAIL>
- **Password**: password (default hashed password in database)

### Service URLs

- **API Gateway**: http://localhost:8000
- **Data Nexus**: http://localhost:8001
- **Strategy Genesis**: http://localhost:8002
- **Backtesting Engine**: http://localhost:8003
- **Frontend**: http://localhost:3000

## Project Structure

```
athena-trader/
├── services/                 # Backend microservices
│   ├── data-nexus/          # Data management service
│   ├── strategy-genesis/    # AI strategy creation
│   ├── backtesting-engine/  # Strategy validation
│   ├── portfolio-optimization/ # Portfolio construction
│   ├── xai/                 # AI explainability
│   ├── execution-engine/    # Live trading execution
│   ├── learning-hub/        # Knowledge management
│   └── api-gateway/         # API gateway service
├── libs/                    # Shared libraries
├── frontend/                # React frontend application
├── infra/                   # Infrastructure as code
├── scripts/                 # Utility scripts
└── tests/                   # End-to-end tests
```

## Development

### Running Tests

```bash
# Backend tests
pytest services/api-gateway/tests/
pytest services/data-nexus/tests/

# Frontend tests
cd frontend && npm test

# Run all tests with coverage
pytest --cov=services --cov-report=html
```

### Code Quality

- **Test Coverage**: >75% for core functionality
- **Type Hints**: Required for all Python code
- **Linting**: Black, isort, flake8 for Python; ESLint, Prettier for TypeScript
- **Documentation**: Comprehensive API documentation with OpenAPI/Swagger

### Development Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the established code patterns
   - Add tests for new functionality
   - Update documentation as needed

3. **Run quality checks**
   ```bash
   # Python code formatting
   black services/
   isort services/
   flake8 services/

   # Frontend code formatting
   cd frontend
   npm run lint:fix
   npm run format
   ```

4. **Test your changes**
   ```bash
   # Run tests
   pytest
   cd frontend && npm test

   # Test with Docker
   docker-compose up --build
   ```

5. **Commit and push**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   git push origin feature/your-feature-name
   ```

### API Documentation

- **API Gateway**: http://localhost:8000/docs
- **Data Nexus**: http://localhost:8001/docs
- **Interactive API Testing**: Available through Swagger UI

### Database Management

```bash
# Access PostgreSQL
docker-compose exec postgres psql -U athena_user -d athena_trader

# View TimescaleDB hypertables
SELECT * FROM timescaledb_information.hypertables;

# Check market data
SELECT * FROM market_data.ohlcv LIMIT 10;
```

## License

Private - Expert Trader Platform

## Contact

For questions or support, please contact the development team.
