# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Data processing and analysis
pandas==2.1.3
numpy==1.24.4
scipy==1.11.4

# Machine learning and statistics
scikit-learn==1.3.2
statsmodels==0.14.0

# XAI and interpretability libraries
shap==0.43.0
lime==*******

# Deep learning interpretability
torch==2.1.1
torchvision==0.16.1
captum==0.6.0

# Visualization libraries
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0

# HTTP client
httpx==0.25.2

# System monitoring
psutil==5.9.6

# Configuration and environment
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging and utilities
structlog==23.2.0
rich==13.7.0

# Parallel processing
joblib==1.3.2

# Caching
redis==5.0.1

# Image processing for chart export
Pillow==10.1.0
kaleido==0.2.1

# Additional ML libraries
xgboost==2.0.2
lightgbm==4.1.0

# Graph visualization
networkx==3.2.1
graphviz==0.20.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Performance optimization
numba==0.58.1

# Additional interpretability tools
eli5==0.13.0
pdpbox==0.2.1

# Time series analysis
tsfresh==0.20.1

# Natural language processing (for text explanations)
nltk==3.8.1
spacy==3.7.2

# Financial calculations
quantlib==1.32

# Cryptography for data hashing
cryptography==41.0.8
