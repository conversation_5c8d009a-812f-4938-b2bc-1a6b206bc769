import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock the auth context to avoid issues in tests
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: false,
    isLoading: false,
    user: null,
    login: jest.fn(),
    logout: jest.fn(),
    clearError: jest.fn(),
    error: null,
  }),
}));

test('renders login page when not authenticated', () => {
  render(<App />);
  const loginElement = screen.getByText(/AthenaTrader/i);
  expect(loginElement).toBeInTheDocument();
});
