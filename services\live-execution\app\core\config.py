"""
Configuration settings for Live Execution Module.
"""

import os
from typing import List, Optional, Dict, Any
from decimal import Decimal

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Basic settings
    DEBUG: bool = Field(default=False, description="Debug mode")
    ENVIRONMENT: str = Field(default="development", description="Environment")
    VERSION: str = Field(default="0.1.0", description="Application version")
    
    # Database settings
    DATABASE_URL: str = Field(
        default="postgresql://athena_user:athena_password@localhost:5432/athena_trader",
        description="Database connection URL"
    )
    
    # Redis settings
    REDIS_URL: str = Field(
        default="redis://localhost:6379",
        description="Redis connection URL"
    )
    
    # External service URLs
    DATA_NEXUS_URL: str = Field(
        default="http://localhost:8001",
        description="Data Nexus service URL"
    )
    STRATEGY_GENESIS_URL: str = Field(
        default="http://localhost:8002",
        description="Strategy Genesis service URL"
    )
    BACKTESTING_ENGINE_URL: str = Field(
        default="http://localhost:8003",
        description="Backtesting Engine service URL"
    )
    PORTFOLIO_CONSTRUCTION_URL: str = Field(
        default="http://localhost:8004",
        description="Portfolio Construction service URL"
    )
    XAI_MODULE_URL: str = Field(
        default="http://localhost:8005",
        description="XAI Module service URL"
    )
    
    # Storage paths
    EXECUTION_LOGS_PATH: str = Field(
        default="/app/execution_logs",
        description="Execution logs storage path"
    )
    STRATEGY_ARTIFACTS_PATH: str = Field(
        default="/app/strategy_artifacts",
        description="Strategy artifacts storage path"
    )
    RISK_REPORTS_PATH: str = Field(
        default="/app/risk_reports",
        description="Risk reports storage path"
    )
    EXECUTION_REPORTS_PATH: str = Field(
        default="/app/execution_reports",
        description="Execution reports storage path"
    )
    
    # Broker configurations
    SUPPORTED_BROKERS: List[str] = Field(
        default=["INTERACTIVE_BROKERS", "ALPACA", "TD_AMERITRADE", "BINANCE", "COINBASE"],
        description="Supported broker list"
    )
    
    # Interactive Brokers settings
    IB_HOST: str = Field(default="127.0.0.1", description="IB Gateway host")
    IB_PORT: int = Field(default=7497, description="IB Gateway port")
    IB_CLIENT_ID: int = Field(default=1, description="IB client ID")
    IB_ACCOUNT: Optional[str] = Field(default=None, description="IB account number")
    
    # Alpaca settings
    ALPACA_API_KEY: Optional[str] = Field(default=None, description="Alpaca API key")
    ALPACA_SECRET_KEY: Optional[str] = Field(default=None, description="Alpaca secret key")
    ALPACA_BASE_URL: str = Field(
        default="https://paper-api.alpaca.markets",
        description="Alpaca API base URL"
    )
    
    # FIX protocol settings
    FIX_CONFIG_FILE: str = Field(
        default="/app/config/fix_config.cfg",
        description="FIX configuration file path"
    )
    FIX_SENDER_COMP_ID: str = Field(default="ATHENA", description="FIX sender comp ID")
    FIX_TARGET_COMP_ID: str = Field(default="BROKER", description="FIX target comp ID")
    FIX_VERSION: str = Field(default="FIX.4.4", description="FIX protocol version")
    
    # Risk management settings
    MAX_POSITION_SIZE: Decimal = Field(
        default=Decimal("1000000"),
        description="Maximum position size in USD"
    )
    MAX_DAILY_LOSS: Decimal = Field(
        default=Decimal("50000"),
        description="Maximum daily loss in USD"
    )
    MAX_DRAWDOWN: Decimal = Field(
        default=Decimal("0.10"),
        description="Maximum drawdown percentage"
    )
    MAX_LEVERAGE: Decimal = Field(
        default=Decimal("3.0"),
        description="Maximum leverage ratio"
    )
    MAX_CONCENTRATION: Decimal = Field(
        default=Decimal("0.20"),
        description="Maximum single position concentration"
    )
    
    # Circuit breaker settings
    ENABLE_CIRCUIT_BREAKERS: bool = Field(
        default=True,
        description="Enable circuit breakers"
    )
    VOLATILITY_THRESHOLD: Decimal = Field(
        default=Decimal("0.05"),
        description="Volatility threshold for circuit breakers"
    )
    PRICE_MOVE_THRESHOLD: Decimal = Field(
        default=Decimal("0.10"),
        description="Price move threshold for circuit breakers"
    )
    VOLUME_SPIKE_THRESHOLD: Decimal = Field(
        default=Decimal("3.0"),
        description="Volume spike threshold multiplier"
    )
    
    # Order management settings
    DEFAULT_ORDER_TIMEOUT: int = Field(
        default=300,
        description="Default order timeout in seconds"
    )
    MAX_ORDER_SIZE: Decimal = Field(
        default=Decimal("100000"),
        description="Maximum single order size in USD"
    )
    MIN_ORDER_SIZE: Decimal = Field(
        default=Decimal("100"),
        description="Minimum single order size in USD"
    )
    ORDER_RETRY_ATTEMPTS: int = Field(
        default=3,
        description="Maximum order retry attempts"
    )
    ORDER_RETRY_DELAY: int = Field(
        default=5,
        description="Order retry delay in seconds"
    )
    
    # Execution algorithms
    SUPPORTED_EXECUTION_ALGOS: List[str] = Field(
        default=["MARKET", "LIMIT", "TWAP", "VWAP", "IMPLEMENTATION_SHORTFALL", "POV"],
        description="Supported execution algorithms"
    )
    DEFAULT_EXECUTION_ALGO: str = Field(
        default="TWAP",
        description="Default execution algorithm"
    )
    
    # Market data settings
    MARKET_DATA_PROVIDERS: List[str] = Field(
        default=["IEX", "ALPHA_VANTAGE", "POLYGON", "YAHOO_FINANCE"],
        description="Market data providers"
    )
    MARKET_DATA_UPDATE_INTERVAL: int = Field(
        default=1,
        description="Market data update interval in seconds"
    )
    ENABLE_LEVEL2_DATA: bool = Field(
        default=True,
        description="Enable Level 2 market data"
    )
    
    # Strategy deployment settings
    ENABLE_CANARY_RELEASES: bool = Field(
        default=True,
        description="Enable canary releases"
    )
    CANARY_TRAFFIC_PERCENTAGE: Decimal = Field(
        default=Decimal("0.10"),
        description="Canary traffic percentage"
    )
    STRATEGY_WARMUP_PERIOD: int = Field(
        default=300,
        description="Strategy warmup period in seconds"
    )
    MAX_CONCURRENT_STRATEGIES: int = Field(
        default=50,
        description="Maximum concurrent strategies"
    )
    
    # Performance monitoring
    PERFORMANCE_UPDATE_INTERVAL: int = Field(
        default=60,
        description="Performance update interval in seconds"
    )
    ENABLE_REAL_TIME_ATTRIBUTION: bool = Field(
        default=True,
        description="Enable real-time performance attribution"
    )
    ENABLE_EXECUTION_ANALYTICS: bool = Field(
        default=True,
        description="Enable execution quality analytics"
    )
    
    # XAI integration settings
    ENABLE_REAL_TIME_XAI: bool = Field(
        default=True,
        description="Enable real-time XAI integration"
    )
    XAI_EXPLANATION_TIMEOUT: int = Field(
        default=30,
        description="XAI explanation timeout in seconds"
    )
    ENABLE_LIVE_BIAS_DETECTION: bool = Field(
        default=True,
        description="Enable live bias detection"
    )
    
    # Compliance and audit settings
    ENABLE_AUDIT_TRAIL: bool = Field(
        default=True,
        description="Enable comprehensive audit trail"
    )
    AUDIT_RETENTION_DAYS: int = Field(
        default=2555,  # 7 years
        description="Audit trail retention in days"
    )
    REGULATORY_FRAMEWORKS: List[str] = Field(
        default=["MIFID_II", "FINRA", "SEC", "CFTC"],
        description="Regulatory frameworks"
    )
    
    # Asset class settings
    SUPPORTED_ASSET_CLASSES: List[str] = Field(
        default=["EQUITY", "FUTURES", "FOREX", "CRYPTO", "OPTIONS"],
        description="Supported asset classes"
    )
    
    # Equity settings
    EQUITY_MARKET_HOURS: Dict[str, Any] = Field(
        default={
            "NYSE": {"open": "09:30", "close": "16:00", "timezone": "US/Eastern"},
            "NASDAQ": {"open": "09:30", "close": "16:00", "timezone": "US/Eastern"},
            "LSE": {"open": "08:00", "close": "16:30", "timezone": "Europe/London"}
        },
        description="Equity market hours"
    )
    
    # Futures settings
    FUTURES_MARGIN_MULTIPLIER: Decimal = Field(
        default=Decimal("0.05"),
        description="Futures margin multiplier"
    )
    
    # Forex settings
    FOREX_SPREAD_THRESHOLD: Decimal = Field(
        default=Decimal("0.0005"),
        description="Forex spread threshold"
    )
    
    # Crypto settings
    CRYPTO_EXCHANGES: List[str] = Field(
        default=["BINANCE", "COINBASE", "KRAKEN", "BITFINEX"],
        description="Supported crypto exchanges"
    )
    
    # Latency and performance settings
    MAX_ORDER_LATENCY_MS: int = Field(
        default=100,
        description="Maximum acceptable order latency in milliseconds"
    )
    MAX_MARKET_DATA_LATENCY_MS: int = Field(
        default=50,
        description="Maximum acceptable market data latency in milliseconds"
    )
    
    # Logging settings
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    ENABLE_STRUCTURED_LOGGING: bool = Field(
        default=True,
        description="Enable structured logging"
    )
    LOG_RETENTION_DAYS: int = Field(
        default=90,
        description="Log retention in days"
    )
    
    # Monitoring and alerting
    ENABLE_REAL_TIME_MONITORING: bool = Field(
        default=True,
        description="Enable real-time monitoring"
    )
    ALERT_EMAIL_RECIPIENTS: List[str] = Field(
        default=[],
        description="Alert email recipients"
    )
    SLACK_WEBHOOK_URL: Optional[str] = Field(
        default=None,
        description="Slack webhook URL for alerts"
    )
    
    # Testing and simulation
    ENABLE_PAPER_TRADING: bool = Field(
        default=True,
        description="Enable paper trading mode"
    )
    SIMULATION_MODE: bool = Field(
        default=False,
        description="Simulation mode for testing"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
