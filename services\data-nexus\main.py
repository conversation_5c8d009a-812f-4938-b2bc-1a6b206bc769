"""
AthenaTrader Data Nexus Service

Central data management and processing service that handles data ingestion,
storage, validation, and distribution for the AthenaTrader platform.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import health, market_data, instruments

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader Data Nexus Service...")
    await init_db()
    logger.info("Database initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader Data Nexus Service...")


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader Data Nexus",
    description="Central data management and processing service for AthenaTrader",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(instruments.router, prefix="/instruments", tags=["instruments"])
app.include_router(market_data.router, prefix="/market-data", tags=["market-data"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader Data Nexus Service",
        "version": "0.1.0",
        "status": "operational"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
