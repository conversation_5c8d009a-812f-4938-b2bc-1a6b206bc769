"""
Health check router for Data Nexus service.
"""

import logging
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """
    Basic health check endpoint.
    
    Returns:
        dict: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "data-nexus",
        "version": "0.1.0"
    }


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_db)):
    """
    Kubernetes readiness probe endpoint.
    
    Args:
        db: Database session
        
    Returns:
        dict: Readiness status including dependencies
        
    Raises:
        HTTPException: If service is not ready
    """
    checks = {}
    overall_status = "ready"
    
    # Database connectivity check
    try:
        result = await db.execute(text("SELECT 1"))
        result.scalar()
        checks["database"] = {"status": "healthy"}
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        checks["database"] = {"status": "unhealthy", "error": str(e)}
        overall_status = "not_ready"
    
    # Alpha Vantage API check (if configured)
    if settings.ALPHA_VANTAGE_API_KEY:
        try:
            from app.integrations.alpha_vantage import AlphaVantageClient
            av_client = AlphaVantageClient()
            # Simple check - get available currencies
            currencies = await av_client.get_available_currencies()
            await av_client.close()
            checks["alpha_vantage"] = {"status": "healthy", "currencies_available": len(currencies)}
        except Exception as e:
            logger.error(f"Alpha Vantage health check failed: {e}")
            checks["alpha_vantage"] = {"status": "unhealthy", "error": str(e)}
            # Don't mark as not ready for external API issues
    else:
        checks["alpha_vantage"] = {"status": "not_configured"}
    
    response = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks
    }
    
    if overall_status != "ready":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=response
        )
    
    return response
