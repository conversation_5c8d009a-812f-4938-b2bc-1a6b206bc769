"""
Smart Order Routing (SOR) system with multi-criteria venue selection and ML optimization.
Phase 10: Production Trading Optimization - Weeks 1-4
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
import joblib

from app.core.config import settings
from app.schemas.execution import OrderRequest, AssetClass, OrderSide, OrderType
from app.engine.market_data import MarketDataManager

logger = logging.getLogger(__name__)


class VenueType(str, Enum):
    """Venue type enumeration."""
    EXCHANGE = "EXCHANGE"
    DARK_POOL = "DARK_POOL"
    ECN = "ECN"
    MARKET_MAKER = "MARKET_MAKER"
    CROSSING_NETWORK = "CROSSING_NETWORK"


@dataclass
class VenueMetrics:
    """Venue performance metrics."""
    venue_id: str
    fill_rate: float
    avg_latency_ms: float
    avg_spread_bps: float
    market_share: float
    price_improvement_bps: float
    rejection_rate: float
    last_updated: datetime


@dataclass
class LiquidityLevel:
    """Order book liquidity level."""
    price: Decimal
    quantity: Decimal
    venue_id: str
    timestamp: datetime


@dataclass
class VenueSelection:
    """Venue selection result."""
    venue_id: str
    allocation_percentage: float
    expected_fill_rate: float
    expected_cost_bps: float
    confidence_score: float
    reasoning: str


class VenueScorer:
    """Multi-criteria venue scoring engine."""
    
    def __init__(self):
        """Initialize venue scorer."""
        self.weights = {
            'fill_rate': 0.25,
            'latency': 0.20,
            'cost': 0.25,
            'market_share': 0.15,
            'price_improvement': 0.15
        }
        self.scaler = StandardScaler()
        
    def calculate_venue_score(
        self, 
        venue_metrics: VenueMetrics, 
        order: OrderRequest,
        market_conditions: Dict[str, Any]
    ) -> float:
        """Calculate composite venue score."""
        try:
            # Normalize metrics to 0-1 scale
            fill_rate_score = min(venue_metrics.fill_rate, 1.0)
            latency_score = max(0, 1 - (venue_metrics.avg_latency_ms / 1000))  # Penalize high latency
            cost_score = max(0, 1 - (venue_metrics.avg_spread_bps / 100))  # Penalize high spreads
            market_share_score = min(venue_metrics.market_share, 1.0)
            price_improvement_score = min(venue_metrics.price_improvement_bps / 10, 1.0)
            
            # Apply order-specific adjustments
            if order.order_type == OrderType.MARKET:
                self.weights['fill_rate'] = 0.35  # Prioritize fill rate for market orders
                self.weights['latency'] = 0.30
            elif order.order_type == OrderType.LIMIT:
                self.weights['price_improvement'] = 0.25  # Prioritize price improvement
                self.weights['cost'] = 0.30
            
            # Calculate weighted score
            composite_score = (
                fill_rate_score * self.weights['fill_rate'] +
                latency_score * self.weights['latency'] +
                cost_score * self.weights['cost'] +
                market_share_score * self.weights['market_share'] +
                price_improvement_score * self.weights['price_improvement']
            )
            
            # Apply market condition adjustments
            volatility_adjustment = self._calculate_volatility_adjustment(
                market_conditions.get('volatility', 0.2)
            )
            
            return min(composite_score * volatility_adjustment, 1.0)
            
        except Exception as e:
            logger.error(f"Venue scoring failed: {e}")
            return 0.0
    
    def _calculate_volatility_adjustment(self, volatility: float) -> float:
        """Adjust scoring based on market volatility."""
        if volatility > 0.5:  # High volatility
            return 0.9  # Slightly penalize during high volatility
        elif volatility < 0.1:  # Low volatility
            return 1.1  # Slight bonus during low volatility
        return 1.0


class LiquidityAggregator:
    """Real-time liquidity aggregation across venues."""
    
    def __init__(self, market_data_manager: MarketDataManager):
        """Initialize liquidity aggregator."""
        self.market_data_manager = market_data_manager
        self.venue_order_books = {}
        self.latency_adjustments = {}
        
    async def aggregate_liquidity(self, symbol: str) -> Dict[str, List[LiquidityLevel]]:
        """Aggregate order book liquidity across venues."""
        try:
            aggregated_liquidity = {'bids': [], 'asks': []}
            
            # Get order books from all venues
            venue_books = await self._get_venue_order_books(symbol)
            
            for venue_id, order_book in venue_books.items():
                if not order_book:
                    continue
                
                # Apply latency adjustments
                adjusted_book = await self._apply_latency_adjustment(venue_id, order_book)
                
                # Extract liquidity levels
                for bid in adjusted_book.get('bids', []):
                    liquidity_level = LiquidityLevel(
                        price=Decimal(str(bid['price'])),
                        quantity=Decimal(str(bid['quantity'])),
                        venue_id=venue_id,
                        timestamp=datetime.utcnow()
                    )
                    aggregated_liquidity['bids'].append(liquidity_level)
                
                for ask in adjusted_book.get('asks', []):
                    liquidity_level = LiquidityLevel(
                        price=Decimal(str(ask['price'])),
                        quantity=Decimal(str(ask['quantity'])),
                        venue_id=venue_id,
                        timestamp=datetime.utcnow()
                    )
                    aggregated_liquidity['asks'].append(liquidity_level)
            
            # Sort by price
            aggregated_liquidity['bids'].sort(key=lambda x: x.price, reverse=True)
            aggregated_liquidity['asks'].sort(key=lambda x: x.price)
            
            return aggregated_liquidity
            
        except Exception as e:
            logger.error(f"Liquidity aggregation failed: {e}")
            return {'bids': [], 'asks': []}
    
    async def _get_venue_order_books(self, symbol: str) -> Dict[str, Dict]:
        """Get order books from all connected venues."""
        venue_books = {}
        
        # Get from market data manager
        market_data = await self.market_data_manager.get_market_data(symbol, AssetClass.EQUITY)
        
        if market_data:
            # Simulate multiple venue data (in production, this would come from actual venues)
            venues = ['NYSE', 'NASDAQ', 'BATS', 'IEX', 'DARK_POOL_1']
            
            for venue in venues:
                # Simulate venue-specific order book variations
                venue_books[venue] = {
                    'bids': [
                        {
                            'price': float(market_data.bid_price) * (1 + np.random.uniform(-0.001, 0.001)),
                            'quantity': float(market_data.bid_size) * np.random.uniform(0.5, 1.5)
                        }
                    ],
                    'asks': [
                        {
                            'price': float(market_data.ask_price) * (1 + np.random.uniform(-0.001, 0.001)),
                            'quantity': float(market_data.ask_size) * np.random.uniform(0.5, 1.5)
                        }
                    ]
                }
        
        return venue_books
    
    async def _apply_latency_adjustment(self, venue_id: str, order_book: Dict) -> Dict:
        """Apply latency-based price adjustments."""
        latency_ms = self.latency_adjustments.get(venue_id, 10)  # Default 10ms
        
        # Adjust prices based on latency (simple model)
        adjustment_factor = 1 + (latency_ms / 10000)  # Small adjustment for latency
        
        adjusted_book = {
            'bids': [
                {
                    'price': bid['price'] * (2 - adjustment_factor),  # Slightly lower bids
                    'quantity': bid['quantity']
                }
                for bid in order_book.get('bids', [])
            ],
            'asks': [
                {
                    'price': ask['price'] * adjustment_factor,  # Slightly higher asks
                    'quantity': ask['quantity']
                }
                for ask in order_book.get('asks', [])
            ]
        }
        
        return adjusted_book


class MLVenueOptimizer:
    """Machine learning-based venue optimization."""
    
    def __init__(self):
        """Initialize ML venue optimizer."""
        self.models = {
            'fill_rate_predictor': RandomForestRegressor(n_estimators=100, random_state=42),
            'cost_predictor': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'latency_predictor': RandomForestRegressor(n_estimators=50, random_state=42)
        }
        self.feature_scaler = StandardScaler()
        self.is_trained = False
        
    async def train_models(self, historical_data: pd.DataFrame):
        """Train ML models on historical execution data."""
        try:
            if historical_data.empty:
                logger.warning("No historical data available for ML training")
                return
            
            # Prepare features
            features = self._prepare_features(historical_data)
            
            # Train fill rate predictor
            if 'actual_fill_rate' in historical_data.columns:
                self.models['fill_rate_predictor'].fit(
                    features, historical_data['actual_fill_rate']
                )
            
            # Train cost predictor
            if 'actual_cost_bps' in historical_data.columns:
                self.models['cost_predictor'].fit(
                    features, historical_data['actual_cost_bps']
                )
            
            # Train latency predictor
            if 'actual_latency_ms' in historical_data.columns:
                self.models['latency_predictor'].fit(
                    features, historical_data['actual_latency_ms']
                )
            
            self.is_trained = True
            logger.info("ML venue optimization models trained successfully")
            
        except Exception as e:
            logger.error(f"ML model training failed: {e}")
    
    def _prepare_features(self, data: pd.DataFrame) -> np.ndarray:
        """Prepare features for ML models."""
        feature_columns = [
            'order_size', 'market_cap', 'volatility', 'spread_bps',
            'volume_ratio', 'time_of_day', 'day_of_week'
        ]
        
        # Fill missing columns with defaults
        for col in feature_columns:
            if col not in data.columns:
                data[col] = 0
        
        features = data[feature_columns].fillna(0)
        return self.feature_scaler.fit_transform(features)
    
    async def predict_venue_performance(
        self, 
        venue_id: str, 
        order: OrderRequest,
        market_conditions: Dict[str, Any]
    ) -> Dict[str, float]:
        """Predict venue performance using ML models."""
        try:
            if not self.is_trained:
                # Return default predictions if models not trained
                return {
                    'predicted_fill_rate': 0.95,
                    'predicted_cost_bps': 2.0,
                    'predicted_latency_ms': 50.0
                }
            
            # Prepare features for prediction
            features = self._prepare_prediction_features(order, market_conditions)
            
            # Make predictions
            predictions = {}
            for model_name, model in self.models.items():
                try:
                    prediction = model.predict([features])[0]
                    predictions[f"predicted_{model_name.replace('_predictor', '')}"] = prediction
                except Exception as e:
                    logger.warning(f"Prediction failed for {model_name}: {e}")
                    predictions[f"predicted_{model_name.replace('_predictor', '')}"] = 0.0
            
            return predictions
            
        except Exception as e:
            logger.error(f"ML prediction failed: {e}")
            return {
                'predicted_fill_rate': 0.95,
                'predicted_cost_bps': 2.0,
                'predicted_latency_ms': 50.0
            }
    
    def _prepare_prediction_features(
        self, 
        order: OrderRequest, 
        market_conditions: Dict[str, Any]
    ) -> np.ndarray:
        """Prepare features for prediction."""
        now = datetime.utcnow()
        
        features = [
            float(order.quantity),  # order_size
            market_conditions.get('market_cap', 1e9),  # market_cap
            market_conditions.get('volatility', 0.2),  # volatility
            market_conditions.get('spread_bps', 5.0),  # spread_bps
            market_conditions.get('volume_ratio', 1.0),  # volume_ratio
            now.hour + now.minute / 60.0,  # time_of_day
            now.weekday()  # day_of_week
        ]
        
        return self.feature_scaler.transform([features])[0]


class SmartOrderRouter:
    """Main smart order routing engine."""
    
    def __init__(self, market_data_manager: MarketDataManager):
        """Initialize smart order router."""
        self.market_data_manager = market_data_manager
        self.venue_scorer = VenueScorer()
        self.liquidity_aggregator = LiquidityAggregator(market_data_manager)
        self.ml_optimizer = MLVenueOptimizer()
        self.venue_metrics = {}
        self.routing_history = []
        
    async def initialize(self):
        """Initialize smart order router."""
        try:
            # Load venue metrics
            await self._load_venue_metrics()
            
            # Train ML models if historical data available
            historical_data = await self._load_historical_data()
            if not historical_data.empty:
                await self.ml_optimizer.train_models(historical_data)
            
            logger.info("Smart Order Router initialized successfully")
            
        except Exception as e:
            logger.error(f"Smart Order Router initialization failed: {e}")
            raise
    
    async def route_order(self, order: OrderRequest) -> List[VenueSelection]:
        """Route order to optimal venues."""
        try:
            # Get market conditions
            market_conditions = await self._get_market_conditions(order.symbol)
            
            # Get available venues
            available_venues = await self._get_available_venues(order.symbol, order.asset_class)
            
            if not available_venues:
                raise ValueError("No venues available for order routing")
            
            # Score each venue
            venue_scores = {}
            for venue_id in available_venues:
                venue_metrics = self.venue_metrics.get(venue_id)
                if venue_metrics:
                    # Get ML predictions
                    ml_predictions = await self.ml_optimizer.predict_venue_performance(
                        venue_id, order, market_conditions
                    )
                    
                    # Calculate composite score
                    score = self.venue_scorer.calculate_venue_score(
                        venue_metrics, order, market_conditions
                    )
                    
                    # Adjust score with ML predictions
                    ml_adjustment = (
                        ml_predictions.get('predicted_fill_rate', 0.95) * 0.3 +
                        (1 - ml_predictions.get('predicted_cost_bps', 5.0) / 20) * 0.3 +
                        (1 - ml_predictions.get('predicted_latency_ms', 50.0) / 200) * 0.4
                    )
                    
                    adjusted_score = score * 0.7 + ml_adjustment * 0.3
                    venue_scores[venue_id] = {
                        'score': adjusted_score,
                        'ml_predictions': ml_predictions
                    }
            
            # Select optimal venues
            venue_selections = await self._select_optimal_venues(
                venue_scores, order, market_conditions
            )
            
            # Record routing decision
            await self._record_routing_decision(order, venue_selections)
            
            return venue_selections
            
        except Exception as e:
            logger.error(f"Order routing failed: {e}")
            raise
    
    async def _get_market_conditions(self, symbol: str) -> Dict[str, Any]:
        """Get current market conditions."""
        try:
            market_data = await self.market_data_manager.get_market_data(symbol, AssetClass.EQUITY)
            
            if market_data:
                return {
                    'volatility': float(market_data.volatility) if market_data.volatility else 0.2,
                    'spread_bps': float(market_data.spread_bps) if market_data.spread_bps else 5.0,
                    'volume_ratio': 1.0,  # Would be calculated from historical volume
                    'market_cap': 1e9,  # Would be fetched from reference data
                    'last_price': float(market_data.last_price) if market_data.last_price else 100.0
                }
            else:
                return {
                    'volatility': 0.2,
                    'spread_bps': 5.0,
                    'volume_ratio': 1.0,
                    'market_cap': 1e9,
                    'last_price': 100.0
                }
                
        except Exception as e:
            logger.error(f"Failed to get market conditions: {e}")
            return {
                'volatility': 0.2,
                'spread_bps': 5.0,
                'volume_ratio': 1.0,
                'market_cap': 1e9,
                'last_price': 100.0
            }
    
    async def _get_available_venues(self, symbol: str, asset_class: AssetClass) -> List[str]:
        """Get available venues for symbol and asset class."""
        # In production, this would query venue connectivity and symbol coverage
        if asset_class == AssetClass.EQUITY:
            return ['NYSE', 'NASDAQ', 'BATS', 'IEX', 'DARK_POOL_1']
        elif asset_class == AssetClass.CRYPTO:
            return ['BINANCE', 'COINBASE', 'KRAKEN']
        else:
            return ['INTERACTIVE_BROKERS']
    
    async def _select_optimal_venues(
        self, 
        venue_scores: Dict[str, Dict], 
        order: OrderRequest,
        market_conditions: Dict[str, Any]
    ) -> List[VenueSelection]:
        """Select optimal venues based on scores and order characteristics."""
        try:
            # Sort venues by score
            sorted_venues = sorted(
                venue_scores.items(), 
                key=lambda x: x[1]['score'], 
                reverse=True
            )
            
            selections = []
            remaining_quantity = float(order.quantity)
            
            # For small orders, use single best venue
            if remaining_quantity < 1000:
                best_venue = sorted_venues[0]
                selections.append(VenueSelection(
                    venue_id=best_venue[0],
                    allocation_percentage=100.0,
                    expected_fill_rate=best_venue[1]['ml_predictions'].get('predicted_fill_rate', 0.95),
                    expected_cost_bps=best_venue[1]['ml_predictions'].get('predicted_cost_bps', 2.0),
                    confidence_score=best_venue[1]['score'],
                    reasoning="Single venue for small order"
                ))
            else:
                # For large orders, split across multiple venues
                total_allocation = 0.0
                for i, (venue_id, venue_data) in enumerate(sorted_venues[:3]):  # Top 3 venues
                    if i == 0:
                        allocation = 60.0  # Primary venue gets 60%
                    elif i == 1:
                        allocation = 30.0  # Secondary venue gets 30%
                    else:
                        allocation = 10.0  # Tertiary venue gets 10%
                    
                    total_allocation += allocation
                    
                    selections.append(VenueSelection(
                        venue_id=venue_id,
                        allocation_percentage=allocation,
                        expected_fill_rate=venue_data['ml_predictions'].get('predicted_fill_rate', 0.95),
                        expected_cost_bps=venue_data['ml_predictions'].get('predicted_cost_bps', 2.0),
                        confidence_score=venue_data['score'],
                        reasoning=f"Multi-venue split - rank {i+1}"
                    ))
            
            return selections
            
        except Exception as e:
            logger.error(f"Venue selection failed: {e}")
            return []
    
    async def _load_venue_metrics(self):
        """Load venue performance metrics."""
        # In production, this would load from database
        self.venue_metrics = {
            'NYSE': VenueMetrics(
                venue_id='NYSE',
                fill_rate=0.98,
                avg_latency_ms=15.0,
                avg_spread_bps=2.5,
                market_share=0.25,
                price_improvement_bps=0.5,
                rejection_rate=0.01,
                last_updated=datetime.utcnow()
            ),
            'NASDAQ': VenueMetrics(
                venue_id='NASDAQ',
                fill_rate=0.97,
                avg_latency_ms=12.0,
                avg_spread_bps=2.8,
                market_share=0.22,
                price_improvement_bps=0.3,
                rejection_rate=0.015,
                last_updated=datetime.utcnow()
            ),
            'BATS': VenueMetrics(
                venue_id='BATS',
                fill_rate=0.96,
                avg_latency_ms=10.0,
                avg_spread_bps=3.0,
                market_share=0.15,
                price_improvement_bps=0.8,
                rejection_rate=0.02,
                last_updated=datetime.utcnow()
            ),
            'IEX': VenueMetrics(
                venue_id='IEX',
                fill_rate=0.94,
                avg_latency_ms=350.0,  # IEX speed bump
                avg_spread_bps=2.2,
                market_share=0.08,
                price_improvement_bps=1.2,
                rejection_rate=0.005,
                last_updated=datetime.utcnow()
            ),
            'DARK_POOL_1': VenueMetrics(
                venue_id='DARK_POOL_1',
                fill_rate=0.85,
                avg_latency_ms=25.0,
                avg_spread_bps=1.5,
                market_share=0.05,
                price_improvement_bps=2.0,
                rejection_rate=0.1,
                last_updated=datetime.utcnow()
            )
        }
    
    async def _load_historical_data(self) -> pd.DataFrame:
        """Load historical execution data for ML training."""
        # In production, this would load from database
        # Return empty DataFrame for now
        return pd.DataFrame()
    
    async def _record_routing_decision(
        self, 
        order: OrderRequest, 
        venue_selections: List[VenueSelection]
    ):
        """Record routing decision for analysis."""
        routing_record = {
            'timestamp': datetime.utcnow(),
            'order_id': str(uuid.uuid4()),
            'symbol': order.symbol,
            'quantity': float(order.quantity),
            'order_type': order.order_type.value,
            'venue_selections': [
                {
                    'venue_id': selection.venue_id,
                    'allocation_percentage': selection.allocation_percentage,
                    'expected_fill_rate': selection.expected_fill_rate,
                    'confidence_score': selection.confidence_score
                }
                for selection in venue_selections
            ]
        }
        
        self.routing_history.append(routing_record)
        
        # Keep only recent history
        if len(self.routing_history) > 10000:
            self.routing_history = self.routing_history[-5000:]
    
    async def get_routing_statistics(self) -> Dict[str, Any]:
        """Get routing performance statistics."""
        if not self.routing_history:
            return {"error": "No routing history available"}
        
        # Calculate statistics
        total_orders = len(self.routing_history)
        venue_usage = {}
        
        for record in self.routing_history:
            for selection in record['venue_selections']:
                venue_id = selection['venue_id']
                if venue_id not in venue_usage:
                    venue_usage[venue_id] = 0
                venue_usage[venue_id] += 1
        
        return {
            'total_orders_routed': total_orders,
            'venue_usage_distribution': {
                venue: count / total_orders for venue, count in venue_usage.items()
            },
            'avg_venues_per_order': sum(
                len(record['venue_selections']) for record in self.routing_history
            ) / total_orders,
            'last_updated': datetime.utcnow().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup smart order router."""
        self.routing_history.clear()
        logger.info("Smart Order Router cleaned up")
