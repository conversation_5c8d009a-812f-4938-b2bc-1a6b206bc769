"""
Real-time risk management with circuit breakers and automated controls.
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
import numpy as np
import pandas as pd

from app.core.config import settings
from app.schemas.execution import (
    OrderRequest, RiskAlert, RiskEventType, PositionSnapshot
)

logger = logging.getLogger(__name__)


class CircuitBreaker:
    """Circuit breaker for automated risk controls."""
    
    def __init__(self, name: str, threshold: Decimal, window_minutes: int = 5):
        """Initialize circuit breaker."""
        self.name = name
        self.threshold = threshold
        self.window_minutes = window_minutes
        self.triggered = False
        self.trigger_time = None
        self.reset_time = None
        self.breach_count = 0
        self.breach_history = []
        
    def check_breach(self, current_value: Decimal) -> bool:
        """Check if threshold is breached."""
        now = datetime.utcnow()
        
        # Clean old breaches
        cutoff_time = now - timedelta(minutes=self.window_minutes)
        self.breach_history = [
            breach_time for breach_time in self.breach_history 
            if breach_time > cutoff_time
        ]
        
        # Check current breach
        if abs(current_value) > self.threshold:
            self.breach_history.append(now)
            self.breach_count = len(self.breach_history)
            
            if not self.triggered:
                self.triggered = True
                self.trigger_time = now
                self.reset_time = now + timedelta(minutes=self.window_minutes)
                logger.warning(f"Circuit breaker {self.name} triggered: {current_value} > {self.threshold}")
                return True
        
        # Check if should reset
        if self.triggered and now > self.reset_time and len(self.breach_history) == 0:
            self.triggered = False
            self.trigger_time = None
            self.reset_time = None
            logger.info(f"Circuit breaker {self.name} reset")
        
        return False
    
    def is_triggered(self) -> bool:
        """Check if circuit breaker is currently triggered."""
        return self.triggered
    
    def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status."""
        return {
            "name": self.name,
            "threshold": float(self.threshold),
            "triggered": self.triggered,
            "trigger_time": self.trigger_time.isoformat() if self.trigger_time else None,
            "reset_time": self.reset_time.isoformat() if self.reset_time else None,
            "breach_count": self.breach_count,
            "window_minutes": self.window_minutes
        }


class PositionRiskMonitor:
    """Monitor position-level risk metrics."""
    
    def __init__(self):
        """Initialize position risk monitor."""
        self.positions = {}
        self.risk_limits = {
            "max_position_size": settings.MAX_POSITION_SIZE,
            "max_concentration": settings.MAX_CONCENTRATION,
            "max_leverage": settings.MAX_LEVERAGE
        }
        
    def update_position(self, position: PositionSnapshot):
        """Update position data."""
        self.positions[position.position_id] = position
        
    def check_position_limits(self, position: PositionSnapshot) -> List[RiskAlert]:
        """Check position against risk limits."""
        alerts = []
        
        # Position size limit
        if abs(position.notional_value) > self.risk_limits["max_position_size"]:
            alerts.append(self._create_alert(
                RiskEventType.POSITION_LIMIT,
                "Position Size Limit Exceeded",
                f"Position {position.symbol} exceeds size limit",
                position.strategy_id,
                position.portfolio_id,
                "max_position_size",
                self.risk_limits["max_position_size"],
                abs(position.notional_value)
            ))
        
        # Concentration limit (requires portfolio context)
        portfolio_value = self._get_portfolio_value(position.portfolio_id)
        if portfolio_value > 0:
            concentration = abs(position.notional_value) / portfolio_value
            if concentration > self.risk_limits["max_concentration"]:
                alerts.append(self._create_alert(
                    RiskEventType.CONCENTRATION_LIMIT,
                    "Concentration Limit Exceeded",
                    f"Position {position.symbol} exceeds concentration limit",
                    position.strategy_id,
                    position.portfolio_id,
                    "max_concentration",
                    self.risk_limits["max_concentration"],
                    concentration
                ))
        
        return alerts
    
    def check_leverage_limits(self, portfolio_id: uuid.UUID) -> List[RiskAlert]:
        """Check portfolio leverage limits."""
        alerts = []
        
        portfolio_positions = [
            pos for pos in self.positions.values() 
            if pos.portfolio_id == portfolio_id
        ]
        
        if not portfolio_positions:
            return alerts
        
        # Calculate total notional and equity
        total_notional = sum(abs(pos.notional_value) for pos in portfolio_positions)
        total_equity = sum(pos.market_value for pos in portfolio_positions if pos.market_value > 0)
        
        if total_equity > 0:
            leverage = total_notional / total_equity
            if leverage > self.risk_limits["max_leverage"]:
                alerts.append(self._create_alert(
                    RiskEventType.LEVERAGE_LIMIT,
                    "Leverage Limit Exceeded",
                    f"Portfolio leverage exceeds limit",
                    None,
                    portfolio_id,
                    "max_leverage",
                    self.risk_limits["max_leverage"],
                    leverage
                ))
        
        return alerts
    
    def _get_portfolio_value(self, portfolio_id: uuid.UUID) -> Decimal:
        """Get total portfolio value."""
        portfolio_positions = [
            pos for pos in self.positions.values() 
            if pos.portfolio_id == portfolio_id
        ]
        return sum(pos.market_value for pos in portfolio_positions)
    
    def _create_alert(
        self,
        event_type: RiskEventType,
        title: str,
        description: str,
        strategy_id: Optional[uuid.UUID],
        portfolio_id: Optional[uuid.UUID],
        risk_metric: str,
        threshold_value: Decimal,
        actual_value: Decimal
    ) -> RiskAlert:
        """Create risk alert."""
        return RiskAlert(
            id=uuid.uuid4(),
            event_id=f"RISK_{uuid.uuid4().hex[:8]}",
            event_type=event_type,
            severity="HIGH",
            category="POSITION_RISK",
            strategy_id=strategy_id,
            portfolio_id=portfolio_id,
            risk_metric=risk_metric,
            threshold_value=threshold_value,
            actual_value=actual_value,
            breach_amount=actual_value - threshold_value,
            title=title,
            description=description,
            auto_resolved=False,
            status="ACTIVE",
            detected_at=datetime.utcnow()
        )


class MarketRiskMonitor:
    """Monitor market-level risk conditions."""
    
    def __init__(self):
        """Initialize market risk monitor."""
        self.market_data = {}
        self.volatility_history = {}
        self.circuit_breakers = {
            "volatility": CircuitBreaker("volatility_spike", settings.VOLATILITY_THRESHOLD),
            "price_move": CircuitBreaker("price_move", settings.PRICE_MOVE_THRESHOLD),
            "volume_spike": CircuitBreaker("volume_spike", settings.VOLUME_SPIKE_THRESHOLD)
        }
        
    def update_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """Update market data for symbol."""
        self.market_data[symbol] = {
            **market_data,
            "timestamp": datetime.utcnow()
        }
        
        # Update volatility history
        if symbol not in self.volatility_history:
            self.volatility_history[symbol] = []
        
        # Calculate volatility if we have price data
        if "last_price" in market_data:
            self._update_volatility(symbol, market_data["last_price"])
    
    def _update_volatility(self, symbol: str, price: Decimal):
        """Update volatility calculation."""
        history = self.volatility_history[symbol]
        history.append({"price": float(price), "timestamp": datetime.utcnow()})
        
        # Keep only recent data (last 100 points)
        if len(history) > 100:
            history.pop(0)
        
        # Calculate volatility if we have enough data
        if len(history) >= 20:
            prices = [point["price"] for point in history[-20:]]
            returns = np.diff(np.log(prices))
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
            
            # Check volatility circuit breaker
            if self.circuit_breakers["volatility"].check_breach(Decimal(str(volatility))):
                logger.warning(f"Volatility spike detected for {symbol}: {volatility:.4f}")
    
    def check_market_conditions(self) -> List[RiskAlert]:
        """Check market conditions for risk events."""
        alerts = []
        
        for symbol, data in self.market_data.items():
            # Check for stale data
            if datetime.utcnow() - data["timestamp"] > timedelta(minutes=5):
                continue
            
            # Check price movements
            if "price_change_pct" in data:
                price_change = abs(Decimal(str(data["price_change_pct"])))
                if self.circuit_breakers["price_move"].check_breach(price_change):
                    alerts.append(self._create_market_alert(
                        RiskEventType.CIRCUIT_BREAKER,
                        f"Large Price Movement - {symbol}",
                        f"Price moved {price_change:.2%} for {symbol}",
                        symbol,
                        "price_movement",
                        settings.PRICE_MOVE_THRESHOLD,
                        price_change
                    ))
            
            # Check volume spikes
            if "volume_ratio" in data:
                volume_ratio = Decimal(str(data["volume_ratio"]))
                if self.circuit_breakers["volume_spike"].check_breach(volume_ratio):
                    alerts.append(self._create_market_alert(
                        RiskEventType.MARKET_DISRUPTION,
                        f"Volume Spike - {symbol}",
                        f"Volume is {volume_ratio:.1f}x normal for {symbol}",
                        symbol,
                        "volume_spike",
                        settings.VOLUME_SPIKE_THRESHOLD,
                        volume_ratio
                    ))
        
        return alerts
    
    def _create_market_alert(
        self,
        event_type: RiskEventType,
        title: str,
        description: str,
        symbol: str,
        risk_metric: str,
        threshold_value: Decimal,
        actual_value: Decimal
    ) -> RiskAlert:
        """Create market risk alert."""
        return RiskAlert(
            id=uuid.uuid4(),
            event_id=f"MARKET_{uuid.uuid4().hex[:8]}",
            event_type=event_type,
            severity="MEDIUM",
            category="MARKET_RISK",
            risk_metric=risk_metric,
            threshold_value=threshold_value,
            actual_value=actual_value,
            breach_amount=actual_value - threshold_value,
            title=title,
            description=f"{description} (Symbol: {symbol})",
            auto_resolved=False,
            status="ACTIVE",
            detected_at=datetime.utcnow()
        )
    
    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get status of all circuit breakers."""
        return {
            name: breaker.get_status() 
            for name, breaker in self.circuit_breakers.items()
        }


class PreTradeRiskChecker:
    """Pre-trade risk validation."""
    
    def __init__(self, position_monitor: PositionRiskMonitor, market_monitor: MarketRiskMonitor):
        """Initialize pre-trade risk checker."""
        self.position_monitor = position_monitor
        self.market_monitor = market_monitor
        
    async def validate_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Validate order against risk limits."""
        try:
            validation_result = {
                "approved": True,
                "risk_score": 0.0,
                "warnings": [],
                "rejections": [],
                "checks_performed": []
            }
            
            # Check if circuit breakers are triggered
            cb_status = self.market_monitor.get_circuit_breaker_status()
            for name, status in cb_status.items():
                if status["triggered"]:
                    validation_result["rejections"].append(
                        f"Circuit breaker {name} is triggered"
                    )
                    validation_result["approved"] = False
            
            # Check order size limits
            order_value = order_request.quantity * (order_request.price or Decimal("100"))
            if order_value > settings.MAX_ORDER_SIZE:
                validation_result["rejections"].append(
                    f"Order size {order_value} exceeds limit {settings.MAX_ORDER_SIZE}"
                )
                validation_result["approved"] = False
            
            validation_result["checks_performed"].extend([
                "circuit_breaker_check",
                "order_size_check"
            ])
            
            # Calculate risk score
            risk_factors = []
            
            # Size risk
            size_risk = min(float(order_value) / float(settings.MAX_ORDER_SIZE), 1.0)
            risk_factors.append(size_risk * 0.3)
            
            # Market volatility risk
            symbol_data = self.market_monitor.market_data.get(order_request.symbol)
            if symbol_data and "volatility" in symbol_data:
                vol_risk = min(symbol_data["volatility"] / 0.5, 1.0)  # Normalize to 50% vol
                risk_factors.append(vol_risk * 0.4)
            
            # Time of day risk (higher risk outside market hours)
            now = datetime.utcnow()
            if now.hour < 9 or now.hour > 16:  # Outside US market hours
                risk_factors.append(0.2)
            
            validation_result["risk_score"] = sum(risk_factors)
            
            # Add warnings for high risk
            if validation_result["risk_score"] > 0.7:
                validation_result["warnings"].append("High risk order")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Pre-trade risk check failed: {e}")
            return {
                "approved": False,
                "risk_score": 1.0,
                "warnings": [],
                "rejections": [f"Risk check error: {str(e)}"],
                "checks_performed": ["error_check"]
            }


class RealTimeRiskManager:
    """Main real-time risk management coordinator."""
    
    def __init__(self):
        """Initialize real-time risk manager."""
        self.position_monitor = PositionRiskMonitor()
        self.market_monitor = MarketRiskMonitor()
        self.pre_trade_checker = PreTradeRiskChecker(self.position_monitor, self.market_monitor)
        self.active_alerts = {}
        self.risk_callbacks = []
        self.monitoring_active = False
        
    async def initialize(self):
        """Initialize risk manager."""
        try:
            self.monitoring_active = True
            
            # Start monitoring tasks
            asyncio.create_task(self._monitor_positions())
            asyncio.create_task(self._monitor_market_conditions())
            
            logger.info("Real-time risk manager initialized")
            
        except Exception as e:
            logger.error(f"Risk manager initialization failed: {e}")
            raise
    
    async def validate_order(self, order_request: OrderRequest) -> Dict[str, Any]:
        """Validate order against risk limits."""
        return await self.pre_trade_checker.validate_order(order_request)
    
    async def update_position(self, position: PositionSnapshot):
        """Update position and check limits."""
        self.position_monitor.update_position(position)
        
        # Check position limits
        alerts = self.position_monitor.check_position_limits(position)
        for alert in alerts:
            await self._process_risk_alert(alert)
        
        # Check leverage limits
        leverage_alerts = self.position_monitor.check_leverage_limits(position.portfolio_id)
        for alert in leverage_alerts:
            await self._process_risk_alert(alert)
    
    async def update_market_data(self, symbol: str, market_data: Dict[str, Any]):
        """Update market data and check conditions."""
        self.market_monitor.update_market_data(symbol, market_data)
        
        # Check market conditions
        alerts = self.market_monitor.check_market_conditions()
        for alert in alerts:
            await self._process_risk_alert(alert)
    
    async def _monitor_positions(self):
        """Monitor positions for risk events."""
        while self.monitoring_active:
            try:
                # Check all positions for risk events
                for position in self.position_monitor.positions.values():
                    alerts = self.position_monitor.check_position_limits(position)
                    for alert in alerts:
                        await self._process_risk_alert(alert)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Position monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_market_conditions(self):
        """Monitor market conditions for risk events."""
        while self.monitoring_active:
            try:
                # Check market conditions
                alerts = self.market_monitor.check_market_conditions()
                for alert in alerts:
                    await self._process_risk_alert(alert)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Market monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _process_risk_alert(self, alert: RiskAlert):
        """Process risk alert and take action."""
        try:
            # Store alert
            self.active_alerts[alert.event_id] = alert
            
            # Log alert
            logger.warning(f"Risk alert: {alert.title} - {alert.description}")
            
            # Determine action based on severity and type
            action_taken = None
            
            if alert.severity == "CRITICAL":
                # Critical alerts may trigger emergency actions
                if alert.event_type == RiskEventType.POSITION_LIMIT:
                    action_taken = "BLOCK_NEW_ORDERS"
                elif alert.event_type == RiskEventType.LOSS_LIMIT:
                    action_taken = "LIQUIDATE_POSITIONS"
            
            elif alert.severity == "HIGH":
                # High severity alerts trigger warnings and monitoring
                action_taken = "INCREASE_MONITORING"
            
            # Update alert with action taken
            alert.action_taken = action_taken
            
            # Notify callbacks
            for callback in self.risk_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"Risk callback failed: {e}")
            
        except Exception as e:
            logger.error(f"Risk alert processing failed: {e}")
    
    def add_risk_callback(self, callback):
        """Add risk event callback."""
        self.risk_callbacks.append(callback)
    
    async def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        return {
            "active_alerts": len(self.active_alerts),
            "circuit_breakers": self.market_monitor.get_circuit_breaker_status(),
            "position_count": len(self.position_monitor.positions),
            "monitoring_active": self.monitoring_active,
            "risk_limits": self.position_monitor.risk_limits,
            "recent_alerts": [
                {
                    "event_id": alert.event_id,
                    "title": alert.title,
                    "severity": alert.severity,
                    "detected_at": alert.detected_at.isoformat()
                }
                for alert in list(self.active_alerts.values())[-10:]
            ]
        }
    
    async def acknowledge_alert(self, event_id: str, user_id: uuid.UUID) -> bool:
        """Acknowledge risk alert."""
        if event_id in self.active_alerts:
            alert = self.active_alerts[event_id]
            alert.status = "ACKNOWLEDGED"
            # TODO: Update in database
            logger.info(f"Risk alert {event_id} acknowledged by {user_id}")
            return True
        return False
    
    async def cleanup(self):
        """Cleanup risk manager."""
        self.monitoring_active = False
        logger.info("Real-time risk manager cleaned up")
