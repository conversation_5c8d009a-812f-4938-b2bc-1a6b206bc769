"""
High-fidelity historical simulation engine with realistic market friction modeling.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from decimal import Decimal
import random
from dataclasses import dataclass

from app.core.config import settings
from app.schemas.backtest import BacktestConfig, TradeSide, FillType

logger = logging.getLogger(__name__)


@dataclass
class MarketState:
    """Current market state for simulation."""
    timestamp: datetime
    price: float
    bid: float
    ask: float
    volume: float
    volatility: float
    liquidity_score: float = 1.0  # 0-1 scale, 1 = high liquidity


@dataclass
class OrderRequest:
    """Order request for execution."""
    symbol: str
    side: TradeSide
    quantity: float
    order_type: str = "MARKET"
    limit_price: Optional[float] = None
    signal_strength: Optional[float] = None


@dataclass
class ExecutionResult:
    """Order execution result."""
    filled_quantity: float
    fill_price: float
    fill_type: FillType
    commission: float
    spread_cost: float
    slippage_cost: float
    market_impact_cost: float
    total_cost: float
    execution_latency_ms: int
    timestamp: datetime


class MarketFrictionModel:
    """Model for realistic market friction simulation."""
    
    def __init__(self, config: BacktestConfig):
        """Initialize market friction model."""
        self.config = config
        self.commission_bps = float(config.commission_bps)
        self.spread_bps = float(config.spread_bps)
        self.slippage_bps = float(config.slippage_bps)
        self.market_impact_factor = float(config.market_impact_factor)
        
    def calculate_spread(self, market_state: MarketState, quantity: float) -> Tuple[float, float]:
        """Calculate bid-ask spread based on market conditions."""
        base_spread_bps = self.spread_bps
        
        # Adjust spread based on volatility
        volatility_multiplier = 1.0 + (market_state.volatility * 2.0)
        
        # Adjust spread based on liquidity
        liquidity_multiplier = 2.0 - market_state.liquidity_score
        
        # Adjust spread based on order size
        size_multiplier = 1.0 + (abs(quantity) / 1000000.0)  # Larger orders = wider spreads
        
        adjusted_spread_bps = base_spread_bps * volatility_multiplier * liquidity_multiplier * size_multiplier
        spread_amount = market_state.price * (adjusted_spread_bps / 10000.0)
        
        bid = market_state.price - (spread_amount / 2.0)
        ask = market_state.price + (spread_amount / 2.0)
        
        return bid, ask
    
    def calculate_slippage(self, market_state: MarketState, side: TradeSide, quantity: float) -> float:
        """Calculate slippage based on market conditions and order characteristics."""
        base_slippage_bps = self.slippage_bps
        
        # Volatility impact
        volatility_impact = market_state.volatility * 0.5
        
        # Liquidity impact
        liquidity_impact = (2.0 - market_state.liquidity_score) * 0.3
        
        # Size impact
        size_impact = min(abs(quantity) / 100000.0, 2.0)  # Cap at 2x
        
        # Time-of-day impact (simulate lower liquidity during off-hours)
        hour = market_state.timestamp.hour
        if hour < 9 or hour > 16:  # Outside main trading hours
            time_impact = 0.5
        else:
            time_impact = 0.0
        
        total_slippage_bps = base_slippage_bps * (1.0 + volatility_impact + liquidity_impact + size_impact + time_impact)
        
        # Random component
        random_factor = random.uniform(0.5, 1.5)
        total_slippage_bps *= random_factor
        
        slippage_amount = market_state.price * (total_slippage_bps / 10000.0)
        
        # Slippage is always adverse to the trade
        if side == TradeSide.BUY:
            return slippage_amount
        else:
            return -slippage_amount
    
    def calculate_market_impact(self, market_state: MarketState, quantity: float) -> float:
        """Calculate market impact based on order size and market conditions."""
        if abs(quantity) < 1000:  # Small orders have minimal impact
            return 0.0
        
        # Base impact proportional to order size
        size_factor = abs(quantity) / 1000000.0  # Normalize to millions
        
        # Liquidity adjustment
        liquidity_factor = 2.0 - market_state.liquidity_score
        
        # Volatility adjustment
        volatility_factor = 1.0 + market_state.volatility
        
        impact_bps = self.market_impact_factor * size_factor * liquidity_factor * volatility_factor
        impact_amount = market_state.price * (impact_bps / 10000.0)
        
        return impact_amount
    
    def calculate_commission(self, quantity: float, price: float) -> float:
        """Calculate commission based on trade value."""
        trade_value = abs(quantity) * price
        commission = trade_value * (self.commission_bps / 10000.0)
        return commission


class ExecutionEngine:
    """Order execution engine with realistic latency and partial fills."""
    
    def __init__(self, config: BacktestConfig):
        """Initialize execution engine."""
        self.config = config
        self.friction_model = MarketFrictionModel(config)
        
    def simulate_execution_latency(self) -> int:
        """Simulate execution latency."""
        if not self.config.enable_latency_simulation:
            return 0
        
        min_latency = self.config.min_execution_latency_ms
        max_latency = self.config.max_execution_latency_ms
        
        # Use log-normal distribution for realistic latency
        mean_log = np.log((min_latency + max_latency) / 2)
        std_log = 0.5
        latency = int(np.random.lognormal(mean_log, std_log))
        
        return max(min_latency, min(max_latency, latency))
    
    def determine_fill_type(self, market_state: MarketState, quantity: float) -> Tuple[FillType, float]:
        """Determine if order gets full or partial fill."""
        if not self.config.enable_partial_fills:
            return FillType.FULL, quantity
        
        # Factors affecting fill probability
        size_factor = min(abs(quantity) / 100000.0, 1.0)  # Larger orders more likely to be partial
        liquidity_factor = market_state.liquidity_score
        volatility_factor = min(market_state.volatility * 2.0, 1.0)
        
        # Probability of partial fill
        partial_prob = size_factor * (1.0 - liquidity_factor) * (1.0 + volatility_factor) * 0.3
        
        if random.random() < partial_prob:
            # Partial fill - fill between 50% and 95% of order
            fill_ratio = random.uniform(0.5, 0.95)
            filled_quantity = quantity * fill_ratio
            return FillType.PARTIAL, filled_quantity
        else:
            return FillType.FULL, quantity
    
    def execute_order(self, order: OrderRequest, market_state: MarketState) -> ExecutionResult:
        """Execute order with realistic market friction."""
        # Simulate execution latency
        latency = self.simulate_execution_latency()
        
        # Determine fill type and quantity
        fill_type, filled_quantity = self.determine_fill_type(market_state, order.quantity)
        
        # Calculate bid-ask spread
        bid, ask = self.friction_model.calculate_spread(market_state, filled_quantity)
        
        # Determine execution price based on side
        if order.side == TradeSide.BUY:
            base_price = ask
        else:
            base_price = bid
        
        # Calculate slippage
        slippage = self.friction_model.calculate_slippage(market_state, order.side, filled_quantity)
        
        # Calculate market impact
        market_impact = self.friction_model.calculate_market_impact(market_state, filled_quantity)
        if order.side == TradeSide.SELL:
            market_impact = -market_impact
        
        # Final execution price
        fill_price = base_price + slippage + market_impact
        
        # Calculate costs
        commission = self.friction_model.calculate_commission(filled_quantity, fill_price)
        spread_cost = abs(market_state.price - base_price) * abs(filled_quantity)
        slippage_cost = abs(slippage) * abs(filled_quantity)
        market_impact_cost = abs(market_impact) * abs(filled_quantity)
        total_cost = commission + spread_cost + slippage_cost + market_impact_cost
        
        # Execution timestamp (add latency)
        execution_time = market_state.timestamp + timedelta(milliseconds=latency)
        
        return ExecutionResult(
            filled_quantity=filled_quantity,
            fill_price=fill_price,
            fill_type=fill_type,
            commission=commission,
            spread_cost=spread_cost,
            slippage_cost=slippage_cost,
            market_impact_cost=market_impact_cost,
            total_cost=total_cost,
            execution_latency_ms=latency,
            timestamp=execution_time
        )


class PortfolioManager:
    """Portfolio state management during backtesting."""
    
    def __init__(self, initial_capital: float, config: BacktestConfig):
        """Initialize portfolio manager."""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.config = config
        
        # Position tracking
        self.positions: Dict[str, float] = {}  # symbol -> quantity
        self.avg_prices: Dict[str, float] = {}  # symbol -> average price
        
        # Portfolio metrics
        self.total_value = initial_capital
        self.unrealized_pnl = 0.0
        self.realized_pnl = 0.0
        self.total_commission = 0.0
        self.total_slippage = 0.0
        
        # Risk tracking
        self.max_position_value = 0.0
        self.current_leverage = 1.0
        
        # Trade tracking
        self.trade_count = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
    def get_position(self, symbol: str) -> float:
        """Get current position for symbol."""
        return self.positions.get(symbol, 0.0)
    
    def get_position_value(self, symbol: str, current_price: float) -> float:
        """Get current position value for symbol."""
        position = self.get_position(symbol)
        return position * current_price
    
    def calculate_portfolio_value(self, market_prices: Dict[str, float]) -> float:
        """Calculate total portfolio value."""
        total_value = self.current_capital
        
        for symbol, position in self.positions.items():
            if symbol in market_prices and position != 0:
                position_value = position * market_prices[symbol]
                total_value += position_value
        
        return total_value
    
    def check_position_limits(self, symbol: str, new_quantity: float, current_price: float) -> bool:
        """Check if new position would violate limits."""
        current_position = self.get_position(symbol)
        total_position = current_position + new_quantity
        
        # Check maximum position size
        position_value = abs(total_position * current_price)
        max_allowed_value = self.total_value * float(self.config.max_position_size)
        
        if position_value > max_allowed_value:
            logger.warning(f"Position limit exceeded for {symbol}: {position_value} > {max_allowed_value}")
            return False
        
        # Check leverage limits
        total_exposure = sum(abs(pos * current_price) for pos in self.positions.values())
        total_exposure += abs(new_quantity * current_price)
        leverage = total_exposure / self.current_capital if self.current_capital > 0 else float('inf')
        
        if leverage > float(self.config.max_leverage):
            logger.warning(f"Leverage limit exceeded: {leverage} > {self.config.max_leverage}")
            return False
        
        return True
    
    def execute_trade(self, symbol: str, execution: ExecutionResult) -> Dict[str, Any]:
        """Execute trade and update portfolio state."""
        # Check if we have enough capital for the trade
        trade_cost = abs(execution.filled_quantity * execution.fill_price) + execution.total_cost
        
        if execution.filled_quantity > 0:  # Buy order
            if trade_cost > self.current_capital:
                logger.warning(f"Insufficient capital for trade: {trade_cost} > {self.current_capital}")
                return {"success": False, "reason": "insufficient_capital"}
        
        # Update position
        current_position = self.get_position(symbol)
        new_position = current_position + execution.filled_quantity
        
        # Update average price for position tracking
        if symbol in self.positions and self.positions[symbol] != 0:
            current_value = self.positions[symbol] * self.avg_prices.get(symbol, 0)
            new_value = execution.filled_quantity * execution.fill_price
            total_quantity = self.positions[symbol] + execution.filled_quantity
            
            if total_quantity != 0:
                self.avg_prices[symbol] = (current_value + new_value) / total_quantity
            else:
                self.avg_prices[symbol] = execution.fill_price
        else:
            self.avg_prices[symbol] = execution.fill_price
        
        self.positions[symbol] = new_position
        
        # Update capital
        if execution.filled_quantity > 0:  # Buy
            self.current_capital -= (execution.filled_quantity * execution.fill_price + execution.total_cost)
        else:  # Sell
            self.current_capital += (abs(execution.filled_quantity) * execution.fill_price - execution.total_cost)
        
        # Update costs
        self.total_commission += execution.commission
        self.total_slippage += execution.slippage_cost
        
        # Update trade statistics
        self.trade_count += 1
        
        # Calculate realized P&L for closed positions
        realized_pnl = 0.0
        if current_position != 0 and new_position * current_position <= 0:  # Position closed or reversed
            # Calculate P&L on the closed portion
            closed_quantity = min(abs(current_position), abs(execution.filled_quantity))
            if current_position > 0:  # Closing long position
                realized_pnl = closed_quantity * (execution.fill_price - self.avg_prices[symbol])
            else:  # Closing short position
                realized_pnl = closed_quantity * (self.avg_prices[symbol] - execution.fill_price)
            
            self.realized_pnl += realized_pnl
            
            if realized_pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1
        
        return {
            "success": True,
            "new_position": new_position,
            "realized_pnl": realized_pnl,
            "portfolio_value": self.current_capital,
            "trade_cost": execution.total_cost
        }


class SimulationEngine:
    """Main simulation engine coordinating all components."""
    
    def __init__(self):
        """Initialize simulation engine."""
        self.execution_engine = None
        self.portfolio_manager = None
        self.market_data = None
        self.current_bar_index = 0
        
    async def cleanup(self):
        """Cleanup simulation engine resources."""
        pass
    
    def initialize(self, config: BacktestConfig, initial_capital: float, market_data: pd.DataFrame):
        """Initialize simulation with configuration and data."""
        self.execution_engine = ExecutionEngine(config)
        self.portfolio_manager = PortfolioManager(initial_capital, config)
        self.market_data = market_data
        self.current_bar_index = 0
        
        logger.info(f"Simulation engine initialized with {len(market_data)} bars")
    
    def get_current_market_state(self, symbol: str) -> MarketState:
        """Get current market state for symbol."""
        if self.current_bar_index >= len(self.market_data):
            raise IndexError("No more market data available")
        
        current_bar = self.market_data.iloc[self.current_bar_index]
        
        # Calculate volatility (rolling standard deviation of returns)
        if self.current_bar_index >= 20:
            recent_closes = self.market_data['close'].iloc[self.current_bar_index-20:self.current_bar_index]
            returns = recent_closes.pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # Annualized
        else:
            volatility = 0.02  # Default 2% volatility
        
        # Calculate liquidity score based on volume
        avg_volume = self.market_data['volume'].rolling(20).mean().iloc[self.current_bar_index]
        current_volume = current_bar['volume']
        liquidity_score = min(current_volume / avg_volume, 2.0) / 2.0 if avg_volume > 0 else 0.5
        
        return MarketState(
            timestamp=current_bar['time'],
            price=current_bar['close'],
            bid=current_bar['close'] * 0.9999,  # Approximate bid
            ask=current_bar['close'] * 1.0001,  # Approximate ask
            volume=current_bar['volume'],
            volatility=volatility,
            liquidity_score=liquidity_score
        )
    
    def process_signal(self, symbol: str, signal: float, signal_strength: Optional[float] = None) -> Optional[ExecutionResult]:
        """Process trading signal and execute order if valid."""
        if abs(signal) < 0.01:  # Ignore very weak signals
            return None
        
        market_state = self.get_current_market_state(symbol)
        
        # Determine order side and quantity
        if signal > 0:
            side = TradeSide.BUY
        else:
            side = TradeSide.SELL
        
        # Calculate position size based on signal strength and portfolio value
        portfolio_value = self.portfolio_manager.calculate_portfolio_value({symbol: market_state.price})
        max_position_value = portfolio_value * float(self.portfolio_manager.config.max_position_size)
        
        # Scale quantity by signal strength
        signal_magnitude = abs(signal)
        target_value = max_position_value * signal_magnitude
        quantity = target_value / market_state.price
        
        if side == TradeSide.SELL:
            quantity = -quantity
        
        # Check position limits
        if not self.portfolio_manager.check_position_limits(symbol, quantity, market_state.price):
            logger.warning(f"Position limits prevent trade execution for {symbol}")
            return None
        
        # Create and execute order
        order = OrderRequest(
            symbol=symbol,
            side=side,
            quantity=quantity,
            signal_strength=signal_strength
        )
        
        execution = self.execution_engine.execute_order(order, market_state)
        
        # Update portfolio
        trade_result = self.portfolio_manager.execute_trade(symbol, execution)
        
        if trade_result["success"]:
            logger.debug(f"Trade executed: {symbol} {side.value} {execution.filled_quantity:.4f} @ {execution.fill_price:.4f}")
            return execution
        else:
            logger.warning(f"Trade failed: {trade_result['reason']}")
            return None
    
    def advance_time(self) -> bool:
        """Advance to next time step."""
        self.current_bar_index += 1
        return self.current_bar_index < len(self.market_data)
    
    def get_portfolio_snapshot(self, market_prices: Dict[str, float]) -> Dict[str, Any]:
        """Get current portfolio snapshot."""
        total_value = self.portfolio_manager.calculate_portfolio_value(market_prices)
        
        return {
            "timestamp": self.market_data.iloc[self.current_bar_index]['time'] if self.current_bar_index < len(self.market_data) else None,
            "total_value": total_value,
            "cash": self.portfolio_manager.current_capital,
            "positions": dict(self.portfolio_manager.positions),
            "unrealized_pnl": total_value - self.portfolio_manager.initial_capital - self.portfolio_manager.realized_pnl,
            "realized_pnl": self.portfolio_manager.realized_pnl,
            "total_commission": self.portfolio_manager.total_commission,
            "total_slippage": self.portfolio_manager.total_slippage,
            "trade_count": self.portfolio_manager.trade_count,
            "winning_trades": self.portfolio_manager.winning_trades,
            "losing_trades": self.portfolio_manager.losing_trades
        }
