"""
Strategy integration module for connecting with Strategy Genesis service.
"""

import logging
import asyncio
import httpx
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
import uuid
from decimal import Decimal

from app.core.config import settings

logger = logging.getLogger(__name__)


class StrategyClient:
    """Client for communicating with Strategy Genesis service."""
    
    def __init__(self, base_url: str = None):
        """Initialize strategy client."""
        self.base_url = base_url or settings.STRATEGY_GENESIS_URL
        self.timeout = httpx.Timeout(30.0)
    
    async def get_strategy(self, strategy_id: uuid.UUID, user_id: uuid.UUID) -> Dict[str, Any]:
        """Get strategy information from Strategy Genesis."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.get(
                f"{self.base_url}/strategies/{strategy_id}",
                headers={"X-User-ID": str(user_id)}
            )
            response.raise_for_status()
            return response.json()
    
    async def generate_signal(
        self,
        strategy_id: uuid.UUID,
        user_id: uuid.UUID,
        market_data: Dict[str, Any]
    ) -> float:
        """Generate trading signal from strategy."""
        strategy = await self.get_strategy(strategy_id, user_id)
        ai_paradigm = strategy.get("ai_paradigm")
        
        if ai_paradigm == "GP":
            return await self._generate_gp_signal(strategy_id, user_id, market_data)
        elif ai_paradigm == "RL":
            return await self._generate_rl_signal(strategy_id, user_id, market_data)
        elif ai_paradigm == "DL":
            return await self._generate_dl_signal(strategy_id, user_id, market_data)
        else:
            logger.warning(f"Unknown AI paradigm: {ai_paradigm}")
            return 0.0
    
    async def _generate_gp_signal(
        self,
        strategy_id: uuid.UUID,
        user_id: uuid.UUID,
        market_data: Dict[str, Any]
    ) -> float:
        """Generate signal from Genetic Programming strategy."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.base_url}/genetic-programming/predict/{strategy_id}",
                headers={"X-User-ID": str(user_id)},
                json=market_data
            )
            response.raise_for_status()
            result = response.json()
            return float(result.get("signal", 0.0))
    
    async def _generate_rl_signal(
        self,
        strategy_id: uuid.UUID,
        user_id: uuid.UUID,
        market_data: Dict[str, Any]
    ) -> float:
        """Generate signal from Reinforcement Learning strategy."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.base_url}/reinforcement-learning/predict/{strategy_id}",
                headers={"X-User-ID": str(user_id)},
                json=market_data
            )
            response.raise_for_status()
            result = response.json()
            return float(result.get("signal", 0.0))
    
    async def _generate_dl_signal(
        self,
        strategy_id: uuid.UUID,
        user_id: uuid.UUID,
        market_data: Dict[str, Any]
    ) -> float:
        """Generate signal from Deep Learning strategy."""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.base_url}/deep-learning/predict/{strategy_id}",
                headers={"X-User-ID": str(user_id)},
                json=market_data
            )
            response.raise_for_status()
            result = response.json()
            return float(result.get("signal", 0.0))


class SignalProcessor:
    """Process and interpret trading signals from strategies."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize signal processor."""
        self.config = config or {}
        self.signal_history = []
        self.max_history_length = 100
    
    def process_signal(
        self,
        raw_signal: float,
        market_context: Dict[str, Any],
        strategy_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process raw signal into actionable trading decision."""
        
        # Normalize signal to [-1, 1] range
        normalized_signal = self._normalize_signal(raw_signal)
        
        # Apply signal filtering
        filtered_signal = self._apply_signal_filters(normalized_signal, market_context)
        
        # Calculate confidence
        confidence = self._calculate_confidence(filtered_signal, market_context, strategy_metadata)
        
        # Determine action
        action = self._determine_action(filtered_signal, confidence)
        
        # Store in history
        signal_data = {
            'timestamp': datetime.utcnow(),
            'raw_signal': raw_signal,
            'normalized_signal': normalized_signal,
            'filtered_signal': filtered_signal,
            'confidence': confidence,
            'action': action,
            'market_context': market_context
        }
        
        self._update_signal_history(signal_data)
        
        return {
            'signal': filtered_signal,
            'confidence': confidence,
            'action': action,
            'metadata': {
                'raw_signal': raw_signal,
                'signal_strength': abs(filtered_signal),
                'processing_timestamp': signal_data['timestamp'].isoformat()
            }
        }
    
    def _normalize_signal(self, raw_signal: float) -> float:
        """Normalize signal to [-1, 1] range."""
        # Clip extreme values
        clipped = max(-10.0, min(10.0, raw_signal))
        
        # Apply tanh normalization for smooth bounds
        normalized = np.tanh(clipped)
        
        return float(normalized)
    
    def _apply_signal_filters(self, signal: float, market_context: Dict[str, Any]) -> float:
        """Apply various filters to the signal."""
        filtered_signal = signal
        
        # Volatility filter - reduce signal strength in high volatility
        volatility = market_context.get('volatility', 0.02)
        if volatility > 0.05:  # High volatility threshold
            vol_factor = max(0.3, 1.0 - (volatility - 0.05) * 5.0)
            filtered_signal *= vol_factor
        
        # Liquidity filter - reduce signal strength in low liquidity
        liquidity_score = market_context.get('liquidity_score', 1.0)
        if liquidity_score < 0.5:  # Low liquidity threshold
            liquidity_factor = max(0.2, liquidity_score * 2.0)
            filtered_signal *= liquidity_factor
        
        # Time-based filter - reduce signals during off-hours
        current_hour = datetime.utcnow().hour
        if current_hour < 8 or current_hour > 18:  # Outside main trading hours
            filtered_signal *= 0.7
        
        # Signal strength threshold - ignore very weak signals
        if abs(filtered_signal) < 0.05:
            filtered_signal = 0.0
        
        return filtered_signal
    
    def _calculate_confidence(
        self,
        signal: float,
        market_context: Dict[str, Any],
        strategy_metadata: Dict[str, Any]
    ) -> float:
        """Calculate confidence in the signal."""
        base_confidence = abs(signal)  # Signal strength as base confidence
        
        # Strategy performance factor
        performance_score = strategy_metadata.get('performance_score', 0.5)
        performance_factor = min(1.0, max(0.1, float(performance_score)))
        
        # Market condition factor
        volatility = market_context.get('volatility', 0.02)
        vol_factor = max(0.5, 1.0 - volatility * 2.0)  # Lower confidence in high volatility
        
        # Signal consistency factor (based on recent signal history)
        consistency_factor = self._calculate_signal_consistency()
        
        # Combined confidence
        confidence = base_confidence * performance_factor * vol_factor * consistency_factor
        
        return min(1.0, max(0.0, confidence))
    
    def _determine_action(self, signal: float, confidence: float) -> str:
        """Determine trading action based on signal and confidence."""
        if confidence < 0.3:
            return "HOLD"
        
        if signal > 0.1:
            return "BUY"
        elif signal < -0.1:
            return "SELL"
        else:
            return "HOLD"
    
    def _calculate_signal_consistency(self) -> float:
        """Calculate signal consistency based on recent history."""
        if len(self.signal_history) < 3:
            return 1.0  # Default to high consistency with limited history
        
        recent_signals = [s['filtered_signal'] for s in self.signal_history[-5:]]
        
        # Calculate standard deviation of recent signals
        signal_std = np.std(recent_signals)
        
        # Convert to consistency score (lower std = higher consistency)
        consistency = max(0.3, 1.0 - signal_std)
        
        return consistency
    
    def _update_signal_history(self, signal_data: Dict[str, Any]):
        """Update signal history with new data."""
        self.signal_history.append(signal_data)
        
        # Maintain maximum history length
        if len(self.signal_history) > self.max_history_length:
            self.signal_history = self.signal_history[-self.max_history_length:]


class PositionSizer:
    """Calculate position sizes based on signals and risk management."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize position sizer."""
        self.max_position_size = float(config.get('max_position_size', 0.2))
        self.risk_per_trade = float(config.get('risk_per_trade', 0.02))
        self.volatility_scaling = config.get('volatility_scaling', True)
        self.kelly_criterion = config.get('use_kelly_criterion', False)
    
    def calculate_position_size(
        self,
        signal: float,
        confidence: float,
        portfolio_value: float,
        current_price: float,
        market_context: Dict[str, Any],
        strategy_stats: Dict[str, Any] = None
    ) -> float:
        """Calculate position size for the signal."""
        
        # Base position size from signal strength
        base_size = abs(signal) * self.max_position_size
        
        # Adjust for confidence
        confidence_adjusted_size = base_size * confidence
        
        # Volatility adjustment
        if self.volatility_scaling:
            volatility = market_context.get('volatility', 0.02)
            vol_adjustment = min(2.0, max(0.5, 0.02 / volatility))  # Inverse volatility scaling
            confidence_adjusted_size *= vol_adjustment
        
        # Kelly criterion adjustment (if enabled and stats available)
        if self.kelly_criterion and strategy_stats:
            kelly_fraction = self._calculate_kelly_fraction(strategy_stats)
            confidence_adjusted_size *= kelly_fraction
        
        # Risk-based position sizing
        risk_adjusted_size = self._apply_risk_sizing(
            confidence_adjusted_size, portfolio_value, current_price, market_context
        )
        
        # Apply signal direction
        if signal < 0:
            risk_adjusted_size = -risk_adjusted_size
        
        return risk_adjusted_size
    
    def _calculate_kelly_fraction(self, strategy_stats: Dict[str, Any]) -> float:
        """Calculate Kelly criterion fraction."""
        win_rate = strategy_stats.get('win_rate', 0.5)
        avg_win = strategy_stats.get('avg_win', 0.01)
        avg_loss = strategy_stats.get('avg_loss', -0.01)
        
        if avg_loss >= 0:  # Avoid division by zero or negative
            return 0.5  # Conservative default
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/abs(avg_loss), p = win_rate, q = 1-win_rate
        b = avg_win / abs(avg_loss)
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Cap Kelly fraction to prevent over-leveraging
        return max(0.0, min(0.5, kelly_fraction))
    
    def _apply_risk_sizing(
        self,
        base_size: float,
        portfolio_value: float,
        current_price: float,
        market_context: Dict[str, Any]
    ) -> float:
        """Apply risk-based position sizing."""
        
        # Calculate position value
        position_value = abs(base_size) * portfolio_value
        
        # Risk per trade limit
        max_risk_value = portfolio_value * self.risk_per_trade
        
        # ATR-based stop loss for risk calculation
        atr = market_context.get('atr', current_price * 0.01)  # Default 1% ATR
        stop_distance = atr * 2.0  # 2 ATR stop loss
        
        # Calculate maximum position size based on risk
        if stop_distance > 0:
            max_position_by_risk = max_risk_value / stop_distance
            max_position_value_by_risk = max_position_by_risk * current_price
            
            # Use the more conservative limit
            if position_value > max_position_value_by_risk:
                risk_factor = max_position_value_by_risk / position_value
                base_size *= risk_factor
        
        return base_size


class RiskManager:
    """Risk management overlay for position and portfolio management."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize risk manager."""
        self.max_portfolio_risk = float(config.get('max_portfolio_risk', 0.1))
        self.max_correlation_exposure = float(config.get('max_correlation_exposure', 0.3))
        self.stop_loss_pct = config.get('stop_loss_pct')
        self.take_profit_pct = config.get('take_profit_pct')
        self.max_drawdown_limit = float(config.get('max_drawdown_limit', 0.2))
        
        # Risk state tracking
        self.current_drawdown = 0.0
        self.peak_portfolio_value = 0.0
        self.risk_budget_used = 0.0
    
    def check_risk_limits(
        self,
        proposed_position: float,
        symbol: str,
        current_price: float,
        portfolio_state: Dict[str, Any],
        market_context: Dict[str, Any]
    ) -> Tuple[bool, str, float]:
        """Check if proposed position violates risk limits."""
        
        # Update portfolio tracking
        current_value = portfolio_state.get('total_value', 100000.0)
        self._update_drawdown_tracking(current_value)
        
        # Check maximum drawdown limit
        if self.current_drawdown > self.max_drawdown_limit:
            return False, "Maximum drawdown limit exceeded", 0.0
        
        # Check position size limits
        position_value = abs(proposed_position * current_price)
        portfolio_value = portfolio_state.get('total_value', 100000.0)
        position_pct = position_value / portfolio_value
        
        max_position_pct = float(settings.MAX_POSITION_SIZE)
        if position_pct > max_position_pct:
            # Scale down position to fit limit
            scale_factor = max_position_pct / position_pct
            adjusted_position = proposed_position * scale_factor
            return True, f"Position scaled down to fit size limit", adjusted_position
        
        # Check portfolio risk budget
        current_positions = portfolio_state.get('positions', {})
        total_exposure = sum(abs(pos * current_price) for pos in current_positions.values())
        new_exposure = total_exposure + position_value
        
        max_exposure = portfolio_value * self.max_portfolio_risk
        if new_exposure > max_exposure:
            return False, "Portfolio risk budget exceeded", 0.0
        
        # Check correlation limits (simplified - would need correlation matrix in practice)
        # For now, just check if we're not over-concentrating in similar assets
        similar_positions = sum(
            abs(pos * current_price) for sym, pos in current_positions.items()
            if self._are_correlated(symbol, sym)
        )
        
        if (similar_positions + position_value) > portfolio_value * self.max_correlation_exposure:
            return False, "Correlation exposure limit exceeded", 0.0
        
        return True, "Risk checks passed", proposed_position
    
    def calculate_stop_loss_take_profit(
        self,
        entry_price: float,
        position_size: float,
        market_context: Dict[str, Any]
    ) -> Tuple[Optional[float], Optional[float]]:
        """Calculate stop loss and take profit levels."""
        
        stop_loss = None
        take_profit = None
        
        if self.stop_loss_pct:
            if position_size > 0:  # Long position
                stop_loss = entry_price * (1 - float(self.stop_loss_pct))
            else:  # Short position
                stop_loss = entry_price * (1 + float(self.stop_loss_pct))
        
        if self.take_profit_pct:
            if position_size > 0:  # Long position
                take_profit = entry_price * (1 + float(self.take_profit_pct))
            else:  # Short position
                take_profit = entry_price * (1 - float(self.take_profit_pct))
        
        return stop_loss, take_profit
    
    def _update_drawdown_tracking(self, current_value: float):
        """Update drawdown tracking."""
        if current_value > self.peak_portfolio_value:
            self.peak_portfolio_value = current_value
        
        if self.peak_portfolio_value > 0:
            self.current_drawdown = (self.peak_portfolio_value - current_value) / self.peak_portfolio_value
        else:
            self.current_drawdown = 0.0
    
    def _are_correlated(self, symbol1: str, symbol2: str) -> bool:
        """Simple correlation check (would be more sophisticated in practice)."""
        # For FOREX pairs, check if they share a common currency
        if len(symbol1) == 6 and len(symbol2) == 6:  # FOREX format
            base1, quote1 = symbol1[:3], symbol1[3:]
            base2, quote2 = symbol2[:3], symbol2[3:]
            
            return base1 in [base2, quote2] or quote1 in [base2, quote2]
        
        return False


class StrategyIntegrator:
    """Main strategy integration coordinator."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize strategy integrator."""
        self.config = config
        self.strategy_client = StrategyClient()
        self.signal_processor = SignalProcessor(config)
        self.position_sizer = PositionSizer(config)
        self.risk_manager = RiskManager(config)
    
    async def process_strategy_signal(
        self,
        strategy_id: uuid.UUID,
        user_id: uuid.UUID,
        market_data: Dict[str, Any],
        portfolio_state: Dict[str, Any],
        strategy_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process complete strategy signal pipeline."""
        
        try:
            # Generate raw signal from strategy
            raw_signal = await self.strategy_client.generate_signal(
                strategy_id, user_id, market_data
            )
            
            # Process signal
            processed_signal = self.signal_processor.process_signal(
                raw_signal, market_data, strategy_metadata
            )
            
            # Calculate position size
            current_price = market_data.get('close', market_data.get('price', 0.0))
            portfolio_value = portfolio_state.get('total_value', 100000.0)
            
            position_size = self.position_sizer.calculate_position_size(
                processed_signal['signal'],
                processed_signal['confidence'],
                portfolio_value,
                current_price,
                market_data,
                strategy_metadata.get('trade_stats')
            )
            
            # Apply risk management
            symbol = market_data.get('symbol', 'UNKNOWN')
            risk_approved, risk_message, final_position = self.risk_manager.check_risk_limits(
                position_size, symbol, current_price, portfolio_state, market_data
            )
            
            # Calculate stop loss and take profit
            stop_loss, take_profit = None, None
            if risk_approved and abs(final_position) > 0:
                stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                    current_price, final_position, market_data
                )
            
            return {
                'strategy_id': str(strategy_id),
                'raw_signal': raw_signal,
                'processed_signal': processed_signal,
                'position_size': final_position if risk_approved else 0.0,
                'risk_approved': risk_approved,
                'risk_message': risk_message,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'timestamp': datetime.utcnow().isoformat(),
                'market_context': {
                    'symbol': symbol,
                    'price': current_price,
                    'volatility': market_data.get('volatility'),
                    'liquidity_score': market_data.get('liquidity_score')
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing strategy signal: {e}")
            return {
                'strategy_id': str(strategy_id),
                'error': str(e),
                'position_size': 0.0,
                'risk_approved': False,
                'timestamp': datetime.utcnow().isoformat()
            }
