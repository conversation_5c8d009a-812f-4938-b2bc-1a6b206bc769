"""
Tests for execution gateway functionality.
"""

import pytest
import uuid
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch

from app.engine.execution_gateway import ExecutionGateway, AlpacaAdapter, InteractiveBrokersAdapter
from app.schemas.execution import OrderRequest, OrderSide, OrderType, AssetClass, TimeInForce


@pytest.fixture
def sample_order_request():
    """Create sample order request for testing."""
    return OrderRequest(
        strategy_id=uuid.uuid4(),
        symbol="AAPL",
        asset_class=AssetClass.EQUITY,
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        time_in_force=TimeInForce.DAY,
        quantity=Decimal("100"),
        execution_algo="MARKET"
    )


class TestAlpacaAdapter:
    """Test Alpaca broker adapter."""
    
    @pytest.mark.asyncio
    async def test_alpaca_initialization(self):
        """Test Alpaca adapter initialization."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.ALPACA_API_KEY = "test_key"
            mock_settings.ALPACA_SECRET_KEY = "test_secret"
            mock_settings.ALPACA_BASE_URL = "https://paper-api.alpaca.markets"
            
            adapter = AlpacaAdapter()
            
            assert adapter.api_key == "test_key"
            assert adapter.secret_key == "test_secret"
            assert adapter.base_url == "https://paper-api.alpaca.markets"
    
    @pytest.mark.asyncio
    async def test_alpaca_order_submission(self, sample_order_request):
        """Test Alpaca order submission."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.ALPACA_API_KEY = "test_key"
            mock_settings.ALPACA_SECRET_KEY = "test_secret"
            mock_settings.ALPACA_BASE_URL = "https://paper-api.alpaca.markets"
            
            adapter = AlpacaAdapter()
            
            # Mock HTTP response
            mock_response = Mock()
            mock_response.status_code = 201
            mock_response.json.return_value = {"id": "test_order_id"}
            
            with patch.object(adapter.client, 'post', return_value=mock_response):
                result = await adapter.submit_order(sample_order_request)
                
                assert result["success"] is True
                assert result["broker_order_id"] == "test_order_id"
                assert result["status"] == "SUBMITTED"
    
    @pytest.mark.asyncio
    async def test_alpaca_order_cancellation(self):
        """Test Alpaca order cancellation."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.ALPACA_API_KEY = "test_key"
            mock_settings.ALPACA_SECRET_KEY = "test_secret"
            mock_settings.ALPACA_BASE_URL = "https://paper-api.alpaca.markets"
            
            adapter = AlpacaAdapter()
            
            # Mock HTTP response
            mock_response = Mock()
            mock_response.status_code = 204
            
            with patch.object(adapter.client, 'delete', return_value=mock_response):
                result = await adapter.cancel_order("test_order_id")
                
                assert result["success"] is True
                assert result["status"] == "CANCELLED"


class TestInteractiveBrokersAdapter:
    """Test Interactive Brokers adapter."""
    
    @pytest.mark.asyncio
    async def test_ib_initialization(self):
        """Test IB adapter initialization."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.IB_HOST = "127.0.0.1"
            mock_settings.IB_PORT = 7497
            mock_settings.IB_ACCOUNT = "DU123456"
            
            adapter = InteractiveBrokersAdapter()
            
            assert adapter.base_url == "https://localhost:7497/v1/api"
            assert adapter.account == "DU123456"
    
    @pytest.mark.asyncio
    async def test_ib_authentication_check(self):
        """Test IB authentication check."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.IB_HOST = "127.0.0.1"
            mock_settings.IB_PORT = 7497
            mock_settings.IB_ACCOUNT = "DU123456"
            
            adapter = InteractiveBrokersAdapter()
            
            # Mock successful authentication
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"authenticated": True}
            
            with patch.object(adapter.client, 'get', return_value=mock_response):
                result = await adapter.initialize()
                
                assert result is True
    
    @pytest.mark.asyncio
    async def test_ib_order_submission(self, sample_order_request):
        """Test IB order submission."""
        with patch('app.engine.execution_gateway.settings') as mock_settings:
            mock_settings.IB_HOST = "127.0.0.1"
            mock_settings.IB_PORT = 7497
            mock_settings.IB_ACCOUNT = "DU123456"
            
            adapter = InteractiveBrokersAdapter()
            
            # Mock contract ID lookup
            contract_response = Mock()
            contract_response.status_code = 200
            contract_response.json.return_value = [{"conid": 265598}]
            
            # Mock order submission
            order_response = Mock()
            order_response.status_code = 200
            order_response.json.return_value = [{"order_id": "test_ib_order"}]
            
            with patch.object(adapter.client, 'get', return_value=contract_response), \
                 patch.object(adapter.client, 'post', return_value=order_response):
                
                result = await adapter.submit_order(sample_order_request)
                
                assert result["success"] is True
                assert result["broker_order_id"] == "test_ib_order"
                assert result["status"] == "SUBMITTED"


class TestExecutionGateway:
    """Test main execution gateway."""
    
    @pytest.mark.asyncio
    async def test_gateway_initialization(self):
        """Test execution gateway initialization."""
        gateway = ExecutionGateway()
        
        # Mock broker adapters
        with patch('app.engine.execution_gateway.InteractiveBrokersAdapter') as mock_ib, \
             patch('app.engine.execution_gateway.AlpacaAdapter') as mock_alpaca, \
             patch('app.engine.execution_gateway.settings') as mock_settings:
            
            mock_settings.SUPPORTED_BROKERS = ["INTERACTIVE_BROKERS", "ALPACA"]
            mock_settings.FIX_CONFIG_FILE = None
            
            # Mock adapter initialization
            mock_ib_instance = AsyncMock()
            mock_ib_instance.initialize.return_value = True
            mock_ib.return_value = mock_ib_instance
            
            mock_alpaca_instance = AsyncMock()
            mock_alpaca_instance.initialize.return_value = True
            mock_alpaca.return_value = mock_alpaca_instance
            
            await gateway.initialize()
            
            assert len(gateway.brokers) == 2
            assert "INTERACTIVE_BROKERS" in gateway.brokers
            assert "ALPACA" in gateway.brokers
    
    @pytest.mark.asyncio
    async def test_order_submission(self, sample_order_request):
        """Test order submission through gateway."""
        gateway = ExecutionGateway()
        
        # Mock broker adapter
        mock_adapter = AsyncMock()
        mock_adapter.submit_order.return_value = {
            "success": True,
            "broker_order_id": "test_broker_order",
            "status": "SUBMITTED"
        }
        
        gateway.brokers["ALPACA"] = mock_adapter
        
        response = await gateway.submit_order(sample_order_request)
        
        assert response.status == "SUBMITTED"
        assert response.broker == "ALPACA"
        assert response.symbol == "AAPL"
        assert response.quantity == Decimal("100")
    
    @pytest.mark.asyncio
    async def test_broker_selection(self, sample_order_request):
        """Test broker selection logic."""
        gateway = ExecutionGateway()
        
        # Test preferred broker selection
        sample_order_request.preferred_broker = "ALPACA"
        gateway.brokers["ALPACA"] = Mock()
        gateway.brokers["INTERACTIVE_BROKERS"] = Mock()
        
        broker = gateway._select_broker(sample_order_request)
        assert broker == "ALPACA"
        
        # Test asset class-based selection
        sample_order_request.preferred_broker = None
        sample_order_request.asset_class = AssetClass.EQUITY
        
        broker = gateway._select_broker(sample_order_request)
        assert broker in ["INTERACTIVE_BROKERS", "ALPACA"]
    
    @pytest.mark.asyncio
    async def test_order_cancellation(self):
        """Test order cancellation."""
        gateway = ExecutionGateway()
        
        # Add active order
        client_order_id = "test_order"
        gateway.active_orders[client_order_id] = {
            "broker": "ALPACA",
            "broker_order_id": "broker_123"
        }
        
        # Mock broker adapter
        mock_adapter = AsyncMock()
        mock_adapter.cancel_order.return_value = {"success": True, "status": "CANCELLED"}
        gateway.brokers["ALPACA"] = mock_adapter
        
        result = await gateway.cancel_order(client_order_id)
        
        assert result["success"] is True
        assert result["status"] == "CANCELLED"
    
    @pytest.mark.asyncio
    async def test_order_status_check(self):
        """Test order status checking."""
        gateway = ExecutionGateway()
        
        # Add active order
        client_order_id = "test_order"
        gateway.active_orders[client_order_id] = {
            "broker": "ALPACA",
            "broker_order_id": "broker_123"
        }
        
        # Mock broker adapter
        mock_adapter = AsyncMock()
        mock_adapter.get_order_status.return_value = {
            "success": True,
            "status": "FILLED",
            "filled_quantity": 100,
            "avg_price": 150.50
        }
        gateway.brokers["ALPACA"] = mock_adapter
        
        result = await gateway.get_order_status(client_order_id)
        
        assert result["success"] is True
        assert result["status"] == "FILLED"
        assert result["filled_quantity"] == 100
    
    @pytest.mark.asyncio
    async def test_execution_callback(self):
        """Test execution report callback."""
        gateway = ExecutionGateway()
        
        # Add callback
        callback_called = False
        callback_data = None
        
        async def test_callback(data):
            nonlocal callback_called, callback_data
            callback_called = True
            callback_data = data
        
        gateway.add_execution_callback(test_callback)
        
        # Process execution report
        execution_data = {
            "client_order_id": "test_order",
            "order_status": "FILLED",
            "cumulative_quantity": 100,
            "average_price": 150.50
        }
        
        # Add active order
        gateway.active_orders["test_order"] = {
            "status": "SUBMITTED",
            "filled_quantity": 0
        }
        
        await gateway._process_execution_report(execution_data)
        
        assert callback_called is True
        assert callback_data == execution_data
        assert gateway.active_orders["test_order"]["status"] == "FILLED"
    
    @pytest.mark.asyncio
    async def test_broker_connections_status(self):
        """Test broker connections status."""
        gateway = ExecutionGateway()
        
        # Mock brokers
        gateway.brokers["ALPACA"] = Mock()
        gateway.brokers["INTERACTIVE_BROKERS"] = Mock()
        
        connections = await gateway.get_broker_connections()
        
        assert len(connections) == 2
        assert all(conn.broker_name in ["ALPACA", "INTERACTIVE_BROKERS"] for conn in connections)
        assert all(conn.status == "CONNECTED" for conn in connections)
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test gateway cleanup."""
        gateway = ExecutionGateway()
        
        # Mock FIX initiator
        gateway.fix_initiator = Mock()
        gateway.fix_initiator.stop = Mock()
        
        # Mock broker adapters
        mock_adapter = Mock()
        mock_adapter.client = AsyncMock()
        gateway.brokers["ALPACA"] = mock_adapter
        
        await gateway.cleanup()
        
        # Verify cleanup was called
        gateway.fix_initiator.stop.assert_called_once()
        mock_adapter.client.aclose.assert_called_once()


@pytest.mark.integration
class TestExecutionGatewayIntegration:
    """Integration tests for execution gateway."""
    
    @pytest.mark.asyncio
    async def test_full_order_workflow(self, sample_order_request):
        """Test complete order workflow."""
        gateway = ExecutionGateway()
        
        # Mock successful broker adapter
        mock_adapter = AsyncMock()
        mock_adapter.submit_order.return_value = {
            "success": True,
            "broker_order_id": "test_order_123",
            "status": "SUBMITTED"
        }
        mock_adapter.get_order_status.return_value = {
            "success": True,
            "status": "FILLED",
            "filled_quantity": 100,
            "avg_price": 150.50
        }
        mock_adapter.cancel_order.return_value = {
            "success": True,
            "status": "CANCELLED"
        }
        
        gateway.brokers["ALPACA"] = mock_adapter
        
        # Submit order
        response = await gateway.submit_order(sample_order_request)
        assert response.status == "SUBMITTED"
        
        client_order_id = response.client_order_id
        
        # Check status
        status_result = await gateway.get_order_status(client_order_id)
        assert status_result["success"] is True
        
        # Cancel order
        cancel_result = await gateway.cancel_order(client_order_id)
        assert cancel_result["success"] is True
    
    @pytest.mark.asyncio
    async def test_multiple_broker_handling(self, sample_order_request):
        """Test handling multiple brokers."""
        gateway = ExecutionGateway()
        
        # Mock multiple brokers
        alpaca_adapter = AsyncMock()
        alpaca_adapter.submit_order.return_value = {
            "success": True,
            "broker_order_id": "alpaca_order",
            "status": "SUBMITTED"
        }
        
        ib_adapter = AsyncMock()
        ib_adapter.submit_order.return_value = {
            "success": True,
            "broker_order_id": "ib_order",
            "status": "SUBMITTED"
        }
        
        gateway.brokers["ALPACA"] = alpaca_adapter
        gateway.brokers["INTERACTIVE_BROKERS"] = ib_adapter
        
        # Test preferred broker
        sample_order_request.preferred_broker = "INTERACTIVE_BROKERS"
        response = await gateway.submit_order(sample_order_request)
        
        assert response.broker == "INTERACTIVE_BROKERS"
        ib_adapter.submit_order.assert_called_once()
        alpaca_adapter.submit_order.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, sample_order_request):
        """Test error handling in execution gateway."""
        gateway = ExecutionGateway()
        
        # Mock failing broker adapter
        mock_adapter = AsyncMock()
        mock_adapter.submit_order.return_value = {
            "success": False,
            "error": "Broker connection failed"
        }
        
        gateway.brokers["ALPACA"] = mock_adapter
        
        # Test order submission failure
        with pytest.raises(ValueError, match="Order submission failed"):
            await gateway.submit_order(sample_order_request)
        
        # Test missing broker
        gateway.brokers.clear()
        
        with pytest.raises(ValueError, match="No brokers available"):
            await gateway.submit_order(sample_order_request)
