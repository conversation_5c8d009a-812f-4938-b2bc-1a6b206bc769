version: '3.8'

services:
  # Database Services
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: athena-postgres
    environment:
      POSTGRES_DB: athena_trader
      POSTGRES_USER: athena_user
      POSTGRES_PASSWORD: athena_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - athena-network

  redis:
    image: redis:7-alpine
    container_name: athena-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - athena-network

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: athena-api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/api-gateway:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Data Nexus Service
  data-nexus:
    build:
      context: .
      dockerfile: services/data-nexus/Dockerfile
    container_name: athena-data-nexus
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/data-nexus:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Strategy Genesis Service
  strategy-genesis:
    build:
      context: ./services/strategy-genesis
      dockerfile: Dockerfile
    container_name: athena-strategy-genesis
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - MODEL_STORAGE_PATH=/app/models
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
    volumes:
      - ./services/strategy-genesis:/app
      - strategy_models:/app/models
    networks:
      - athena-network

  # Backtesting Engine Service
  backtesting-engine:
    build:
      context: ./services/backtesting-engine
      dockerfile: Dockerfile
    container_name: athena-backtesting-engine
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - RESULTS_STORAGE_PATH=/app/results
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
    volumes:
      - ./services/backtesting-engine:/app
      - backtest_results:/app/results
    networks:
      - athena-network

  # Portfolio Construction Service
  portfolio-construction:
    build:
      context: ./services/portfolio-construction
      dockerfile: Dockerfile
    container_name: athena-portfolio-construction
    ports:
      - "8004:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - RESULTS_STORAGE_PATH=/app/results
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
    volumes:
      - ./services/portfolio-construction:/app
      - portfolio_results:/app/results
    networks:
      - athena-network

  # XAI Module Service
  xai-module:
    build:
      context: ./services/xai-module
      dockerfile: Dockerfile
    container_name: athena-xai-module
    ports:
      - "8005:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - PORTFOLIO_CONSTRUCTION_URL=http://portfolio-construction:8000
      - EXPLANATIONS_STORAGE_PATH=/app/explanations
      - VISUALIZATIONS_STORAGE_PATH=/app/visualizations
      - AUDIT_LOGS_STORAGE_PATH=/app/audit_logs
      - MODEL_ARTIFACTS_STORAGE_PATH=/app/model_artifacts
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
      - portfolio-construction
    volumes:
      - ./services/xai-module:/app
      - xai_explanations:/app/explanations
      - xai_visualizations:/app/visualizations
      - xai_audit_logs:/app/audit_logs
      - xai_model_artifacts:/app/model_artifacts
    networks:
      - athena-network



  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: athena-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - athena-network

volumes:
  postgres_data:
  redis_data:
  strategy_models:
  backtest_results:
  portfolio_results:
  xai_explanations:
  xai_visualizations:
  xai_audit_logs:
  xai_model_artifacts:

networks:
  athena-network:
    driver: bridge
