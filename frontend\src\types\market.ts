export interface Instrument {
  id: string;
  symbol: string;
  name: string;
  instrument_type: InstrumentType;
  base_currency?: string;
  quote_currency?: string;
  exchange?: string;
  is_active: boolean;
  created_at: string;
}

export enum InstrumentType {
  FOREX = 'FOREX',
  STOCK = 'STOCK',
  CRYPTO = 'CRYPTO',
  COMMODITY = 'COMMODITY',
  INDEX = 'INDEX',
  BOND = 'BOND',
  OPTION = 'OPTION',
  FUTURE = 'FUTURE',
}

export enum Timeframe {
  TICK = 'tick',
  ONE_MINUTE = '1m',
  FIVE_MINUTES = '5m',
  FIFTEEN_MINUTES = '15m',
  THIRTY_MINUTES = '30m',
  ONE_HOUR = '1h',
  FOUR_HOURS = '4h',
  ONE_DAY = '1d',
  ONE_WEEK = '1w',
  ONE_MONTH = '1M',
}

export interface OHLCV {
  time: string;
  instrument_id: string;
  timeframe: Timeframe;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface OHLCVWithInstrument extends OHLCV {
  instrument: Instrument;
}

export interface MarketDataQuery {
  instrument_ids?: string[];
  symbols?: string[];
  timeframe: Timeframe;
  start_time?: string;
  end_time?: string;
  limit?: number;
}

export interface TechnicalIndicator {
  name: string;
  value: number;
  parameters?: Record<string, any>;
  timestamp: string;
}

export interface MarketDataSummary {
  instrument: Instrument;
  latest_data?: OHLCV;
  data_count: number;
  first_timestamp?: string;
  last_timestamp?: string;
  timeframes_available: Timeframe[];
}
