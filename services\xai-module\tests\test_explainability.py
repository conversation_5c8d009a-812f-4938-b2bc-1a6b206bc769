"""
Tests for explainability functionality.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime
from unittest.mock import Mock, patch

from app.engine.explainability import (
    StrategyExplainer, SHAPExplainer, LIMEExplainer, FeatureImportanceAnalyzer
)
from app.schemas.explanation import ExplanationType, AIParadigm, ExplanationScope


@pytest.fixture
def sample_data():
    """Create sample data for testing."""
    np.random.seed(42)
    n_samples = 100
    n_features = 10
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)
    feature_names = [f"feature_{i}" for i in range(n_features)]
    
    return X, y, feature_names


@pytest.fixture
def mock_model():
    """Create mock model for testing."""
    model = Mock()
    model.predict = Mock(return_value=np.random.randn(10))
    return model


class TestFeatureImportanceAnalyzer:
    """Test feature importance analysis."""
    
    def test_permutation_importance_analysis(self, sample_data, mock_model):
        """Test permutation importance analysis."""
        X, y, feature_names = sample_data
        analyzer = FeatureImportanceAnalyzer()
        
        result = analyzer.permutation_importance_analysis(
            mock_model, X, y, feature_names, n_repeats=3
        )
        
        assert result["method"] == "permutation_importance"
        assert "results" in result
        assert len(result["results"]) == len(feature_names)
        
        # Check result structure
        for feature_result in result["results"]:
            assert "feature_name" in feature_result
            assert "importance_mean" in feature_result
            assert "importance_std" in feature_result
            assert "rank" in feature_result
            assert "percentile" in feature_result
    
    def test_correlation_analysis(self, sample_data):
        """Test correlation analysis."""
        X, y, feature_names = sample_data
        analyzer = FeatureImportanceAnalyzer()
        
        result = analyzer.correlation_analysis(X, y, feature_names)
        
        assert result["method"] == "correlation_analysis"
        assert "results" in result
        assert len(result["results"]) == len(feature_names)
        
        # Check result structure
        for feature_result in result["results"]:
            assert "feature_name" in feature_result
            assert "correlation" in feature_result
            assert "abs_correlation" in feature_result
            assert "rank" in feature_result
    
    def test_ablation_study(self, sample_data, mock_model):
        """Test ablation study."""
        X, y, feature_names = sample_data
        analyzer = FeatureImportanceAnalyzer()
        
        result = analyzer.ablation_study(
            mock_model, X, y, feature_names, n_iterations=3
        )
        
        assert result["method"] == "ablation_study"
        assert "results" in result
        assert len(result["results"]) == len(feature_names)
        
        # Check result structure
        for feature_result in result["results"]:
            assert "feature_name" in feature_result
            assert "importance" in feature_result
            assert "baseline_score" in feature_result
            assert "ablated_score" in feature_result


class TestSHAPExplainer:
    """Test SHAP explainer functionality."""
    
    @pytest.mark.skipif(
        not pytest.importorskip("shap", minversion=None),
        reason="SHAP not available"
    )
    def test_shap_explainer_creation(self, sample_data, mock_model):
        """Test SHAP explainer creation."""
        X, y, feature_names = sample_data
        
        try:
            explainer = SHAPExplainer()
            
            # Test explainer creation
            shap_explainer = explainer.create_explainer(
                mock_model, X[:10], model_type="auto", ai_paradigm="DL"
            )
            
            assert shap_explainer is not None
            
        except ImportError:
            pytest.skip("SHAP not available")
    
    def test_shap_explainer_without_library(self):
        """Test SHAP explainer when library not available."""
        with patch('app.engine.explainability.SHAP_AVAILABLE', False):
            with pytest.raises(ImportError):
                SHAPExplainer()


class TestLIMEExplainer:
    """Test LIME explainer functionality."""
    
    @pytest.mark.skipif(
        not pytest.importorskip("lime", minversion=None),
        reason="LIME not available"
    )
    def test_lime_explainer_creation(self, sample_data):
        """Test LIME explainer creation."""
        X, y, feature_names = sample_data
        
        try:
            explainer = LIMEExplainer()
            
            # Test explainer creation
            lime_explainer = explainer.create_explainer(
                X, feature_names, mode="regression"
            )
            
            assert lime_explainer is not None
            
        except ImportError:
            pytest.skip("LIME not available")
    
    def test_lime_explainer_without_library(self):
        """Test LIME explainer when library not available."""
        with patch('app.engine.explainability.LIME_AVAILABLE', False):
            with pytest.raises(ImportError):
                LIMEExplainer()


class TestStrategyExplainer:
    """Test main strategy explainer."""
    
    def test_strategy_explainer_initialization(self):
        """Test strategy explainer initialization."""
        explainer = StrategyExplainer()
        
        # Check that components are initialized
        assert explainer.feature_analyzer is not None
        assert hasattr(explainer, 'explainer_cache')
        assert hasattr(explainer, 'background_cache')
    
    @pytest.mark.asyncio
    async def test_explain_strategy_feature_importance(self, sample_data, mock_model):
        """Test strategy explanation with feature importance."""
        X, y, feature_names = sample_data
        explainer = StrategyExplainer()
        
        result = await explainer.explain_strategy(
            strategy_id="test_strategy",
            explanation_type=ExplanationType.FEATURE_IMPORTANCE,
            explanation_scope=ExplanationScope.GLOBAL,
            model=mock_model,
            data=X,
            target=y,
            feature_names=feature_names,
            ai_paradigm=AIParadigm.DL,
            config={}
        )
        
        assert result["strategy_id"] == "test_strategy"
        assert result["explanation_type"] == "FEATURE_IMPORTANCE"
        assert result["ai_paradigm"] == "DL"
        assert "feature_importance" in result
    
    @pytest.mark.asyncio
    async def test_explain_strategy_permutation_importance(self, sample_data, mock_model):
        """Test strategy explanation with permutation importance."""
        X, y, feature_names = sample_data
        explainer = StrategyExplainer()
        
        result = await explainer.explain_strategy(
            strategy_id="test_strategy",
            explanation_type=ExplanationType.PERMUTATION_IMPORTANCE,
            explanation_scope=ExplanationScope.GLOBAL,
            model=mock_model,
            data=X,
            target=y,
            feature_names=feature_names,
            ai_paradigm=AIParadigm.GP,
            config={"n_repeats": 3}
        )
        
        assert result["strategy_id"] == "test_strategy"
        assert result["explanation_type"] == "PERMUTATION_IMPORTANCE"
        assert result["ai_paradigm"] == "GP"
        assert "permutation_importance" in result
    
    @pytest.mark.asyncio
    async def test_explain_strategy_insufficient_data(self, mock_model):
        """Test strategy explanation with insufficient data."""
        explainer = StrategyExplainer()
        
        # Very small dataset
        X = np.array([[1, 2]])
        y = np.array([1])
        feature_names = ["feature_1", "feature_2"]
        
        result = await explainer.explain_strategy(
            strategy_id="test_strategy",
            explanation_type=ExplanationType.FEATURE_IMPORTANCE,
            explanation_scope=ExplanationScope.GLOBAL,
            model=mock_model,
            data=X,
            target=y,
            feature_names=feature_names,
            ai_paradigm=AIParadigm.DL,
            config={}
        )
        
        # Should still return a result, possibly with warnings
        assert result["strategy_id"] == "test_strategy"
        assert result["explanation_type"] == "FEATURE_IMPORTANCE"
    
    @pytest.mark.asyncio
    async def test_explain_strategy_error_handling(self, sample_data):
        """Test strategy explanation error handling."""
        X, y, feature_names = sample_data
        explainer = StrategyExplainer()
        
        # Use None as model to trigger error
        result = await explainer.explain_strategy(
            strategy_id="test_strategy",
            explanation_type=ExplanationType.FEATURE_IMPORTANCE,
            explanation_scope=ExplanationScope.GLOBAL,
            model=None,
            data=X,
            target=y,
            feature_names=feature_names,
            ai_paradigm=AIParadigm.DL,
            config={}
        )
        
        assert "error" in result
        assert result["strategy_id"] == "test_strategy"
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """Test explainer cleanup."""
        explainer = StrategyExplainer()
        
        # Add some cache entries
        explainer.explainer_cache["test"] = "value"
        explainer.background_cache["test"] = "value"
        
        await explainer.cleanup()
        
        # Cache should be cleared
        assert len(explainer.explainer_cache) == 0
        assert len(explainer.background_cache) == 0


@pytest.mark.integration
class TestExplainabilityIntegration:
    """Integration tests for explainability engine."""
    
    @pytest.mark.asyncio
    async def test_multiple_explanation_types(self, sample_data, mock_model):
        """Test multiple explanation types on same data."""
        X, y, feature_names = sample_data
        explainer = StrategyExplainer()
        
        explanation_types = [
            ExplanationType.FEATURE_IMPORTANCE,
            ExplanationType.PERMUTATION_IMPORTANCE
        ]
        
        results = {}
        
        for exp_type in explanation_types:
            result = await explainer.explain_strategy(
                strategy_id="test_strategy",
                explanation_type=exp_type,
                explanation_scope=ExplanationScope.GLOBAL,
                model=mock_model,
                data=X,
                target=y,
                feature_names=feature_names,
                ai_paradigm=AIParadigm.DL,
                config={}
            )
            results[exp_type.value] = result
        
        # All explanations should succeed
        for exp_type, result in results.items():
            assert "error" not in result or result.get("error") is None
            assert result["explanation_type"] == exp_type
    
    @pytest.mark.asyncio
    async def test_different_ai_paradigms(self, sample_data, mock_model):
        """Test explanations for different AI paradigms."""
        X, y, feature_names = sample_data
        explainer = StrategyExplainer()
        
        paradigms = [AIParadigm.GP, AIParadigm.RL, AIParadigm.DL]
        
        for paradigm in paradigms:
            result = await explainer.explain_strategy(
                strategy_id=f"test_strategy_{paradigm.value}",
                explanation_type=ExplanationType.FEATURE_IMPORTANCE,
                explanation_scope=ExplanationScope.GLOBAL,
                model=mock_model,
                data=X,
                target=y,
                feature_names=feature_names,
                ai_paradigm=paradigm,
                config={}
            )
            
            assert result["ai_paradigm"] == paradigm.value
            assert result["strategy_id"] == f"test_strategy_{paradigm.value}"
