# Database Configuration
DATABASE_URL=postgresql://athena_user:athena_password@localhost:5432/athena_trader
POSTGRES_DB=athena_trader
POSTGRES_USER=athena_user
POSTGRES_PASSWORD=athena_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key
OANDA_API_KEY=your-oanda-api-key
OANDA_ACCOUNT_ID=your-oanda-account-id

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Service URLs (for development)
API_GATEWAY_URL=http://localhost:8000
DATA_NEXUS_URL=http://localhost:8001
STRATEGY_GENESIS_URL=http://localhost:8002
BACKTESTING_ENGINE_URL=http://localhost:8003
PORTFOLIO_OPTIMIZATION_URL=http://localhost:8004
XAI_URL=http://localhost:8005
EXECUTION_ENGINE_URL=http://localhost:8006
LEARNING_HUB_URL=http://localhost:8007

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
