"""
Real-time XAI integration for live trading explanations.
"""

import logging
import async<PERSON>
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
import json

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)


class XAIIntegration:
    """Real-time XAI integration for transparent trading decisions."""
    
    def __init__(self):
        """Initialize XAI integration."""
        self.xai_client = httpx.AsyncClient(timeout=settings.XAI_EXPLANATION_TIMEOUT)
        self.explanation_cache = {}
        self.live_explanations = {}
        self.explanation_callbacks = []
        
    async def explain_trading_decision(
        self,
        strategy_id: uuid.UUID,
        decision_type: str,
        decision_data: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate real-time explanation for trading decision."""
        try:
            explanation_request = {
                "strategy_id": str(strategy_id),
                "explanation_type": "REAL_TIME_DECISION",
                "explanation_scope": "LOCAL",
                "decision_context": {
                    "decision_type": decision_type,
                    "decision_data": decision_data,
                    "context": context or {},
                    "timestamp": datetime.utcnow().isoformat()
                },
                "config": {
                    "explanation_methods": ["SHAP", "FEATURE_IMPORTANCE"],
                    "max_features": 10,
                    "confidence_threshold": 0.7
                }
            }
            
            # Call XAI Module
            response = await self.xai_client.post(
                f"{settings.XAI_MODULE_URL}/explanations/",
                json=explanation_request
            )
            
            if response.status_code == 200:
                explanation = response.json()
                
                # Cache explanation
                explanation_id = explanation.get("id", str(uuid.uuid4()))
                self.explanation_cache[explanation_id] = explanation
                
                # Store live explanation
                self.live_explanations[str(strategy_id)] = {
                    "explanation_id": explanation_id,
                    "explanation": explanation,
                    "decision_type": decision_type,
                    "timestamp": datetime.utcnow()
                }
                
                # Notify callbacks
                await self._notify_explanation_callbacks(strategy_id, explanation)
                
                logger.info(f"Generated explanation for {decision_type} decision")
                
                return {
                    "success": True,
                    "explanation_id": explanation_id,
                    "explanation": explanation,
                    "decision_type": decision_type
                }
            else:
                logger.error(f"XAI explanation failed: {response.status_code}")
                return {
                    "success": False,
                    "error": f"XAI service error: {response.status_code}",
                    "decision_type": decision_type
                }
                
        except Exception as e:
            logger.error(f"XAI explanation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "decision_type": decision_type
            }
    
    async def explain_order_decision(
        self,
        strategy_id: uuid.UUID,
        order_data: Dict[str, Any],
        market_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Explain order placement decision."""
        decision_data = {
            "symbol": order_data.get("symbol"),
            "side": order_data.get("side"),
            "quantity": str(order_data.get("quantity", 0)),
            "order_type": order_data.get("order_type"),
            "price": str(order_data.get("price", 0)) if order_data.get("price") else None
        }
        
        context = {
            "market_context": market_context or {},
            "order_context": order_data
        }
        
        return await self.explain_trading_decision(
            strategy_id, "ORDER_PLACEMENT", decision_data, context
        )
    
    async def explain_position_sizing(
        self,
        strategy_id: uuid.UUID,
        sizing_data: Dict[str, Any],
        risk_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Explain position sizing decision."""
        decision_data = {
            "symbol": sizing_data.get("symbol"),
            "target_size": str(sizing_data.get("target_size", 0)),
            "current_size": str(sizing_data.get("current_size", 0)),
            "size_change": str(sizing_data.get("size_change", 0)),
            "sizing_method": sizing_data.get("sizing_method")
        }
        
        context = {
            "risk_context": risk_context or {},
            "portfolio_context": sizing_data.get("portfolio_context", {})
        }
        
        return await self.explain_trading_decision(
            strategy_id, "POSITION_SIZING", decision_data, context
        )
    
    async def explain_risk_action(
        self,
        strategy_id: uuid.UUID,
        risk_action: str,
        risk_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Explain risk management action."""
        decision_data = {
            "action": risk_action,
            "trigger": risk_data.get("trigger"),
            "risk_metric": risk_data.get("risk_metric"),
            "threshold": str(risk_data.get("threshold", 0)),
            "current_value": str(risk_data.get("current_value", 0))
        }
        
        context = {
            "risk_context": risk_data,
            "action_context": {
                "urgency": risk_data.get("urgency", "NORMAL"),
                "severity": risk_data.get("severity", "MEDIUM")
            }
        }
        
        return await self.explain_trading_decision(
            strategy_id, "RISK_ACTION", decision_data, context
        )
    
    async def get_live_explanation(self, strategy_id: uuid.UUID) -> Optional[Dict[str, Any]]:
        """Get current live explanation for strategy."""
        return self.live_explanations.get(str(strategy_id))
    
    async def get_explanation_history(
        self,
        strategy_id: uuid.UUID,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get explanation history for strategy."""
        try:
            response = await self.xai_client.get(
                f"{settings.XAI_MODULE_URL}/explanations/strategy/{strategy_id}",
                params={"limit": limit}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get explanation history: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Explanation history retrieval failed: {e}")
            return []
    
    async def run_live_bias_detection(
        self,
        strategy_id: uuid.UUID,
        prediction_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run live bias detection on strategy predictions."""
        try:
            if not settings.ENABLE_LIVE_BIAS_DETECTION:
                return {"bias_detection_disabled": True}
            
            bias_request = {
                "strategy_id": str(strategy_id),
                "prediction_data": prediction_data,
                "detection_config": {
                    "check_statistical_parity": True,
                    "check_feature_importance": True,
                    "check_explanation_consistency": True
                }
            }
            
            response = await self.xai_client.post(
                f"{settings.XAI_MODULE_URL}/audit/bias-detection/{strategy_id}",
                json=bias_request
            )
            
            if response.status_code == 200:
                bias_result = response.json()
                
                # Log bias detection results
                if bias_result.get("bias_detected"):
                    logger.warning(
                        f"Bias detected in strategy {strategy_id}: "
                        f"{bias_result.get('bias_types', [])}"
                    )
                
                return bias_result
            else:
                logger.error(f"Bias detection failed: {response.status_code}")
                return {"error": "Bias detection service unavailable"}
                
        except Exception as e:
            logger.error(f"Live bias detection failed: {e}")
            return {"error": str(e)}
    
    async def generate_compliance_report(
        self,
        strategy_id: uuid.UUID,
        framework: str = "MIFID_II"
    ) -> Dict[str, Any]:
        """Generate real-time compliance report."""
        try:
            response = await self.xai_client.get(
                f"{settings.XAI_MODULE_URL}/audit/compliance/{strategy_id}",
                params={"framework": framework}
            )
            
            if response.status_code == 200:
                compliance_report = response.json()
                
                # Log compliance issues
                if compliance_report.get("compliance_status") != "COMPLIANT":
                    logger.warning(
                        f"Compliance issues detected for strategy {strategy_id}: "
                        f"{compliance_report.get('violations', [])}"
                    )
                
                return compliance_report
            else:
                logger.error(f"Compliance report generation failed: {response.status_code}")
                return {"error": "Compliance service unavailable"}
                
        except Exception as e:
            logger.error(f"Compliance report generation failed: {e}")
            return {"error": str(e)}
    
    async def create_explanation_dashboard(
        self,
        strategy_id: uuid.UUID,
        dashboard_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create real-time explanation dashboard."""
        try:
            # Get recent explanations
            explanations = await self.get_explanation_history(strategy_id, limit=5)
            
            if not explanations:
                return {"error": "No explanations available"}
            
            dashboard_request = {
                "strategy_id": str(strategy_id),
                "dashboard_config": dashboard_config or {
                    "chart_types": ["feature_importance", "shap_waterfall"],
                    "time_range": "1H",
                    "refresh_interval": 30
                },
                "explanation_data": explanations[0] if explanations else {}
            }
            
            response = await self.xai_client.post(
                f"{settings.XAI_MODULE_URL}/dashboards/",
                json=dashboard_request
            )
            
            if response.status_code == 200:
                dashboard = response.json()
                return dashboard
            else:
                logger.error(f"Dashboard creation failed: {response.status_code}")
                return {"error": "Dashboard service unavailable"}
                
        except Exception as e:
            logger.error(f"Dashboard creation failed: {e}")
            return {"error": str(e)}
    
    async def _notify_explanation_callbacks(
        self,
        strategy_id: uuid.UUID,
        explanation: Dict[str, Any]
    ):
        """Notify explanation callbacks."""
        for callback in self.explanation_callbacks:
            try:
                await callback(strategy_id, explanation)
            except Exception as e:
                logger.error(f"Explanation callback failed: {e}")
    
    def add_explanation_callback(self, callback):
        """Add explanation callback."""
        self.explanation_callbacks.append(callback)
    
    def remove_explanation_callback(self, callback):
        """Remove explanation callback."""
        if callback in self.explanation_callbacks:
            self.explanation_callbacks.remove(callback)
    
    async def get_xai_status(self) -> Dict[str, Any]:
        """Get XAI integration status."""
        try:
            response = await self.xai_client.get(f"{settings.XAI_MODULE_URL}/health/")
            
            if response.status_code == 200:
                xai_health = response.json()
                return {
                    "xai_service_available": True,
                    "xai_service_status": xai_health.get("status"),
                    "cached_explanations": len(self.explanation_cache),
                    "live_explanations": len(self.live_explanations),
                    "explanation_callbacks": len(self.explanation_callbacks),
                    "bias_detection_enabled": settings.ENABLE_LIVE_BIAS_DETECTION
                }
            else:
                return {
                    "xai_service_available": False,
                    "error": f"XAI service unhealthy: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "xai_service_available": False,
                "error": str(e)
            }
    
    async def cleanup(self):
        """Cleanup XAI integration."""
        await self.xai_client.aclose()
        self.explanation_cache.clear()
        self.live_explanations.clear()
        self.explanation_callbacks.clear()
        
        logger.info("XAI integration cleaned up")
