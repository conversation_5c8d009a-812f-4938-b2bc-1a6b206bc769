"""
Backtest models for historical simulation and performance analysis.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, String, Boolean, DateTime, Numeric, Text, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Backtest(Base):
    """Backtest configuration and execution model."""
    
    __tablename__ = "backtests"
    __table_args__ = (
        {"schema": "backtests"},
        Index("idx_backtests_user_id", "user_id"),
        Index("idx_backtests_strategy_id", "strategy_id"),
        Index("idx_backtests_status", "status"),
        Index("idx_backtests_created_at", "created_at"),
        Index("idx_backtests_performance", "total_return"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # Reference to auth.users
    strategy_id = Column(UUID(as_uuid=True), nullable=False, index=True)  # Reference to strategies.strategies
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Backtest Configuration
    config = Column(JSONB, nullable=False, default=dict)
    
    # Time Period
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    timeframe = Column(String(10), nullable=False, default="1h")
    
    # Market Data Configuration
    instruments = Column(JSONB, nullable=False)  # List of instrument IDs
    benchmark_symbol = Column(String(20), nullable=True, default="SPY")
    
    # Simulation Parameters
    initial_capital = Column(Numeric(15, 2), nullable=False, default=100000.00)
    commission_bps = Column(Numeric(8, 4), nullable=False, default=1.0)
    spread_bps = Column(Numeric(8, 4), nullable=False, default=2.0)
    slippage_bps = Column(Numeric(8, 4), nullable=False, default=0.5)
    market_impact_factor = Column(Numeric(8, 6), nullable=False, default=0.1)
    
    # Risk Management
    max_position_size = Column(Numeric(5, 4), nullable=False, default=0.2)
    stop_loss_pct = Column(Numeric(5, 4), nullable=True)
    take_profit_pct = Column(Numeric(5, 4), nullable=True)
    max_leverage = Column(Numeric(8, 2), nullable=False, default=1.0)
    
    # Execution Status
    status = Column(String(50), default="PENDING", nullable=False)  # PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
    progress_pct = Column(Numeric(5, 2), default=0.0, nullable=False)
    
    # Performance Results
    total_return = Column(Numeric(10, 6), nullable=True)
    annualized_return = Column(Numeric(10, 6), nullable=True)
    volatility = Column(Numeric(10, 6), nullable=True)
    sharpe_ratio = Column(Numeric(10, 6), nullable=True)
    sortino_ratio = Column(Numeric(10, 6), nullable=True)
    calmar_ratio = Column(Numeric(10, 6), nullable=True)
    max_drawdown = Column(Numeric(10, 6), nullable=True)
    max_drawdown_duration_days = Column(Integer, nullable=True)
    
    # Trade Statistics
    total_trades = Column(Integer, nullable=True)
    winning_trades = Column(Integer, nullable=True)
    losing_trades = Column(Integer, nullable=True)
    win_rate = Column(Numeric(5, 4), nullable=True)
    profit_factor = Column(Numeric(10, 6), nullable=True)
    avg_trade_return = Column(Numeric(10, 6), nullable=True)
    avg_trade_duration_hours = Column(Numeric(10, 2), nullable=True)
    
    # Risk Metrics
    var_95 = Column(Numeric(10, 6), nullable=True)  # Value at Risk 95%
    cvar_95 = Column(Numeric(10, 6), nullable=True)  # Conditional VaR 95%
    beta = Column(Numeric(10, 6), nullable=True)
    alpha = Column(Numeric(10, 6), nullable=True)
    information_ratio = Column(Numeric(10, 6), nullable=True)
    treynor_ratio = Column(Numeric(10, 6), nullable=True)
    
    # Detailed Results
    performance_metrics = Column(JSONB, nullable=True, default=dict)
    equity_curve = Column(JSONB, nullable=True)  # Time series of portfolio values
    drawdown_curve = Column(JSONB, nullable=True)  # Time series of drawdowns
    
    # Execution Information
    bars_processed = Column(Integer, nullable=True)
    execution_time_seconds = Column(Integer, nullable=True)
    memory_usage_mb = Column(Integer, nullable=True)
    
    # Error Handling
    error_message = Column(Text, nullable=True)
    error_traceback = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    trades = relationship("BacktestTrade", back_populates="backtest", cascade="all, delete-orphan")
    analytics = relationship("BacktestAnalytics", back_populates="backtest", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Backtest(id={self.id}, name={self.name}, status={self.status})>"


class BacktestTrade(Base):
    """Individual trade execution during backtest."""
    
    __tablename__ = "backtest_trades"
    __table_args__ = (
        {"schema": "backtests"},
        Index("idx_backtest_trades_backtest_id", "backtest_id"),
        Index("idx_backtest_trades_timestamp", "timestamp"),
        Index("idx_backtest_trades_instrument", "instrument_id"),
        Index("idx_backtest_trades_side", "side"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    backtest_id = Column(UUID(as_uuid=True), ForeignKey("backtests.backtests.id"), nullable=False)
    
    # Trade Identification
    trade_id = Column(String(50), nullable=False)  # Unique trade identifier within backtest
    instrument_id = Column(UUID(as_uuid=True), nullable=False)  # Reference to market_data.instruments
    symbol = Column(String(20), nullable=False)
    
    # Trade Details
    side = Column(String(10), nullable=False)  # BUY, SELL
    quantity = Column(Numeric(15, 8), nullable=False)
    price = Column(Numeric(15, 8), nullable=False)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    
    # Execution Details
    signal_strength = Column(Numeric(5, 4), nullable=True)  # Original signal strength
    intended_quantity = Column(Numeric(15, 8), nullable=True)  # Quantity before position sizing
    fill_type = Column(String(20), nullable=False, default="FULL")  # FULL, PARTIAL
    
    # Costs and Slippage
    commission = Column(Numeric(15, 8), nullable=False, default=0.0)
    spread_cost = Column(Numeric(15, 8), nullable=False, default=0.0)
    slippage_cost = Column(Numeric(15, 8), nullable=False, default=0.0)
    market_impact_cost = Column(Numeric(15, 8), nullable=False, default=0.0)
    total_cost = Column(Numeric(15, 8), nullable=False, default=0.0)
    
    # Position Information
    position_before = Column(Numeric(15, 8), nullable=False, default=0.0)
    position_after = Column(Numeric(15, 8), nullable=False)
    portfolio_value_before = Column(Numeric(15, 2), nullable=False)
    portfolio_value_after = Column(Numeric(15, 2), nullable=False)
    
    # Trade Performance (for closed trades)
    entry_price = Column(Numeric(15, 8), nullable=True)
    exit_price = Column(Numeric(15, 8), nullable=True)
    entry_timestamp = Column(DateTime(timezone=True), nullable=True)
    exit_timestamp = Column(DateTime(timezone=True), nullable=True)
    pnl = Column(Numeric(15, 8), nullable=True)
    pnl_pct = Column(Numeric(10, 6), nullable=True)
    duration_hours = Column(Numeric(10, 2), nullable=True)
    
    # Risk Management
    stop_loss_triggered = Column(Boolean, default=False, nullable=False)
    take_profit_triggered = Column(Boolean, default=False, nullable=False)
    risk_limit_triggered = Column(Boolean, default=False, nullable=False)
    
    # Market Context
    market_price = Column(Numeric(15, 8), nullable=True)  # Market price at execution
    bid_price = Column(Numeric(15, 8), nullable=True)
    ask_price = Column(Numeric(15, 8), nullable=True)
    volatility = Column(Numeric(10, 6), nullable=True)  # Market volatility at execution
    
    # Metadata
    metadata = Column(JSONB, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    backtest = relationship("Backtest", back_populates="trades")
    
    def __repr__(self) -> str:
        return f"<BacktestTrade(id={self.id}, symbol={self.symbol}, side={self.side}, quantity={self.quantity})>"


class BacktestAnalytics(Base):
    """Detailed analytics and performance breakdown for backtests."""
    
    __tablename__ = "backtest_analytics"
    __table_args__ = (
        {"schema": "backtests"},
        Index("idx_backtest_analytics_backtest_id", "backtest_id"),
        Index("idx_backtest_analytics_period", "period_start", "period_end"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    backtest_id = Column(UUID(as_uuid=True), ForeignKey("backtests.backtests.id"), nullable=False)
    
    # Analysis Period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    period_type = Column(String(20), nullable=False)  # DAILY, WEEKLY, MONTHLY, YEARLY, FULL
    
    # Performance Metrics
    period_return = Column(Numeric(10, 6), nullable=True)
    cumulative_return = Column(Numeric(10, 6), nullable=True)
    benchmark_return = Column(Numeric(10, 6), nullable=True)
    excess_return = Column(Numeric(10, 6), nullable=True)
    
    # Risk Metrics
    volatility = Column(Numeric(10, 6), nullable=True)
    downside_volatility = Column(Numeric(10, 6), nullable=True)
    tracking_error = Column(Numeric(10, 6), nullable=True)
    
    # Ratios
    sharpe_ratio = Column(Numeric(10, 6), nullable=True)
    sortino_ratio = Column(Numeric(10, 6), nullable=True)
    information_ratio = Column(Numeric(10, 6), nullable=True)
    calmar_ratio = Column(Numeric(10, 6), nullable=True)
    
    # Drawdown Analysis
    max_drawdown = Column(Numeric(10, 6), nullable=True)
    drawdown_duration_days = Column(Integer, nullable=True)
    recovery_time_days = Column(Integer, nullable=True)
    
    # Trade Statistics
    trade_count = Column(Integer, nullable=True)
    win_count = Column(Integer, nullable=True)
    loss_count = Column(Integer, nullable=True)
    win_rate = Column(Numeric(5, 4), nullable=True)
    avg_win = Column(Numeric(10, 6), nullable=True)
    avg_loss = Column(Numeric(10, 6), nullable=True)
    profit_factor = Column(Numeric(10, 6), nullable=True)
    
    # Detailed Analytics
    detailed_metrics = Column(JSONB, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    backtest = relationship("Backtest", back_populates="analytics")
    
    def __repr__(self) -> str:
        return f"<BacktestAnalytics(id={self.id}, period_type={self.period_type}, period_return={self.period_return})>"
