"""
Pydantic schemas for portfolio operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, Field, validator


class RiskProfile(str, Enum):
    """Risk profile enumeration."""
    CONSERVATIVE = "CONSERVATIVE"
    MODERATE = "MODERATE"
    AGGRESSIVE = "AGGRESSIVE"


class OptimizationMethod(str, Enum):
    """Optimization method enumeration."""
    MEAN_VARIANCE = "MEAN_VARIANCE"
    RISK_PARITY = "RISK_PARITY"
    BLACK_LITTERMAN = "BLACK_LITTERMAN"
    MIN_VARIANCE = "MIN_VARIANCE"
    MAX_SHARPE = "MAX_SHARPE"
    HIERARCHICAL_RISK_PARITY = "HIERARCHICAL_RISK_PARITY"


class ObjectiveFunction(str, Enum):
    """Objective function enumeration."""
    MAX_SHARPE = "MAX_SHARPE"
    MIN_VARIANCE = "MIN_VARIANCE"
    MAX_RETURN = "MAX_RETURN"
    TARGET_RETURN = "TARGET_RETURN"
    TARGET_VOLATILITY = "TARGET_VOLATILITY"
    RISK_PARITY = "RISK_PARITY"


class RebalancingFrequency(str, Enum):
    """Rebalancing frequency enumeration."""
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    QUARTERLY = "QUARTERLY"


class TriggerType(str, Enum):
    """Rebalancing trigger type enumeration."""
    SCHEDULED = "SCHEDULED"
    DRIFT = "DRIFT"
    VOLATILITY = "VOLATILITY"
    RISK_LIMIT = "RISK_LIMIT"
    MANUAL = "MANUAL"


class PortfolioStatus(str, Enum):
    """Portfolio status enumeration."""
    ACTIVE = "ACTIVE"
    PAUSED = "PAUSED"
    CLOSED = "CLOSED"


class OptimizationStatus(str, Enum):
    """Optimization status enumeration."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class PortfolioConstraints(BaseModel):
    """Portfolio optimization constraints."""
    
    # Weight constraints
    min_weight: Decimal = Field(default=Decimal("0.0"), ge=0, le=1, description="Minimum strategy weight")
    max_weight: Decimal = Field(default=Decimal("0.5"), ge=0, le=1, description="Maximum strategy weight")
    max_concentration: Decimal = Field(default=Decimal("0.3"), ge=0, le=1, description="Maximum concentration")
    
    # Diversification constraints
    min_strategies: int = Field(default=3, ge=1, le=50, description="Minimum number of strategies")
    max_strategies: int = Field(default=20, ge=1, le=50, description="Maximum number of strategies")
    
    # Risk constraints
    max_volatility: Optional[Decimal] = Field(None, ge=0, le=1, description="Maximum portfolio volatility")
    max_drawdown: Optional[Decimal] = Field(None, ge=0, le=1, description="Maximum drawdown")
    min_sharpe_ratio: Optional[Decimal] = Field(None, description="Minimum Sharpe ratio")
    
    # Sector limits
    sector_limits: Dict[str, Decimal] = Field(default_factory=dict, description="Sector allocation limits")
    strategy_type_limits: Dict[str, Decimal] = Field(default_factory=dict, description="Strategy type limits")
    geographic_limits: Dict[str, Decimal] = Field(default_factory=dict, description="Geographic limits")
    
    # Turnover constraints
    max_turnover: Optional[Decimal] = Field(None, ge=0, le=2, description="Maximum portfolio turnover")
    transaction_cost_bps: Decimal = Field(default=Decimal("5.0"), ge=0, le=100, description="Transaction cost in bps")
    
    # Custom constraints
    custom_constraints: Dict[str, Any] = Field(default_factory=dict, description="Custom optimization constraints")


class OptimizationConfig(BaseModel):
    """Optimization configuration parameters."""
    
    # Method and objective
    optimization_method: OptimizationMethod = Field(default=OptimizationMethod.MEAN_VARIANCE)
    objective_function: ObjectiveFunction = Field(default=ObjectiveFunction.MAX_SHARPE)
    
    # Data configuration
    lookback_days: int = Field(default=252, ge=30, le=1825, description="Lookback period in days")
    correlation_window: int = Field(default=126, ge=20, le=500, description="Correlation window in days")
    
    # Risk parameters
    risk_free_rate: Decimal = Field(default=Decimal("0.02"), ge=0, le=0.1, description="Risk-free rate")
    confidence_level: Decimal = Field(default=Decimal("0.95"), gt=0, lt=1, description="Confidence level for VaR")
    
    # Black-Litterman parameters
    bl_tau: Optional[Decimal] = Field(default=Decimal("0.025"), gt=0, le=1, description="BL tau parameter")
    bl_confidence: Optional[Decimal] = Field(default=Decimal("0.95"), gt=0, lt=1, description="BL confidence level")
    
    # Risk parity parameters
    rp_tolerance: Optional[Decimal] = Field(default=Decimal("0.01"), gt=0, le=0.1, description="Risk parity tolerance")
    rp_max_iterations: Optional[int] = Field(default=1000, ge=100, le=10000, description="Max iterations")
    
    # Solver configuration
    solver: str = Field(default="ECOS", description="Optimization solver")
    solver_tolerance: Decimal = Field(default=Decimal("1e-6"), gt=0, description="Solver tolerance")
    max_iterations: int = Field(default=10000, ge=100, le=100000, description="Maximum solver iterations")
    
    # Advanced options
    enable_shrinkage: bool = Field(default=True, description="Enable covariance shrinkage")
    shrinkage_target: str = Field(default="diagonal", description="Shrinkage target")
    robust_estimation: bool = Field(default=False, description="Use robust covariance estimation")


class PortfolioCreate(BaseModel):
    """Schema for creating a new portfolio."""
    
    user_id: uuid.UUID = Field(..., description="User ID who owns the portfolio")
    name: str = Field(..., min_length=1, max_length=255, description="Portfolio name")
    description: Optional[str] = Field(None, description="Portfolio description")
    
    # Configuration
    optimization_method: OptimizationMethod = Field(default=OptimizationMethod.MEAN_VARIANCE)
    risk_profile: RiskProfile = Field(default=RiskProfile.MODERATE)
    investment_objective: Optional[str] = Field(None, max_length=100, description="Investment objective")
    
    # Target metrics
    target_return: Optional[Decimal] = Field(None, ge=0, le=1, description="Target annual return")
    max_volatility: Optional[Decimal] = Field(None, ge=0, le=1, description="Maximum volatility")
    max_drawdown: Optional[Decimal] = Field(None, ge=0, le=1, description="Maximum drawdown")
    
    # Rebalancing configuration
    rebalancing_frequency: RebalancingFrequency = Field(default=RebalancingFrequency.WEEKLY)
    drift_threshold: Decimal = Field(default=Decimal("0.05"), gt=0, le=0.5, description="Drift threshold")
    volatility_threshold: Decimal = Field(default=Decimal("0.02"), gt=0, le=0.5, description="Volatility threshold")
    
    # Constraints
    constraints: PortfolioConstraints = Field(default_factory=PortfolioConstraints)
    optimization_config: OptimizationConfig = Field(default_factory=OptimizationConfig)
    
    # Initial strategies
    initial_strategies: List[uuid.UUID] = Field(default_factory=list, description="Initial strategy IDs")


class PortfolioUpdate(BaseModel):
    """Schema for updating portfolio information."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[PortfolioStatus] = None
    risk_profile: Optional[RiskProfile] = None
    investment_objective: Optional[str] = None
    target_return: Optional[Decimal] = None
    max_volatility: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    rebalancing_frequency: Optional[RebalancingFrequency] = None
    drift_threshold: Optional[Decimal] = None
    volatility_threshold: Optional[Decimal] = None


class StrategyAllocation(BaseModel):
    """Strategy allocation within portfolio."""
    strategy_id: uuid.UUID
    target_weight: Decimal = Field(..., ge=0, le=1, description="Target allocation weight")
    min_weight: Decimal = Field(default=Decimal("0.0"), ge=0, le=1)
    max_weight: Decimal = Field(default=Decimal("1.0"), ge=0, le=1)
    risk_budget: Optional[Decimal] = Field(None, ge=0, le=1, description="Risk budget allocation")
    
    @validator("max_weight")
    def validate_max_weight(cls, v, values):
        """Validate max weight is greater than min weight."""
        if "min_weight" in values and v < values["min_weight"]:
            raise ValueError("Max weight must be greater than or equal to min weight")
        return v


class PortfolioAllocationRequest(BaseModel):
    """Request to set portfolio allocations."""
    allocations: List[StrategyAllocation] = Field(..., min_items=1, description="Strategy allocations")
    
    @validator("allocations")
    def validate_allocations_sum(cls, v):
        """Validate allocations sum to 1.0."""
        total_weight = sum(allocation.target_weight for allocation in v)
        if abs(total_weight - 1.0) > 0.001:  # Allow small rounding errors
            raise ValueError("Total allocation weights must sum to 1.0")
        return v


class OptimizationRequest(BaseModel):
    """Request to optimize portfolio."""
    strategy_ids: List[uuid.UUID] = Field(..., min_items=2, description="Strategy IDs to optimize")
    config: OptimizationConfig = Field(default_factory=OptimizationConfig)
    constraints: PortfolioConstraints = Field(default_factory=PortfolioConstraints)
    views: Optional[Dict[str, Any]] = Field(None, description="Market views for Black-Litterman")


class RebalancingRequest(BaseModel):
    """Request to rebalance portfolio."""
    trigger_type: TriggerType = Field(default=TriggerType.MANUAL)
    trigger_reason: Optional[str] = Field(None, description="Reason for rebalancing")
    execution_method: str = Field(default="IMMEDIATE", description="Execution method")
    force_rebalance: bool = Field(default=False, description="Force rebalancing even if not needed")


class PerformanceMetrics(BaseModel):
    """Portfolio performance metrics."""
    total_return: Optional[Decimal] = None
    annualized_return: Optional[Decimal] = None
    volatility: Optional[Decimal] = None
    sharpe_ratio: Optional[Decimal] = None
    sortino_ratio: Optional[Decimal] = None
    calmar_ratio: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    var_95: Optional[Decimal] = None
    cvar_95: Optional[Decimal] = None
    beta: Optional[Decimal] = None
    alpha: Optional[Decimal] = None
    information_ratio: Optional[Decimal] = None
    tracking_error: Optional[Decimal] = None


class DiversificationMetrics(BaseModel):
    """Portfolio diversification metrics."""
    effective_strategies: Optional[Decimal] = None
    concentration_index: Optional[Decimal] = None
    correlation_avg: Optional[Decimal] = None
    diversification_ratio: Optional[Decimal] = None


class Portfolio(BaseModel):
    """Public portfolio schema."""
    id: uuid.UUID
    user_id: uuid.UUID
    name: str
    description: Optional[str]
    
    # Configuration
    optimization_method: OptimizationMethod
    risk_profile: RiskProfile
    investment_objective: Optional[str]
    
    # Status and metrics
    status: PortfolioStatus
    total_value: Decimal
    cash_balance: Decimal
    invested_amount: Decimal
    
    # Performance
    performance_metrics: PerformanceMetrics
    diversification_metrics: DiversificationMetrics
    
    # Configuration
    rebalancing_frequency: RebalancingFrequency
    drift_threshold: Decimal
    volatility_threshold: Decimal
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    last_optimized_at: Optional[datetime]
    last_rebalanced_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class PortfolioAllocation(BaseModel):
    """Portfolio allocation schema."""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    strategy_id: uuid.UUID
    strategy_name: Optional[str]
    ai_paradigm: Optional[str]
    
    # Allocation details
    target_weight: Decimal
    current_weight: Decimal
    drift: Decimal
    
    # Performance contribution
    contribution_return: Optional[Decimal]
    contribution_risk: Optional[Decimal]
    risk_budget: Optional[Decimal]
    risk_contribution: Optional[Decimal]
    
    # Constraints
    min_weight: Decimal
    max_weight: Decimal
    is_active: bool
    
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class OptimizationRun(BaseModel):
    """Optimization run schema."""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    optimization_method: OptimizationMethod
    objective_function: ObjectiveFunction
    
    # Configuration
    lookback_days: int
    data_start_date: datetime
    data_end_date: datetime
    strategies_considered: List[uuid.UUID]
    
    # Status
    status: OptimizationStatus
    progress_pct: Decimal
    
    # Results
    optimal_weights: Optional[Dict[str, Decimal]]
    expected_return: Optional[Decimal]
    expected_volatility: Optional[Decimal]
    expected_sharpe: Optional[Decimal]
    
    # Diagnostics
    solver_status: Optional[str]
    optimization_time_seconds: Optional[Decimal]
    error_message: Optional[str]
    
    # Timestamps
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class RebalancingEvent(BaseModel):
    """Rebalancing event schema."""
    id: uuid.UUID
    portfolio_id: uuid.UUID
    trigger_type: TriggerType
    trigger_reason: Optional[str]
    
    # State changes
    pre_allocations: Dict[str, Decimal]
    post_allocations: Dict[str, Decimal]
    allocation_changes: Dict[str, Decimal]
    turnover: Decimal
    transaction_costs: Decimal
    
    # Execution
    status: str
    execution_method: Optional[str]
    execution_duration_minutes: Optional[int]
    
    # Timestamps
    created_at: datetime
    executed_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True
