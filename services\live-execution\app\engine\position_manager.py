"""
Real-time position management and tracking.
"""

import logging
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from decimal import Decimal
from collections import defaultdict

from app.core.config import settings
from app.schemas.execution import PositionSnapshot, AssetClass

logger = logging.getLogger(__name__)


class PositionManager:
    """Real-time position management."""
    
    def __init__(self):
        """Initialize position manager."""
        self.positions = {}  # position_id -> position data
        self.strategy_positions = defaultdict(list)  # strategy_id -> [position_ids]
        self.portfolio_positions = defaultdict(list)  # portfolio_id -> [position_ids]
        self.symbol_positions = defaultdict(list)  # symbol -> [position_ids]
        self.position_callbacks = []
        self.market_data_manager = None
        
    def set_market_data_manager(self, market_data_manager):
        """Set market data manager for position valuation."""
        self.market_data_manager = market_data_manager
    
    async def create_position(
        self,
        strategy_id: uuid.UUID,
        portfolio_id: uuid.UUID,
        user_id: uuid.UUID,
        symbol: str,
        asset_class: AssetClass,
        quantity: Decimal,
        avg_cost: Decimal,
        broker: str
    ) -> str:
        """Create new position."""
        try:
            position_id = f"POS_{uuid.uuid4().hex[:8]}"
            
            position = {
                "id": uuid.uuid4(),
                "position_id": position_id,
                "strategy_id": strategy_id,
                "portfolio_id": portfolio_id,
                "user_id": user_id,
                "symbol": symbol,
                "asset_class": asset_class,
                "quantity": quantity,
                "market_value": Decimal("0"),
                "notional_value": Decimal("0"),
                "avg_cost": avg_cost,
                "total_cost": quantity * avg_cost,
                "unrealized_pnl": Decimal("0"),
                "realized_pnl": Decimal("0"),
                "total_pnl": Decimal("0"),
                "last_price": None,
                "status": "ACTIVE",
                "position_type": "LONG" if quantity > 0 else "SHORT",
                "broker": broker,
                "opened_at": datetime.utcnow(),
                "last_updated": datetime.utcnow(),
                "trades": []  # Track individual trades
            }
            
            # Store position
            self.positions[position_id] = position
            
            # Update indexes
            self.strategy_positions[strategy_id].append(position_id)
            self.portfolio_positions[portfolio_id].append(position_id)
            self.symbol_positions[symbol].append(position_id)
            
            # Subscribe to market data for valuation
            if self.market_data_manager:
                await self.market_data_manager.subscribe(
                    symbol, asset_class, self._market_data_callback
                )
            
            # Initial valuation
            await self._update_position_valuation(position_id)
            
            logger.info(f"Created position {position_id} for {symbol}")
            
            return position_id
            
        except Exception as e:
            logger.error(f"Position creation failed: {e}")
            raise
    
    async def update_position(
        self,
        position_id: str,
        quantity_change: Decimal,
        price: Decimal,
        trade_value: Decimal,
        trade_type: str = "EXECUTION"
    ):
        """Update position with new trade."""
        try:
            if position_id not in self.positions:
                raise ValueError(f"Position {position_id} not found")
            
            position = self.positions[position_id]
            old_quantity = position["quantity"]
            new_quantity = old_quantity + quantity_change
            
            # Record trade
            trade = {
                "timestamp": datetime.utcnow(),
                "quantity": quantity_change,
                "price": price,
                "value": trade_value,
                "type": trade_type
            }
            position["trades"].append(trade)
            
            # Update position
            if new_quantity == 0:
                # Position closed
                position["status"] = "CLOSED"
                position["closed_at"] = datetime.utcnow()
                
                # Calculate realized P&L
                total_cost = position["total_cost"]
                total_proceeds = sum(
                    trade["value"] for trade in position["trades"] 
                    if trade["quantity"] < 0  # Sales
                )
                position["realized_pnl"] = total_proceeds - total_cost
                
            else:
                # Update quantity and average cost
                if quantity_change > 0:  # Adding to position
                    total_cost = position["total_cost"] + trade_value
                    position["avg_cost"] = total_cost / new_quantity
                    position["total_cost"] = total_cost
                else:  # Reducing position
                    # Realize some P&L
                    proportion_sold = abs(quantity_change) / old_quantity
                    realized_pnl = proportion_sold * position["unrealized_pnl"]
                    position["realized_pnl"] += realized_pnl
                    
                    # Reduce total cost proportionally
                    position["total_cost"] *= (1 - proportion_sold)
                
                position["quantity"] = new_quantity
                position["position_type"] = "LONG" if new_quantity > 0 else "SHORT"
            
            position["last_updated"] = datetime.utcnow()
            
            # Update valuation
            await self._update_position_valuation(position_id)
            
            # Notify callbacks
            await self._notify_position_update(position_id)
            
            logger.info(f"Updated position {position_id}: {quantity_change} @ {price}")
            
        except Exception as e:
            logger.error(f"Position update failed: {e}")
            raise
    
    async def _update_position_valuation(self, position_id: str):
        """Update position market valuation."""
        try:
            position = self.positions[position_id]
            
            # Get current market price
            if self.market_data_manager:
                market_data = await self.market_data_manager.get_market_data(
                    position["symbol"], position["asset_class"]
                )
                
                if market_data and market_data.last_price:
                    position["last_price"] = market_data.last_price
                    position["market_value"] = position["quantity"] * market_data.last_price
                    position["notional_value"] = abs(position["market_value"])
                    
                    # Calculate unrealized P&L
                    if position["status"] == "ACTIVE":
                        cost_basis = position["total_cost"]
                        market_value = position["market_value"]
                        position["unrealized_pnl"] = market_value - cost_basis
                        position["total_pnl"] = position["unrealized_pnl"] + position["realized_pnl"]
            
        except Exception as e:
            logger.error(f"Position valuation update failed: {e}")
    
    async def _market_data_callback(self, market_data):
        """Handle market data updates for position valuation."""
        try:
            symbol = market_data.symbol
            
            # Update all positions for this symbol
            for position_id in self.symbol_positions.get(symbol, []):
                await self._update_position_valuation(position_id)
                await self._notify_position_update(position_id)
                
        except Exception as e:
            logger.error(f"Market data callback failed: {e}")
    
    async def _notify_position_update(self, position_id: str):
        """Notify callbacks of position update."""
        position = self.positions[position_id]
        
        for callback in self.position_callbacks:
            try:
                await callback(position)
            except Exception as e:
                logger.error(f"Position callback failed: {e}")
    
    async def get_position(self, position_id: str) -> Optional[Dict[str, Any]]:
        """Get position by ID."""
        return self.positions.get(position_id)
    
    async def get_strategy_positions(self, strategy_id: uuid.UUID) -> List[Dict[str, Any]]:
        """Get all positions for a strategy."""
        position_ids = self.strategy_positions.get(strategy_id, [])
        return [self.positions[pid] for pid in position_ids if pid in self.positions]
    
    async def get_portfolio_positions(self, portfolio_id: uuid.UUID) -> List[Dict[str, Any]]:
        """Get all positions for a portfolio."""
        position_ids = self.portfolio_positions.get(portfolio_id, [])
        return [self.positions[pid] for pid in position_ids if pid in self.positions]
    
    async def get_symbol_positions(self, symbol: str) -> List[Dict[str, Any]]:
        """Get all positions for a symbol."""
        position_ids = self.symbol_positions.get(symbol, [])
        return [self.positions[pid] for pid in position_ids if pid in self.positions]
    
    async def get_portfolio_summary(self, portfolio_id: uuid.UUID) -> Dict[str, Any]:
        """Get portfolio position summary."""
        try:
            positions = await self.get_portfolio_positions(portfolio_id)
            
            if not positions:
                return {
                    "portfolio_id": str(portfolio_id),
                    "total_positions": 0,
                    "total_market_value": Decimal("0"),
                    "total_pnl": Decimal("0"),
                    "positions": []
                }
            
            # Calculate totals
            total_market_value = sum(pos["market_value"] for pos in positions)
            total_pnl = sum(pos["total_pnl"] for pos in positions)
            total_unrealized_pnl = sum(pos["unrealized_pnl"] for pos in positions)
            total_realized_pnl = sum(pos["realized_pnl"] for pos in positions)
            
            # Group by asset class
            by_asset_class = defaultdict(list)
            for pos in positions:
                by_asset_class[pos["asset_class"].value].append(pos)
            
            return {
                "portfolio_id": str(portfolio_id),
                "total_positions": len(positions),
                "active_positions": len([p for p in positions if p["status"] == "ACTIVE"]),
                "total_market_value": total_market_value,
                "total_pnl": total_pnl,
                "total_unrealized_pnl": total_unrealized_pnl,
                "total_realized_pnl": total_realized_pnl,
                "by_asset_class": {
                    asset_class: {
                        "count": len(positions),
                        "market_value": sum(pos["market_value"] for pos in positions),
                        "pnl": sum(pos["total_pnl"] for pos in positions)
                    }
                    for asset_class, positions in by_asset_class.items()
                },
                "top_positions": sorted(
                    positions, 
                    key=lambda x: abs(x["market_value"]), 
                    reverse=True
                )[:10],
                "last_updated": datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Portfolio summary failed: {e}")
            return {"error": str(e)}
    
    async def calculate_portfolio_risk(self, portfolio_id: uuid.UUID) -> Dict[str, Any]:
        """Calculate portfolio risk metrics."""
        try:
            positions = await self.get_portfolio_positions(portfolio_id)
            
            if not positions:
                return {"error": "No positions found"}
            
            # Calculate basic risk metrics
            total_notional = sum(abs(pos["notional_value"]) for pos in positions)
            total_market_value = sum(pos["market_value"] for pos in positions if pos["market_value"] > 0)
            
            # Concentration risk
            concentrations = {}
            for pos in positions:
                symbol = pos["symbol"]
                concentration = abs(pos["notional_value"]) / total_notional if total_notional > 0 else 0
                concentrations[symbol] = concentration
            
            max_concentration = max(concentrations.values()) if concentrations else 0
            
            # Leverage
            leverage = total_notional / total_market_value if total_market_value > 0 else 0
            
            return {
                "portfolio_id": str(portfolio_id),
                "total_notional": total_notional,
                "total_market_value": total_market_value,
                "leverage": leverage,
                "max_concentration": max_concentration,
                "concentrations": concentrations,
                "position_count": len(positions),
                "risk_score": min(leverage * max_concentration * 10, 10),  # Simple risk score
                "calculated_at": datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Portfolio risk calculation failed: {e}")
            return {"error": str(e)}
    
    def add_position_callback(self, callback):
        """Add position update callback."""
        self.position_callbacks.append(callback)
    
    def remove_position_callback(self, callback):
        """Remove position update callback."""
        if callback in self.position_callbacks:
            self.position_callbacks.remove(callback)
    
    async def cleanup(self):
        """Cleanup position manager."""
        # Unsubscribe from market data
        if self.market_data_manager:
            for symbol in self.symbol_positions.keys():
                try:
                    # TODO: Get asset class for unsubscribe
                    pass
                except Exception as e:
                    logger.error(f"Error unsubscribing from {symbol}: {e}")
        
        # Clear data
        self.positions.clear()
        self.strategy_positions.clear()
        self.portfolio_positions.clear()
        self.symbol_positions.clear()
        self.position_callbacks.clear()
        
        logger.info("Position manager cleaned up")
