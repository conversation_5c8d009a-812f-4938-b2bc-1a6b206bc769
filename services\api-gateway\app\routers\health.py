"""
Health check router for monitoring service status.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """
    Basic health check endpoint.
    
    Returns:
        dict: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "api-gateway",
        "version": "0.1.0"
    }


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Returns:
        dict: Liveness status
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_db)):
    """
    Kubernetes readiness probe endpoint.
    
    Args:
        db: Database session
        
    Returns:
        dict: Readiness status including dependencies
        
    Raises:
        HTTPException: If service is not ready
    """
    checks = {}
    overall_status = "ready"
    
    # Database connectivity check
    try:
        result = await db.execute(text("SELECT 1"))
        result.scalar()
        checks["database"] = {"status": "healthy", "response_time_ms": 0}
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        checks["database"] = {"status": "unhealthy", "error": str(e)}
        overall_status = "not_ready"
    
    # Redis connectivity check (if configured)
    try:
        # TODO: Add Redis health check when Redis client is implemented
        checks["redis"] = {"status": "healthy", "response_time_ms": 0}
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        checks["redis"] = {"status": "unhealthy", "error": str(e)}
        overall_status = "not_ready"
    
    response = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks
    }
    
    if overall_status != "ready":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=response
        )
    
    return response


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """
    Detailed health check with comprehensive system information.
    
    Args:
        db: Database session
        
    Returns:
        dict: Detailed health status
    """
    import psutil
    import sys
    
    # System information
    system_info = {
        "python_version": sys.version,
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent,
    }
    
    # Database information
    db_info = {}
    try:
        # Get database version
        result = await db.execute(text("SELECT version()"))
        db_version = result.scalar()
        db_info["version"] = db_version
        
        # Get connection count
        result = await db.execute(text("SELECT count(*) FROM pg_stat_activity"))
        connection_count = result.scalar()
        db_info["active_connections"] = connection_count
        
        db_info["status"] = "healthy"
    except Exception as e:
        logger.error(f"Database detailed check failed: {e}")
        db_info["status"] = "unhealthy"
        db_info["error"] = str(e)
    
    # Application information
    app_info = {
        "name": settings.APP_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
    }
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "system": system_info,
        "database": db_info,
        "application": app_info
    }
