"""
Risk management and constraints engine for portfolio construction.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from decimal import Decimal
from datetime import datetime, timedelta

from app.core.config import settings
from app.schemas.portfolio import PortfolioConstraints

logger = logging.getLogger(__name__)


class RiskBudgetManager:
    """Manage portfolio-level risk budgeting and allocation limits."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize risk budget manager."""
        self.max_portfolio_risk = float(config.get("max_portfolio_risk", settings.MAX_PORTFOLIO_VOLATILITY))
        self.risk_budget_tolerance = float(config.get("risk_budget_tolerance", 0.01))
    
    def allocate_risk_budgets(
        self,
        strategies: List[str],
        method: str = "EQUAL",
        custom_budgets: Optional[Dict[str, float]] = None,
        strategy_volatilities: Optional[Dict[str, float]] = None
    ) -> Dict[str, float]:
        """Allocate risk budgets across strategies."""
        
        if method == "EQUAL":
            # Equal risk budgets
            budget_per_strategy = 1.0 / len(strategies)
            return {strategy: budget_per_strategy for strategy in strategies}
        
        elif method == "INVERSE_VOLATILITY" and strategy_volatilities:
            # Inverse volatility weighting
            inv_vols = {s: 1.0 / max(strategy_volatilities[s], 0.001) for s in strategies}
            total_inv_vol = sum(inv_vols.values())
            return {s: inv_vol / total_inv_vol for s, inv_vol in inv_vols.items()}
        
        elif method == "CUSTOM" and custom_budgets:
            # Custom risk budgets
            total_budget = sum(custom_budgets.get(s, 0) for s in strategies)
            if abs(total_budget - 1.0) > 0.001:
                # Normalize to sum to 1
                return {s: custom_budgets.get(s, 0) / total_budget for s in strategies}
            return {s: custom_budgets.get(s, 0) for s in strategies}
        
        else:
            # Default to equal budgets
            budget_per_strategy = 1.0 / len(strategies)
            return {strategy: budget_per_strategy for strategy in strategies}
    
    def calculate_risk_contributions(
        self,
        weights: np.ndarray,
        covariance_matrix: np.ndarray,
        strategy_names: List[str]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate risk contributions for each strategy."""
        
        portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        if portfolio_variance <= 0:
            return {name: {"marginal_risk": 0, "component_risk": 0, "risk_contribution": 0} 
                   for name in strategy_names}
        
        # Marginal risk contributions
        marginal_risk = np.dot(covariance_matrix, weights) / portfolio_volatility
        
        # Component risk contributions
        component_risk = weights * marginal_risk
        
        # Risk contribution percentages
        risk_contributions = component_risk / portfolio_volatility
        
        return {
            strategy_names[i]: {
                "marginal_risk": marginal_risk[i],
                "component_risk": component_risk[i],
                "risk_contribution": risk_contributions[i],
                "risk_contribution_pct": risk_contributions[i] / np.sum(risk_contributions) if np.sum(risk_contributions) > 0 else 0
            }
            for i in range(len(strategy_names))
        }
    
    def check_risk_budget_compliance(
        self,
        actual_risk_contributions: Dict[str, float],
        target_risk_budgets: Dict[str, float]
    ) -> Dict[str, Any]:
        """Check compliance with risk budget allocations."""
        
        violations = []
        total_deviation = 0.0
        
        for strategy in target_risk_budgets:
            actual = actual_risk_contributions.get(strategy, 0.0)
            target = target_risk_budgets[strategy]
            deviation = abs(actual - target)
            
            if deviation > self.risk_budget_tolerance:
                violations.append({
                    "strategy": strategy,
                    "actual": actual,
                    "target": target,
                    "deviation": deviation,
                    "severity": "HIGH" if deviation > self.risk_budget_tolerance * 2 else "MEDIUM"
                })
            
            total_deviation += deviation
        
        is_compliant = len(violations) == 0
        
        return {
            "is_compliant": is_compliant,
            "violations": violations,
            "total_deviation": total_deviation,
            "max_deviation": max([v["deviation"] for v in violations], default=0),
            "tolerance": self.risk_budget_tolerance
        }


class DiversificationConstraints:
    """Manage diversification constraints and limits."""
    
    def __init__(self, constraints: PortfolioConstraints):
        """Initialize diversification constraints."""
        self.constraints = constraints
    
    def check_sector_limits(
        self,
        weights: Dict[str, float],
        strategy_sectors: Dict[str, str]
    ) -> Dict[str, Any]:
        """Check sector diversification limits."""
        
        # Aggregate weights by sector
        sector_weights = {}
        for strategy, weight in weights.items():
            sector = strategy_sectors.get(strategy, "UNKNOWN")
            sector_weights[sector] = sector_weights.get(sector, 0.0) + weight
        
        violations = []
        for sector, weight in sector_weights.items():
            limit = self.constraints.sector_limits.get(sector)
            if limit and weight > float(limit):
                violations.append({
                    "sector": sector,
                    "weight": weight,
                    "limit": float(limit),
                    "excess": weight - float(limit)
                })
        
        return {
            "is_compliant": len(violations) == 0,
            "violations": violations,
            "sector_weights": sector_weights
        }
    
    def check_strategy_type_limits(
        self,
        weights: Dict[str, float],
        strategy_types: Dict[str, str]
    ) -> Dict[str, Any]:
        """Check strategy type diversification limits."""
        
        # Aggregate weights by strategy type (AI paradigm)
        type_weights = {}
        for strategy, weight in weights.items():
            strategy_type = strategy_types.get(strategy, "UNKNOWN")
            type_weights[strategy_type] = type_weights.get(strategy_type, 0.0) + weight
        
        violations = []
        for strategy_type, weight in type_weights.items():
            limit = self.constraints.strategy_type_limits.get(strategy_type)
            if limit and weight > float(limit):
                violations.append({
                    "strategy_type": strategy_type,
                    "weight": weight,
                    "limit": float(limit),
                    "excess": weight - float(limit)
                })
        
        return {
            "is_compliant": len(violations) == 0,
            "violations": violations,
            "type_weights": type_weights
        }
    
    def check_geographic_limits(
        self,
        weights: Dict[str, float],
        strategy_regions: Dict[str, str]
    ) -> Dict[str, Any]:
        """Check geographic diversification limits."""
        
        # Aggregate weights by geographic region
        region_weights = {}
        for strategy, weight in weights.items():
            region = strategy_regions.get(strategy, "UNKNOWN")
            region_weights[region] = region_weights.get(region, 0.0) + weight
        
        violations = []
        for region, weight in region_weights.items():
            limit = self.constraints.geographic_limits.get(region)
            if limit and weight > float(limit):
                violations.append({
                    "region": region,
                    "weight": weight,
                    "limit": float(limit),
                    "excess": weight - float(limit)
                })
        
        return {
            "is_compliant": len(violations) == 0,
            "violations": violations,
            "region_weights": region_weights
        }
    
    def calculate_concentration_metrics(self, weights: Dict[str, float]) -> Dict[str, float]:
        """Calculate portfolio concentration metrics."""
        
        weight_values = list(weights.values())
        
        # Herfindahl-Hirschman Index
        hhi = sum(w ** 2 for w in weight_values)
        
        # Effective number of strategies
        effective_strategies = 1.0 / hhi if hhi > 0 else 0
        
        # Maximum weight
        max_weight = max(weight_values) if weight_values else 0
        
        # Top 3 concentration
        sorted_weights = sorted(weight_values, reverse=True)
        top3_concentration = sum(sorted_weights[:3]) if len(sorted_weights) >= 3 else sum(sorted_weights)
        
        # Gini coefficient (inequality measure)
        gini = self._calculate_gini_coefficient(weight_values)
        
        return {
            "herfindahl_index": hhi,
            "effective_strategies": effective_strategies,
            "max_weight": max_weight,
            "top3_concentration": top3_concentration,
            "gini_coefficient": gini
        }
    
    def _calculate_gini_coefficient(self, weights: List[float]) -> float:
        """Calculate Gini coefficient for weight distribution."""
        if not weights or len(weights) < 2:
            return 0.0
        
        sorted_weights = sorted(weights)
        n = len(sorted_weights)
        cumsum = np.cumsum(sorted_weights)
        
        return (n + 1 - 2 * np.sum(cumsum) / cumsum[-1]) / n if cumsum[-1] > 0 else 0.0


class CorrelationConstraints:
    """Manage correlation-based constraints and position sizing."""
    
    def __init__(self, max_correlation: float = 0.8):
        """Initialize correlation constraints."""
        self.max_correlation = max_correlation
    
    def check_correlation_limits(
        self,
        correlation_matrix: np.ndarray,
        strategy_names: List[str],
        weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """Check correlation constraints."""
        
        # Find high correlation pairs
        high_corr_pairs = []
        n = len(correlation_matrix)
        
        for i in range(n):
            for j in range(i + 1, n):
                corr = correlation_matrix[i, j]
                if abs(corr) > self.max_correlation:
                    pair_weight = 0.0
                    if weights:
                        pair_weight = weights.get(strategy_names[i], 0) + weights.get(strategy_names[j], 0)
                    
                    high_corr_pairs.append({
                        "strategy_1": strategy_names[i],
                        "strategy_2": strategy_names[j],
                        "correlation": corr,
                        "combined_weight": pair_weight,
                        "severity": "HIGH" if abs(corr) > 0.9 else "MEDIUM"
                    })
        
        # Calculate weighted average correlation
        if weights:
            weighted_corr = self._calculate_weighted_correlation(correlation_matrix, weights, strategy_names)
        else:
            weighted_corr = np.mean(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)])
        
        return {
            "is_compliant": len(high_corr_pairs) == 0,
            "high_correlation_pairs": high_corr_pairs,
            "max_correlation_threshold": self.max_correlation,
            "weighted_avg_correlation": weighted_corr,
            "max_pairwise_correlation": np.max(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)])
        }
    
    def _calculate_weighted_correlation(
        self,
        correlation_matrix: np.ndarray,
        weights: Dict[str, float],
        strategy_names: List[str]
    ) -> float:
        """Calculate portfolio weighted average correlation."""
        
        weight_vector = np.array([weights.get(name, 0) for name in strategy_names])
        
        # Weight matrix
        weight_matrix = np.outer(weight_vector, weight_vector)
        
        # Weighted correlation (excluding diagonal)
        mask = ~np.eye(len(correlation_matrix), dtype=bool)
        weighted_corr = np.sum(correlation_matrix[mask] * weight_matrix[mask]) / np.sum(weight_matrix[mask])
        
        return weighted_corr if not np.isnan(weighted_corr) else 0.0
    
    def suggest_correlation_adjustments(
        self,
        correlation_matrix: np.ndarray,
        strategy_names: List[str],
        current_weights: Dict[str, float]
    ) -> Dict[str, Any]:
        """Suggest weight adjustments to reduce correlation."""
        
        suggestions = []
        
        # Find highly correlated pairs with significant weights
        n = len(correlation_matrix)
        for i in range(n):
            for j in range(i + 1, n):
                corr = abs(correlation_matrix[i, j])
                if corr > self.max_correlation:
                    strategy_1 = strategy_names[i]
                    strategy_2 = strategy_names[j]
                    weight_1 = current_weights.get(strategy_1, 0)
                    weight_2 = current_weights.get(strategy_2, 0)
                    
                    if weight_1 > 0.05 and weight_2 > 0.05:  # Both have significant weights
                        # Suggest reducing the weight of the larger position
                        if weight_1 > weight_2:
                            reduction = min(weight_1 * 0.3, weight_1 - 0.05)
                            suggestions.append({
                                "strategy": strategy_1,
                                "current_weight": weight_1,
                                "suggested_weight": weight_1 - reduction,
                                "reason": f"High correlation ({corr:.2f}) with {strategy_2}",
                                "correlation": corr,
                                "correlated_with": strategy_2
                            })
                        else:
                            reduction = min(weight_2 * 0.3, weight_2 - 0.05)
                            suggestions.append({
                                "strategy": strategy_2,
                                "current_weight": weight_2,
                                "suggested_weight": weight_2 - reduction,
                                "reason": f"High correlation ({corr:.2f}) with {strategy_1}",
                                "correlation": corr,
                                "correlated_with": strategy_1
                            })
        
        return {
            "suggestions": suggestions,
            "total_weight_reduction": sum(s["current_weight"] - s["suggested_weight"] for s in suggestions)
        }


class DrawdownConstraints:
    """Manage drawdown-based risk controls and position scaling."""
    
    def __init__(self, max_drawdown: float = 0.1):
        """Initialize drawdown constraints."""
        self.max_drawdown = max_drawdown
    
    def calculate_strategy_drawdowns(
        self,
        strategy_returns: Dict[str, pd.Series]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate drawdown metrics for each strategy."""
        
        drawdown_metrics = {}
        
        for strategy, returns in strategy_returns.items():
            if len(returns) < 2:
                drawdown_metrics[strategy] = {
                    "max_drawdown": 0.0,
                    "current_drawdown": 0.0,
                    "drawdown_duration": 0,
                    "recovery_time": 0
                }
                continue
            
            # Calculate cumulative returns
            cumulative = (1 + returns).cumprod()
            
            # Calculate running maximum
            running_max = cumulative.expanding().max()
            
            # Calculate drawdown
            drawdown = (cumulative - running_max) / running_max
            
            # Maximum drawdown
            max_dd = drawdown.min()
            
            # Current drawdown
            current_dd = drawdown.iloc[-1]
            
            # Drawdown duration and recovery time
            dd_duration, recovery_time = self._calculate_drawdown_duration(drawdown)
            
            drawdown_metrics[strategy] = {
                "max_drawdown": abs(max_dd),
                "current_drawdown": abs(current_dd),
                "drawdown_duration": dd_duration,
                "recovery_time": recovery_time,
                "underwater_periods": len(drawdown[drawdown < -0.01])  # Periods with >1% drawdown
            }
        
        return drawdown_metrics
    
    def _calculate_drawdown_duration(self, drawdown_series: pd.Series) -> Tuple[int, int]:
        """Calculate drawdown duration and recovery time."""
        
        # Find periods in drawdown (< -1%)
        in_drawdown = drawdown_series < -0.01
        
        if not in_drawdown.any():
            return 0, 0
        
        # Current drawdown duration
        current_duration = 0
        if in_drawdown.iloc[-1]:
            # Currently in drawdown
            for i in range(len(in_drawdown) - 1, -1, -1):
                if in_drawdown.iloc[i]:
                    current_duration += 1
                else:
                    break
        
        # Maximum drawdown duration
        max_duration = 0
        current_streak = 0
        
        for in_dd in in_drawdown:
            if in_dd:
                current_streak += 1
                max_duration = max(max_duration, current_streak)
            else:
                current_streak = 0
        
        return current_duration, max_duration
    
    def suggest_drawdown_adjustments(
        self,
        drawdown_metrics: Dict[str, Dict[str, float]],
        current_weights: Dict[str, float]
    ) -> Dict[str, Any]:
        """Suggest weight adjustments based on drawdown analysis."""
        
        suggestions = []
        
        for strategy, metrics in drawdown_metrics.items():
            current_weight = current_weights.get(strategy, 0)
            
            if current_weight > 0.01:  # Only consider strategies with meaningful weights
                max_dd = metrics["max_drawdown"]
                current_dd = metrics["current_drawdown"]
                
                # Suggest reduction for high drawdown strategies
                if max_dd > self.max_drawdown:
                    # Scale down based on excess drawdown
                    excess_dd = max_dd - self.max_drawdown
                    reduction_factor = min(0.5, excess_dd / self.max_drawdown)
                    suggested_weight = current_weight * (1 - reduction_factor)
                    
                    suggestions.append({
                        "strategy": strategy,
                        "current_weight": current_weight,
                        "suggested_weight": suggested_weight,
                        "reason": f"High maximum drawdown: {max_dd:.1%}",
                        "max_drawdown": max_dd,
                        "reduction_factor": reduction_factor
                    })
                
                # Additional reduction for currently underwater strategies
                elif current_dd > 0.05:  # Currently in >5% drawdown
                    reduction_factor = min(0.3, current_dd / 0.1)
                    suggested_weight = current_weight * (1 - reduction_factor)
                    
                    suggestions.append({
                        "strategy": strategy,
                        "current_weight": current_weight,
                        "suggested_weight": suggested_weight,
                        "reason": f"Currently in drawdown: {current_dd:.1%}",
                        "current_drawdown": current_dd,
                        "reduction_factor": reduction_factor
                    })
        
        return {
            "suggestions": suggestions,
            "total_weight_reduction": sum(s["current_weight"] - s["suggested_weight"] for s in suggestions)
        }


class RiskManager:
    """Main risk management coordinator."""
    
    def __init__(self):
        """Initialize risk manager."""
        self.risk_budget_manager = RiskBudgetManager({})
        self.diversification_constraints = None  # Initialized per portfolio
        self.correlation_constraints = CorrelationConstraints()
        self.drawdown_constraints = DrawdownConstraints()
    
    async def cleanup(self):
        """Cleanup risk manager resources."""
        pass
    
    def validate_portfolio_constraints(
        self,
        weights: Dict[str, float],
        constraints: PortfolioConstraints,
        strategy_metadata: Dict[str, Dict[str, Any]],
        correlation_matrix: Optional[np.ndarray] = None,
        strategy_returns: Optional[Dict[str, pd.Series]] = None
    ) -> Dict[str, Any]:
        """Comprehensive portfolio constraint validation."""
        
        validation_results = {
            "is_valid": True,
            "violations": [],
            "warnings": [],
            "suggestions": []
        }
        
        try:
            # Initialize diversification constraints
            self.diversification_constraints = DiversificationConstraints(constraints)
            
            # 1. Basic weight constraints
            for strategy, weight in weights.items():
                if weight < float(constraints.min_weight):
                    validation_results["violations"].append({
                        "type": "MIN_WEIGHT",
                        "strategy": strategy,
                        "value": weight,
                        "limit": float(constraints.min_weight)
                    })
                    validation_results["is_valid"] = False
                
                if weight > float(constraints.max_weight):
                    validation_results["violations"].append({
                        "type": "MAX_WEIGHT",
                        "strategy": strategy,
                        "value": weight,
                        "limit": float(constraints.max_weight)
                    })
                    validation_results["is_valid"] = False
            
            # 2. Portfolio size constraints
            num_strategies = len([w for w in weights.values() if w > 0.001])
            if num_strategies < constraints.min_strategies:
                validation_results["violations"].append({
                    "type": "MIN_STRATEGIES",
                    "value": num_strategies,
                    "limit": constraints.min_strategies
                })
                validation_results["is_valid"] = False
            
            if num_strategies > constraints.max_strategies:
                validation_results["violations"].append({
                    "type": "MAX_STRATEGIES",
                    "value": num_strategies,
                    "limit": constraints.max_strategies
                })
                validation_results["is_valid"] = False
            
            # 3. Concentration constraints
            concentration_metrics = self.diversification_constraints.calculate_concentration_metrics(weights)
            if concentration_metrics["max_weight"] > float(constraints.max_concentration):
                validation_results["violations"].append({
                    "type": "MAX_CONCENTRATION",
                    "value": concentration_metrics["max_weight"],
                    "limit": float(constraints.max_concentration)
                })
                validation_results["is_valid"] = False
            
            # 4. Sector/Type/Geographic limits
            if strategy_metadata:
                # Extract metadata
                strategy_sectors = {s: meta.get("sector", "UNKNOWN") for s, meta in strategy_metadata.items()}
                strategy_types = {s: meta.get("ai_paradigm", "UNKNOWN") for s, meta in strategy_metadata.items()}
                strategy_regions = {s: meta.get("region", "UNKNOWN") for s, meta in strategy_metadata.items()}
                
                # Check sector limits
                sector_check = self.diversification_constraints.check_sector_limits(weights, strategy_sectors)
                if not sector_check["is_compliant"]:
                    validation_results["violations"].extend([
                        {"type": "SECTOR_LIMIT", **violation} for violation in sector_check["violations"]
                    ])
                    validation_results["is_valid"] = False
                
                # Check strategy type limits
                type_check = self.diversification_constraints.check_strategy_type_limits(weights, strategy_types)
                if not type_check["is_compliant"]:
                    validation_results["violations"].extend([
                        {"type": "STRATEGY_TYPE_LIMIT", **violation} for violation in type_check["violations"]
                    ])
                    validation_results["is_valid"] = False
                
                # Check geographic limits
                geo_check = self.diversification_constraints.check_geographic_limits(weights, strategy_regions)
                if not geo_check["is_compliant"]:
                    validation_results["violations"].extend([
                        {"type": "GEOGRAPHIC_LIMIT", **violation} for violation in geo_check["violations"]
                    ])
                    validation_results["is_valid"] = False
            
            # 5. Correlation constraints
            if correlation_matrix is not None:
                strategy_names = list(weights.keys())
                corr_check = self.correlation_constraints.check_correlation_limits(
                    correlation_matrix, strategy_names, weights
                )
                if not corr_check["is_compliant"]:
                    validation_results["warnings"].extend([
                        {"type": "HIGH_CORRELATION", **pair} for pair in corr_check["high_correlation_pairs"]
                    ])
                    
                    # Add suggestions for correlation adjustments
                    corr_suggestions = self.correlation_constraints.suggest_correlation_adjustments(
                        correlation_matrix, strategy_names, weights
                    )
                    validation_results["suggestions"].extend(corr_suggestions["suggestions"])
            
            # 6. Drawdown constraints
            if strategy_returns:
                drawdown_metrics = self.drawdown_constraints.calculate_strategy_drawdowns(strategy_returns)
                drawdown_suggestions = self.drawdown_constraints.suggest_drawdown_adjustments(
                    drawdown_metrics, weights
                )
                validation_results["suggestions"].extend(drawdown_suggestions["suggestions"])
            
            # 7. Weight sum validation
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.001:
                validation_results["violations"].append({
                    "type": "WEIGHT_SUM",
                    "value": total_weight,
                    "limit": 1.0
                })
                validation_results["is_valid"] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating portfolio constraints: {e}")
            return {
                "is_valid": False,
                "violations": [{"type": "VALIDATION_ERROR", "error": str(e)}],
                "warnings": [],
                "suggestions": []
            }
