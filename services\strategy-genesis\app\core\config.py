"""
Configuration settings for Strategy Genesis service.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "AthenaTrader Strategy Genesis"
    VERSION: str = "0.1.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Database
    DATABASE_URL: str = "postgresql://athena_user:athena_password@localhost:5432/athena_trader"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # External Services
    DATA_NEXUS_URL: str = "http://localhost:8001"
    
    # AI/ML Configuration
    # Genetic Programming
    GP_POPULATION_SIZE: int = 100
    GP_GENERATIONS: int = 50
    GP_TOURNAMENT_SIZE: int = 3
    GP_CROSSOVER_PROB: float = 0.8
    GP_MUTATION_PROB: float = 0.2
    GP_MAX_TREE_DEPTH: int = 10
    
    # Reinforcement Learning
    RL_TRAINING_EPISODES: int = 1000
    RL_LEARNING_RATE: float = 0.001
    RL_DISCOUNT_FACTOR: float = 0.99
    RL_EXPLORATION_RATE: float = 0.1
    RL_BATCH_SIZE: int = 32
    RL_MEMORY_SIZE: int = 10000
    
    # Deep Learning
    DL_BATCH_SIZE: int = 64
    DL_EPOCHS: int = 100
    DL_LEARNING_RATE: float = 0.001
    DL_DROPOUT_RATE: float = 0.2
    DL_LSTM_UNITS: int = 128
    DL_ATTENTION_HEADS: int = 8
    DL_SEQUENCE_LENGTH: int = 60
    
    # Model Storage
    MODEL_STORAGE_PATH: str = "/app/models"
    CHECKPOINT_INTERVAL: int = 10  # Save every N epochs/generations
    
    # Performance Metrics
    PERFORMANCE_METRICS: List[str] = [
        "total_return",
        "sharpe_ratio",
        "max_drawdown",
        "win_rate",
        "profit_factor",
        "calmar_ratio",
        "sortino_ratio"
    ]
    
    # Risk Management
    MAX_POSITION_SIZE: float = 0.1  # 10% of portfolio
    MAX_DAILY_LOSS: float = 0.02    # 2% daily loss limit
    MAX_LEVERAGE: float = 10.0      # Maximum leverage
    
    # Data Configuration
    TRAINING_DATA_DAYS: int = 365   # Days of historical data for training
    VALIDATION_SPLIT: float = 0.2   # 20% for validation
    TEST_SPLIT: float = 0.1         # 10% for testing
    
    # Distributed Computing
    RAY_HEAD_NODE: Optional[str] = None
    RAY_REDIS_PASSWORD: Optional[str] = None
    MAX_CONCURRENT_TRAINING: int = 4
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @validator("DEBUG", pre=True)
    def parse_debug(cls, v):
        """Parse debug flag from string."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("MODEL_STORAGE_PATH", pre=True)
    def create_model_storage_path(cls, v):
        """Ensure model storage path exists."""
        os.makedirs(v, exist_ok=True)
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
