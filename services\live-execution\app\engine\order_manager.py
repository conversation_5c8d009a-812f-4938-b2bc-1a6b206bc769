"""
Unified order management system with order routing and execution optimization.
"""

import logging
import asyncio
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from enum import Enum

from app.core.config import settings
from app.schemas.execution import (
    OrderRequest, OrderResponse, OrderStatus, OrderType, 
    ExecutionAlgorithm, AssetClass
)

logger = logging.getLogger(__name__)


class OrderState(str, Enum):
    """Order state enumeration."""
    NEW = "NEW"
    PENDING_RISK_CHECK = "PENDING_RISK_CHECK"
    RISK_APPROVED = "RISK_APPROVED"
    RISK_REJECTED = "RISK_REJECTED"
    ROUTING = "ROUTING"
    SUBMITTED = "SUBMITTED"
    WORKING = "WORKING"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


class OrderManager:
    """Unified order management system."""
    
    def __init__(self):
        """Initialize order manager."""
        self.active_orders = {}
        self.order_history = {}
        self.order_callbacks = []
        self.execution_gateway = None
        self.risk_manager = None
        self.execution_analytics = None
        
        # Order routing configuration
        self.routing_rules = {
            AssetClass.EQUITY: ["INTERACTIVE_BROKERS", "ALPACA"],
            AssetClass.CRYPTO: ["BINANCE", "COINBASE"],
            AssetClass.FOREX: ["INTERACTIVE_BROKERS"],
            AssetClass.FUTURES: ["INTERACTIVE_BROKERS"]
        }
        
        # Execution algorithm configurations
        self.algo_configs = {
            ExecutionAlgorithm.TWAP: {
                "default_duration_minutes": 30,
                "min_slice_size": 100,
                "max_participation_rate": 0.2
            },
            ExecutionAlgorithm.VWAP: {
                "default_duration_minutes": 60,
                "min_slice_size": 100,
                "max_participation_rate": 0.3
            },
            ExecutionAlgorithm.IMPLEMENTATION_SHORTFALL: {
                "default_duration_minutes": 45,
                "urgency_factor": 0.5,
                "risk_aversion": 0.3
            }
        }
    
    def set_dependencies(self, execution_gateway, risk_manager, execution_analytics):
        """Set dependencies for order manager."""
        self.execution_gateway = execution_gateway
        self.risk_manager = risk_manager
        self.execution_analytics = execution_analytics
    
    async def submit_order(self, order_request: OrderRequest) -> OrderResponse:
        """Submit order through order management workflow."""
        try:
            # Generate order ID
            order_id = str(uuid.uuid4())
            client_order_id = order_request.client_order_id or f"ATHENA_{order_id[:8]}"
            
            # Create order record
            order = {
                "id": uuid.UUID(order_id),
                "order_id": order_id,
                "client_order_id": client_order_id,
                "order_request": order_request,
                "state": OrderState.NEW,
                "status": OrderStatus.PENDING,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "filled_quantity": Decimal("0"),
                "remaining_quantity": order_request.quantity,
                "executions": [],
                "state_history": [
                    {"state": OrderState.NEW, "timestamp": datetime.utcnow()}
                ]
            }
            
            # Store order
            self.active_orders[client_order_id] = order
            
            # Start order workflow
            asyncio.create_task(self._process_order_workflow(client_order_id))
            
            # Create response
            return OrderResponse(
                id=order["id"],
                order_id=order_id,
                client_order_id=client_order_id,
                strategy_id=order_request.strategy_id,
                user_id=uuid.uuid4(),  # TODO: Get from context
                symbol=order_request.symbol,
                asset_class=order_request.asset_class,
                side=order_request.side,
                order_type=order_request.order_type,
                time_in_force=order_request.time_in_force,
                quantity=order_request.quantity,
                filled_quantity=Decimal("0"),
                remaining_quantity=order_request.quantity,
                price=order_request.price,
                status=OrderStatus.PENDING,
                broker="",  # Will be set after routing
                risk_check_status="PENDING",
                created_at=order["created_at"]
            )
            
        except Exception as e:
            logger.error(f"Order submission failed: {e}")
            raise
    
    async def _process_order_workflow(self, client_order_id: str):
        """Process order through complete workflow."""
        try:
            order = self.active_orders[client_order_id]
            
            # Risk check phase
            await self._transition_order_state(client_order_id, OrderState.PENDING_RISK_CHECK)
            
            if not order["order_request"].bypass_risk_checks and self.risk_manager:
                risk_result = await self.risk_manager.validate_order(order["order_request"])
                
                if risk_result["approved"]:
                    await self._transition_order_state(client_order_id, OrderState.RISK_APPROVED)
                    order["risk_check_result"] = risk_result
                else:
                    await self._transition_order_state(client_order_id, OrderState.RISK_REJECTED)
                    order["status"] = OrderStatus.REJECTED
                    order["rejection_reason"] = "; ".join(risk_result.get("rejections", []))
                    await self._notify_order_update(client_order_id)
                    return
            else:
                await self._transition_order_state(client_order_id, OrderState.RISK_APPROVED)
            
            # Order routing phase
            await self._transition_order_state(client_order_id, OrderState.ROUTING)
            
            # Select execution strategy
            execution_strategy = await self._select_execution_strategy(order)
            order["execution_strategy"] = execution_strategy
            
            # Route and execute order
            if execution_strategy["algorithm"] in [ExecutionAlgorithm.MARKET, ExecutionAlgorithm.LIMIT]:
                # Direct execution
                await self._execute_direct_order(client_order_id)
            else:
                # Algorithmic execution
                await self._execute_algorithmic_order(client_order_id)
            
        except Exception as e:
            logger.error(f"Order workflow failed for {client_order_id}: {e}")
            order = self.active_orders.get(client_order_id)
            if order:
                order["status"] = OrderStatus.REJECTED
                order["rejection_reason"] = f"Workflow error: {str(e)}"
                await self._notify_order_update(client_order_id)
    
    async def _select_execution_strategy(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Select optimal execution strategy."""
        order_request = order["order_request"]
        
        # Get execution recommendation if analytics available
        if self.execution_analytics:
            try:
                recommendation = await self.execution_analytics.recommend_execution_strategy(
                    order_request.symbol,
                    order_request.quantity,
                    urgency="NORMAL"  # TODO: Determine urgency from order
                )
                
                if "recommendations" in recommendation and recommendation["recommendations"]:
                    rec = recommendation["recommendations"][0]
                    return {
                        "algorithm": ExecutionAlgorithm(rec["strategy"]),
                        "parameters": rec,
                        "reason": rec.get("reason", "Analytics recommendation")
                    }
            except Exception as e:
                logger.warning(f"Failed to get execution recommendation: {e}")
        
        # Fallback to rule-based selection
        if order_request.execution_algo != ExecutionAlgorithm.MARKET:
            return {
                "algorithm": order_request.execution_algo,
                "parameters": order_request.algo_params or {},
                "reason": "User specified algorithm"
            }
        
        # Default algorithm selection
        order_value = order_request.quantity * (order_request.price or Decimal("100"))
        
        if order_value > settings.MAX_ORDER_SIZE * Decimal("0.1"):
            # Large order - use TWAP
            return {
                "algorithm": ExecutionAlgorithm.TWAP,
                "parameters": self.algo_configs[ExecutionAlgorithm.TWAP],
                "reason": "Large order size"
            }
        else:
            # Small order - use market
            return {
                "algorithm": ExecutionAlgorithm.MARKET,
                "parameters": {},
                "reason": "Small order size"
            }
    
    async def _execute_direct_order(self, client_order_id: str):
        """Execute order directly through broker."""
        try:
            order = self.active_orders[client_order_id]
            
            if not self.execution_gateway:
                raise ValueError("Execution gateway not available")
            
            # Submit to execution gateway
            response = await self.execution_gateway.submit_order(order["order_request"])
            
            # Update order with broker information
            order["broker"] = response.broker
            order["broker_order_id"] = response.broker_order_id
            order["status"] = response.status
            
            await self._transition_order_state(client_order_id, OrderState.SUBMITTED)
            await self._notify_order_update(client_order_id)
            
            # Monitor order status
            asyncio.create_task(self._monitor_order_status(client_order_id))
            
        except Exception as e:
            logger.error(f"Direct order execution failed: {e}")
            raise
    
    async def _execute_algorithmic_order(self, client_order_id: str):
        """Execute order using algorithmic strategy."""
        try:
            order = self.active_orders[client_order_id]
            strategy = order["execution_strategy"]
            
            if strategy["algorithm"] == ExecutionAlgorithm.TWAP:
                await self._execute_twap_order(client_order_id)
            elif strategy["algorithm"] == ExecutionAlgorithm.VWAP:
                await self._execute_vwap_order(client_order_id)
            elif strategy["algorithm"] == ExecutionAlgorithm.IMPLEMENTATION_SHORTFALL:
                await self._execute_is_order(client_order_id)
            else:
                # Fallback to direct execution
                await self._execute_direct_order(client_order_id)
                
        except Exception as e:
            logger.error(f"Algorithmic order execution failed: {e}")
            raise
    
    async def _execute_twap_order(self, client_order_id: str):
        """Execute TWAP (Time Weighted Average Price) order."""
        try:
            order = self.active_orders[client_order_id]
            order_request = order["order_request"]
            strategy = order["execution_strategy"]
            
            # TWAP parameters
            duration_minutes = strategy["parameters"].get("default_duration_minutes", 30)
            slice_count = min(duration_minutes, 20)  # Max 20 slices
            slice_size = order_request.quantity / slice_count
            slice_interval = (duration_minutes * 60) / slice_count  # seconds
            
            order["twap_config"] = {
                "duration_minutes": duration_minutes,
                "slice_count": slice_count,
                "slice_size": slice_size,
                "slice_interval": slice_interval,
                "slices_executed": 0
            }
            
            await self._transition_order_state(client_order_id, OrderState.WORKING)
            
            # Execute slices
            for slice_num in range(slice_count):
                if order["status"] in [OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                    break
                
                if order["remaining_quantity"] <= 0:
                    break
                
                # Calculate slice size (adjust for remaining quantity)
                current_slice_size = min(slice_size, order["remaining_quantity"])
                
                # Create slice order
                slice_request = OrderRequest(
                    strategy_id=order_request.strategy_id,
                    symbol=order_request.symbol,
                    asset_class=order_request.asset_class,
                    side=order_request.side,
                    order_type=OrderType.MARKET,  # TWAP uses market orders
                    quantity=current_slice_size,
                    execution_algo=ExecutionAlgorithm.MARKET,
                    preferred_broker=order_request.preferred_broker,
                    bypass_risk_checks=True  # Parent order already risk checked
                )
                
                # Submit slice
                if self.execution_gateway:
                    slice_response = await self.execution_gateway.submit_order(slice_request)
                    
                    # Track slice
                    order["executions"].append({
                        "slice_number": slice_num + 1,
                        "slice_order_id": slice_response.order_id,
                        "slice_size": current_slice_size,
                        "submitted_at": datetime.utcnow()
                    })
                
                order["twap_config"]["slices_executed"] += 1
                
                # Wait for next slice (except last one)
                if slice_num < slice_count - 1:
                    await asyncio.sleep(slice_interval)
            
            # Monitor completion
            asyncio.create_task(self._monitor_algorithmic_order(client_order_id))
            
        except Exception as e:
            logger.error(f"TWAP execution failed: {e}")
            raise
    
    async def _execute_vwap_order(self, client_order_id: str):
        """Execute VWAP (Volume Weighted Average Price) order."""
        # TODO: Implement VWAP execution logic
        logger.info(f"VWAP execution not yet implemented for {client_order_id}")
        await self._execute_direct_order(client_order_id)
    
    async def _execute_is_order(self, client_order_id: str):
        """Execute Implementation Shortfall order."""
        # TODO: Implement Implementation Shortfall execution logic
        logger.info(f"Implementation Shortfall execution not yet implemented for {client_order_id}")
        await self._execute_direct_order(client_order_id)
    
    async def _monitor_order_status(self, client_order_id: str):
        """Monitor order status updates."""
        try:
            order = self.active_orders[client_order_id]
            
            while order["status"] in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL]:
                await asyncio.sleep(5)  # Check every 5 seconds
                
                # Get status from execution gateway
                if self.execution_gateway and order.get("broker_order_id"):
                    status_result = await self.execution_gateway.get_order_status(client_order_id)
                    
                    if status_result.get("success"):
                        # Update order status
                        old_status = order["status"]
                        order["status"] = OrderStatus(status_result["status"])
                        order["filled_quantity"] = Decimal(str(status_result.get("filled_quantity", 0)))
                        order["remaining_quantity"] = order["order_request"].quantity - order["filled_quantity"]
                        
                        if order["status"] != old_status:
                            await self._notify_order_update(client_order_id)
                        
                        # Check if order is complete
                        if order["status"] in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                            break
                
        except Exception as e:
            logger.error(f"Order status monitoring failed: {e}")
    
    async def _monitor_algorithmic_order(self, client_order_id: str):
        """Monitor algorithmic order completion."""
        try:
            order = self.active_orders[client_order_id]
            
            # Wait for all slices to complete
            while order["status"] == OrderStatus.WORKING:
                await asyncio.sleep(10)  # Check every 10 seconds
                
                # Check slice completion
                completed_slices = 0
                total_filled = Decimal("0")
                
                for execution in order["executions"]:
                    # TODO: Check slice order status
                    completed_slices += 1
                    # TODO: Add filled quantity from slice
                
                # Update order status
                order["filled_quantity"] = total_filled
                order["remaining_quantity"] = order["order_request"].quantity - total_filled
                
                if order["remaining_quantity"] <= 0:
                    order["status"] = OrderStatus.FILLED
                    await self._transition_order_state(client_order_id, OrderState.FILLED)
                    break
                
                # Check if all slices are submitted
                twap_config = order.get("twap_config", {})
                if completed_slices >= twap_config.get("slice_count", 0):
                    if order["remaining_quantity"] > 0:
                        order["status"] = OrderStatus.PARTIAL
                    else:
                        order["status"] = OrderStatus.FILLED
                        await self._transition_order_state(client_order_id, OrderState.FILLED)
                    break
            
            await self._notify_order_update(client_order_id)
            
        except Exception as e:
            logger.error(f"Algorithmic order monitoring failed: {e}")
    
    async def _transition_order_state(self, client_order_id: str, new_state: OrderState):
        """Transition order to new state."""
        order = self.active_orders[client_order_id]
        old_state = order["state"]
        order["state"] = new_state
        order["updated_at"] = datetime.utcnow()
        
        # Add to state history
        order["state_history"].append({
            "state": new_state,
            "timestamp": datetime.utcnow(),
            "previous_state": old_state
        })
        
        logger.info(f"Order {client_order_id} transitioned from {old_state} to {new_state}")
    
    async def _notify_order_update(self, client_order_id: str):
        """Notify callbacks of order update."""
        order = self.active_orders[client_order_id]
        
        for callback in self.order_callbacks:
            try:
                await callback(order)
            except Exception as e:
                logger.error(f"Order callback failed: {e}")
    
    async def cancel_order(self, client_order_id: str) -> bool:
        """Cancel order."""
        try:
            if client_order_id not in self.active_orders:
                return False
            
            order = self.active_orders[client_order_id]
            
            # Cancel at execution gateway
            if self.execution_gateway and order.get("broker_order_id"):
                result = await self.execution_gateway.cancel_order(client_order_id)
                
                if result.get("success"):
                    order["status"] = OrderStatus.CANCELLED
                    await self._transition_order_state(client_order_id, OrderState.CANCELLED)
                    await self._notify_order_update(client_order_id)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Order cancellation failed: {e}")
            return False
    
    async def get_order_status(self, client_order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status."""
        return self.active_orders.get(client_order_id)
    
    async def list_active_orders(self, strategy_id: Optional[uuid.UUID] = None) -> List[Dict[str, Any]]:
        """List active orders."""
        orders = []
        
        for order in self.active_orders.values():
            if strategy_id and order["order_request"].strategy_id != strategy_id:
                continue
            
            if order["status"] not in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                orders.append(order)
        
        return orders
    
    def add_order_callback(self, callback):
        """Add order update callback."""
        self.order_callbacks.append(callback)
    
    async def cleanup(self):
        """Cleanup order manager."""
        # Cancel all active orders
        for client_order_id in list(self.active_orders.keys()):
            await self.cancel_order(client_order_id)
        
        logger.info("Order manager cleaned up")
