"""
Backtests router for backtest management and execution.
"""

import logging
from typing import List, Optional, Dict, Any
import uuid
import asyncio
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload
import httpx
import pandas as pd

from app.core.database import get_db
from app.core.config import settings
from app.models.backtest import Backtest, BacktestTrade, BacktestAnalytics
from app.schemas.backtest import (
    Backtest as BacktestSchema,
    BacktestCreate,
    BacktestUpdate,
    BacktestTrade as BacktestTradeSchema,
    BacktestAnalytics as BacktestAnalyticsSchema,
    BacktestStatus,
    BacktestProgress
)

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


async def fetch_market_data(
    instrument_ids: List[uuid.UUID],
    start_date: datetime,
    end_date: datetime,
    timeframe: str = "1h"
) -> pd.DataFrame:
    """Fetch market data from Data Nexus service."""
    try:
        async with httpx.AsyncClient() as client:
            # For now, fetch data for the first instrument
            # TODO: Implement multi-instrument backtesting
            instrument_id = instrument_ids[0]
            
            response = await client.get(
                f"{settings.DATA_NEXUS_URL}/market-data/",
                params={
                    "instrument_id": str(instrument_id),
                    "timeframe": timeframe,
                    "start_time": start_date.isoformat(),
                    "end_time": end_date.isoformat(),
                    "limit": 50000  # Large limit for backtesting
                }
            )
            response.raise_for_status()
            data = response.json()
            
            if not data:
                raise ValueError("No market data available for the specified period")
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            df['time'] = pd.to_datetime(df['time'])
            df = df.sort_values('time').set_index('time')
            
            # Ensure required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            return df
            
    except Exception as e:
        logger.error(f"Failed to fetch market data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch market data: {str(e)}"
        )


async def run_backtest_simulation(
    backtest_id: uuid.UUID,
    backtest_config: BacktestCreate,
    market_data: pd.DataFrame,
    db: AsyncSession
):
    """Run backtest simulation in background."""
    from app.engine.simulation import SimulationEngine
    from app.engine.performance import PerformanceAnalytics
    from app.engine.strategy_integration import StrategyIntegrator
    
    try:
        # Update backtest status
        stmt = select(Backtest).where(Backtest.id == backtest_id)
        result = await db.execute(stmt)
        backtest = result.scalar_one()
        
        backtest.status = BacktestStatus.RUNNING
        backtest.started_at = datetime.utcnow()
        backtest.bars_processed = 0
        await db.commit()
        
        # Initialize engines
        simulation_engine = SimulationEngine()
        performance_analytics = PerformanceAnalytics()
        strategy_integrator = StrategyIntegrator(backtest_config.config.model_dump())
        
        # Initialize simulation
        simulation_engine.initialize(
            backtest_config.config,
            float(backtest_config.initial_capital),
            market_data
        )
        
        # Track simulation progress
        total_bars = len(market_data)
        trades_executed = []
        equity_curve = []
        
        # Progress tracking
        progress_update_interval = max(1, total_bars // 100)  # Update every 1%
        
        # Simulation loop
        for bar_index in range(len(market_data)):
            try:
                current_bar = market_data.iloc[bar_index]
                
                # Get market state
                market_state = simulation_engine.get_current_market_state("EURUSD")  # TODO: Dynamic symbol
                
                # Prepare market data for strategy
                market_data_dict = {
                    'symbol': 'EURUSD',
                    'time': current_bar.name.isoformat(),
                    'open': float(current_bar['open']),
                    'high': float(current_bar['high']),
                    'low': float(current_bar['low']),
                    'close': float(current_bar['close']),
                    'volume': float(current_bar['volume']),
                    'volatility': market_state.volatility,
                    'liquidity_score': market_state.liquidity_score
                }
                
                # Get portfolio state
                portfolio_snapshot = simulation_engine.get_portfolio_snapshot({'EURUSD': market_state.price})
                
                # Generate strategy signal
                try:
                    signal_result = await strategy_integrator.process_strategy_signal(
                        backtest_config.strategy_id,
                        backtest_config.user_id,
                        market_data_dict,
                        portfolio_snapshot,
                        {'performance_score': 0.5}  # TODO: Get actual strategy metadata
                    )
                    
                    # Execute trade if signal is valid
                    if signal_result['risk_approved'] and abs(signal_result['position_size']) > 0:
                        execution_result = simulation_engine.process_signal(
                            'EURUSD',
                            signal_result['position_size'],
                            signal_result['processed_signal']['confidence']
                        )
                        
                        if execution_result:
                            # Record trade
                            trade_data = {
                                'backtest_id': backtest_id,
                                'trade_id': f"trade_{len(trades_executed) + 1}",
                                'instrument_id': backtest_config.instruments[0],
                                'symbol': 'EURUSD',
                                'side': 'BUY' if execution_result.filled_quantity > 0 else 'SELL',
                                'quantity': abs(execution_result.filled_quantity),
                                'price': execution_result.fill_price,
                                'timestamp': execution_result.timestamp,
                                'signal_strength': signal_result['processed_signal']['confidence'],
                                'intended_quantity': signal_result['position_size'],
                                'fill_type': execution_result.fill_type.value,
                                'commission': execution_result.commission,
                                'spread_cost': execution_result.spread_cost,
                                'slippage_cost': execution_result.slippage_cost,
                                'market_impact_cost': execution_result.market_impact_cost,
                                'total_cost': execution_result.total_cost,
                                'position_before': portfolio_snapshot.get('positions', {}).get('EURUSD', 0),
                                'position_after': simulation_engine.portfolio_manager.get_position('EURUSD'),
                                'portfolio_value_before': portfolio_snapshot['total_value'],
                                'portfolio_value_after': simulation_engine.portfolio_manager.calculate_portfolio_value({'EURUSD': market_state.price}),
                                'market_price': market_state.price,
                                'volatility': market_state.volatility
                            }
                            
                            trades_executed.append(trade_data)
                
                except Exception as e:
                    logger.warning(f"Strategy signal generation failed at bar {bar_index}: {e}")
                
                # Record equity curve
                current_portfolio_value = simulation_engine.portfolio_manager.calculate_portfolio_value({'EURUSD': market_state.price})
                equity_curve.append({
                    'timestamp': current_bar.name,
                    'portfolio_value': current_portfolio_value
                })
                
                # Update progress
                if bar_index % progress_update_interval == 0:
                    progress_pct = (bar_index / total_bars) * 100
                    backtest.progress_pct = progress_pct
                    backtest.bars_processed = bar_index
                    
                    # Estimate completion time
                    if bar_index > 0:
                        elapsed = (datetime.utcnow() - backtest.started_at).total_seconds()
                        estimated_total = elapsed * total_bars / bar_index
                        backtest.estimated_completion = backtest.started_at + timedelta(seconds=estimated_total)
                    
                    await db.commit()
                
                # Advance simulation
                if not simulation_engine.advance_time():
                    break
                    
            except Exception as e:
                logger.error(f"Error processing bar {bar_index}: {e}")
                continue
        
        # Calculate final performance metrics
        equity_df = pd.DataFrame(equity_curve)
        equity_df.set_index('timestamp', inplace=True)
        equity_series = equity_df['portfolio_value']
        
        performance_metrics = performance_analytics.calculate_comprehensive_metrics(
            equity_series,
            trades_executed,
            initial_capital=float(backtest_config.initial_capital)
        )
        
        # Update backtest with results
        backtest.status = BacktestStatus.COMPLETED
        backtest.completed_at = datetime.utcnow()
        backtest.execution_time_seconds = int((backtest.completed_at - backtest.started_at).total_seconds())
        backtest.bars_processed = total_bars
        backtest.progress_pct = 100.0
        
        # Store performance metrics
        backtest.total_return = performance_metrics.total_return
        backtest.annualized_return = performance_metrics.annualized_return
        backtest.volatility = performance_metrics.volatility
        backtest.sharpe_ratio = performance_metrics.sharpe_ratio
        backtest.sortino_ratio = performance_metrics.sortino_ratio
        backtest.calmar_ratio = performance_metrics.calmar_ratio
        backtest.max_drawdown = performance_metrics.max_drawdown
        backtest.max_drawdown_duration_days = performance_metrics.max_drawdown_duration_days
        
        backtest.total_trades = performance_metrics.total_trades
        backtest.winning_trades = performance_metrics.winning_trades
        backtest.losing_trades = performance_metrics.losing_trades
        backtest.win_rate = performance_metrics.win_rate
        backtest.profit_factor = performance_metrics.profit_factor
        backtest.avg_trade_return = performance_metrics.avg_trade_return
        backtest.avg_trade_duration_hours = performance_metrics.avg_trade_duration_hours
        
        backtest.var_95 = performance_metrics.var_95
        backtest.cvar_95 = performance_metrics.cvar_95
        backtest.beta = performance_metrics.beta
        backtest.alpha = performance_metrics.alpha
        backtest.information_ratio = performance_metrics.information_ratio
        backtest.treynor_ratio = performance_metrics.treynor_ratio
        
        # Store detailed results
        backtest.performance_metrics = performance_metrics.model_dump()
        backtest.equity_curve = equity_curve
        
        await db.commit()
        
        # Store individual trades
        for trade_data in trades_executed:
            trade = BacktestTrade(**trade_data)
            db.add(trade)
        
        await db.commit()
        
        logger.info(f"Backtest {backtest_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Backtest {backtest_id} failed: {e}")
        
        # Update backtest with error
        backtest.status = BacktestStatus.FAILED
        backtest.completed_at = datetime.utcnow()
        backtest.error_message = str(e)
        await db.commit()


@router.get("/", response_model=List[BacktestSchema])
async def list_backtests(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    status_filter: Optional[BacktestStatus] = Query(None, description="Filter by status"),
    strategy_id: Optional[uuid.UUID] = Query(None, description="Filter by strategy ID"),
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """List backtests for the current user."""
    # Build query conditions
    conditions = [Backtest.user_id == user_id]
    
    if status_filter:
        conditions.append(Backtest.status == status_filter.value)
    
    if strategy_id:
        conditions.append(Backtest.strategy_id == strategy_id)
    
    # Build and execute query
    stmt = (
        select(Backtest)
        .where(and_(*conditions))
        .order_by(desc(Backtest.created_at))
        .offset(skip)
        .limit(limit)
    )
    
    result = await db.execute(stmt)
    backtests = result.scalars().all()
    
    return [BacktestSchema.from_attributes(backtest) for backtest in backtests]


@router.get("/{backtest_id}", response_model=BacktestSchema)
async def get_backtest(
    backtest_id: uuid.UUID,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific backtest by ID."""
    stmt = select(Backtest).where(
        and_(Backtest.id == backtest_id, Backtest.user_id == user_id)
    )
    result = await db.execute(stmt)
    backtest = result.scalar_one_or_none()
    
    if not backtest:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest not found"
        )
    
    return BacktestSchema.from_attributes(backtest)


@router.post("/", response_model=BacktestSchema)
async def create_backtest(
    backtest_data: BacktestCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Create and start a new backtest."""
    # Validate date range
    if (backtest_data.end_date - backtest_data.start_date).days < settings.MIN_BACKTEST_DAYS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Backtest period must be at least {settings.MIN_BACKTEST_DAYS} days"
        )
    
    if (backtest_data.end_date - backtest_data.start_date).days > settings.MAX_BACKTEST_DAYS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Backtest period cannot exceed {settings.MAX_BACKTEST_DAYS} days"
        )
    
    # Create backtest record
    backtest = Backtest(
        user_id=backtest_data.user_id,
        strategy_id=backtest_data.strategy_id,
        name=backtest_data.name,
        description=backtest_data.description,
        config=backtest_data.config.model_dump(),
        start_date=backtest_data.start_date,
        end_date=backtest_data.end_date,
        timeframe=backtest_data.timeframe,
        instruments=backtest_data.instruments,
        benchmark_symbol=backtest_data.benchmark_symbol,
        initial_capital=backtest_data.initial_capital,
        commission_bps=backtest_data.config.commission_bps,
        spread_bps=backtest_data.config.spread_bps,
        slippage_bps=backtest_data.config.slippage_bps,
        market_impact_factor=backtest_data.config.market_impact_factor,
        max_position_size=backtest_data.config.max_position_size,
        stop_loss_pct=backtest_data.config.stop_loss_pct,
        take_profit_pct=backtest_data.config.take_profit_pct,
        max_leverage=backtest_data.config.max_leverage,
        status=BacktestStatus.PENDING
    )
    
    db.add(backtest)
    await db.commit()
    await db.refresh(backtest)
    
    try:
        # Fetch market data
        market_data = await fetch_market_data(
            backtest_data.instruments,
            backtest_data.start_date,
            backtest_data.end_date,
            backtest_data.timeframe
        )
        
        # Start backtest in background
        background_tasks.add_task(
            run_backtest_simulation,
            backtest.id,
            backtest_data,
            market_data,
            db
        )
        
        logger.info(f"Started backtest {backtest.id}")
        
        return BacktestSchema.from_attributes(backtest)
        
    except Exception as e:
        # Update backtest with error
        backtest.status = BacktestStatus.FAILED
        backtest.error_message = str(e)
        await db.commit()
        
        logger.error(f"Failed to start backtest: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start backtest: {str(e)}"
        )
