"""
Pydantic schemas for XAI operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class ExplanationType(str, Enum):
    """Explanation type enumeration."""
    SHAP = "SHAP"
    LIME = "LIME"
    FEATURE_IMPORTANCE = "FEATURE_IMPORTANCE"
    PERMUTATION_IMPORTANCE = "PERMUTATION_IMPORTANCE"
    GRADIENT_ANALYSIS = "GRADIENT_ANALYSIS"
    ATTENTION_ANALYSIS = "ATTENTION_ANALYSIS"
    COUNTERFACTUAL = "COUNTERFACTUAL"
    WHAT_IF = "WHAT_IF"


class AIParadigm(str, Enum):
    """AI paradigm enumeration."""
    GP = "GP"  # Genetic Programming
    RL = "RL"  # Reinforcement Learning
    DL = "DL"  # Deep Learning
    HYBRID = "HYBRID"


class ExplanationScope(str, Enum):
    """Explanation scope enumeration."""
    GLOBAL = "GLOBAL"  # Model-level explanation
    LOCAL = "LOCAL"    # Instance-level explanation
    COHORT = "COHORT"  # Group-level explanation


class AttributionType(str, Enum):
    """Attribution type enumeration."""
    FACTOR = "FACTOR"
    TRADE = "TRADE"
    TEMPORAL = "TEMPORAL"
    REGIME = "REGIME"
    SIGNAL = "SIGNAL"


class ComplianceStatus(str, Enum):
    """Compliance status enumeration."""
    COMPLIANT = "COMPLIANT"
    NON_COMPLIANT = "NON_COMPLIANT"
    UNDER_REVIEW = "UNDER_REVIEW"
    PENDING = "PENDING"


class ExplanationRequest(BaseModel):
    """Request for strategy explanation."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID to explain")
    explanation_type: ExplanationType = Field(..., description="Type of explanation")
    explanation_scope: ExplanationScope = Field(default=ExplanationScope.GLOBAL, description="Scope of explanation")
    
    # Data configuration
    data_period_start: Optional[datetime] = Field(None, description="Start date for data")
    data_period_end: Optional[datetime] = Field(None, description="End date for data")
    sample_size: Optional[int] = Field(None, ge=10, le=10000, description="Sample size for explanation")
    
    # Method-specific configuration
    shap_config: Optional[Dict[str, Any]] = Field(None, description="SHAP-specific configuration")
    lime_config: Optional[Dict[str, Any]] = Field(None, description="LIME-specific configuration")
    feature_config: Optional[Dict[str, Any]] = Field(None, description="Feature analysis configuration")
    
    # Target specification
    target_variable: Optional[str] = Field(None, description="Target variable to explain")
    target_instance: Optional[Dict[str, Any]] = Field(None, description="Specific instance to explain")
    
    # Output configuration
    include_visualizations: bool = Field(default=True, description="Include visualization data")
    include_interactions: bool = Field(default=False, description="Include feature interactions")
    max_features: Optional[int] = Field(None, ge=1, le=100, description="Maximum features to analyze")
    
    # Compliance
    regulatory_framework: Optional[str] = Field(None, description="Regulatory framework")
    privacy_level: str = Field(default="INTERNAL", description="Privacy level")


class FeatureImportanceResult(BaseModel):
    """Feature importance analysis result."""
    
    feature_name: str = Field(..., description="Feature name")
    importance_score: Decimal = Field(..., description="Importance score")
    importance_rank: Optional[int] = Field(None, description="Rank among features")
    importance_percentile: Optional[Decimal] = Field(None, ge=0, le=1, description="Percentile ranking")
    
    # Method-specific scores
    permutation_importance: Optional[Decimal] = Field(None, description="Permutation importance")
    shap_importance: Optional[Decimal] = Field(None, description="SHAP importance")
    lime_importance: Optional[Decimal] = Field(None, description="LIME importance")
    
    # Statistical significance
    p_value: Optional[Decimal] = Field(None, ge=0, le=1, description="P-value")
    confidence_interval: Optional[List[Decimal]] = Field(None, description="Confidence interval")
    
    # Additional metadata
    feature_type: Optional[str] = Field(None, description="Feature type")
    feature_category: Optional[str] = Field(None, description="Feature category")
    correlation_with_target: Optional[Decimal] = Field(None, ge=-1, le=1, description="Correlation with target")


class SHAPExplanation(BaseModel):
    """SHAP explanation result."""
    
    base_value: Decimal = Field(..., description="Base value (expected model output)")
    shap_values: List[Decimal] = Field(..., description="SHAP values for each feature")
    feature_names: List[str] = Field(..., description="Feature names")
    feature_values: List[Union[str, Decimal]] = Field(..., description="Feature values")
    
    # Interaction effects
    interaction_values: Optional[List[List[Decimal]]] = Field(None, description="SHAP interaction values")
    
    # Explanation metadata
    expected_value: Decimal = Field(..., description="Expected model output")
    prediction_value: Decimal = Field(..., description="Actual prediction")
    explanation_quality: Optional[Decimal] = Field(None, ge=0, le=1, description="Explanation quality score")


class LIMEExplanation(BaseModel):
    """LIME explanation result."""
    
    feature_weights: List[Tuple[str, Decimal]] = Field(..., description="Feature weights")
    intercept: Decimal = Field(..., description="Local model intercept")
    prediction_proba: Optional[List[Decimal]] = Field(None, description="Prediction probabilities")
    
    # Local model information
    local_model_r2: Optional[Decimal] = Field(None, ge=0, le=1, description="Local model R²")
    num_features: int = Field(..., ge=1, description="Number of features in explanation")
    kernel_width: Optional[Decimal] = Field(None, gt=0, description="Kernel width used")


class AttributionResult(BaseModel):
    """Performance attribution result."""
    
    factor_name: str = Field(..., description="Attribution factor name")
    attribution_value: Decimal = Field(..., description="Attribution value")
    attribution_percentage: Optional[Decimal] = Field(None, description="Percentage of total return")
    
    # Time period
    period_start: datetime = Field(..., description="Attribution period start")
    period_end: datetime = Field(..., description="Attribution period end")
    
    # Statistical significance
    t_statistic: Optional[Decimal] = Field(None, description="T-statistic")
    p_value: Optional[Decimal] = Field(None, ge=0, le=1, description="P-value")
    confidence_level: Optional[Decimal] = Field(None, gt=0, lt=1, description="Confidence level")
    
    # Risk attribution
    risk_contribution: Optional[Decimal] = Field(None, description="Risk contribution")
    volatility_contribution: Optional[Decimal] = Field(None, description="Volatility contribution")
    
    # Context
    attribution_type: AttributionType = Field(..., description="Attribution type")
    market_regime: Optional[str] = Field(None, description="Market regime")


class ExplanationResponse(BaseModel):
    """Response containing explanation results."""
    
    id: uuid.UUID = Field(..., description="Explanation ID")
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    explanation_type: ExplanationType = Field(..., description="Explanation type")
    explanation_scope: ExplanationScope = Field(..., description="Explanation scope")
    
    # Results
    feature_importance: Optional[List[FeatureImportanceResult]] = Field(None, description="Feature importance results")
    shap_explanation: Optional[SHAPExplanation] = Field(None, description="SHAP explanation")
    lime_explanation: Optional[LIMEExplanation] = Field(None, description="LIME explanation")
    attribution_results: Optional[List[AttributionResult]] = Field(None, description="Attribution results")
    
    # Quality metrics
    explanation_quality_score: Optional[Decimal] = Field(None, ge=0, le=1, description="Overall quality score")
    consistency_score: Optional[Decimal] = Field(None, ge=0, le=1, description="Consistency score")
    fidelity_score: Optional[Decimal] = Field(None, ge=0, le=1, description="Fidelity score")
    
    # Visualization data
    visualization_data: Optional[Dict[str, Any]] = Field(None, description="Visualization data")
    chart_configs: Optional[Dict[str, Any]] = Field(None, description="Chart configurations")
    
    # Metadata
    ai_paradigm: AIParadigm = Field(..., description="AI paradigm")
    model_type: Optional[str] = Field(None, description="Model type")
    computation_time_seconds: Optional[Decimal] = Field(None, description="Computation time")
    
    # Status and compliance
    status: str = Field(..., description="Explanation status")
    compliance_status: Optional[ComplianceStatus] = Field(None, description="Compliance status")
    
    # Timestamps
    created_at: datetime = Field(..., description="Creation timestamp")
    expires_at: Optional[datetime] = Field(None, description="Expiration timestamp")
    
    class Config:
        from_attributes = True


class AttributionRequest(BaseModel):
    """Request for performance attribution analysis."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    attribution_type: AttributionType = Field(..., description="Attribution type")
    
    # Time period
    period_start: datetime = Field(..., description="Analysis period start")
    period_end: datetime = Field(..., description="Analysis period end")
    
    # Configuration
    attribution_method: str = Field(default="FACTOR_MODEL", description="Attribution method")
    factor_models: Optional[List[str]] = Field(None, description="Factor models to use")
    benchmark_id: Optional[uuid.UUID] = Field(None, description="Benchmark strategy ID")
    
    # Analysis options
    include_interactions: bool = Field(default=False, description="Include factor interactions")
    include_regime_analysis: bool = Field(default=True, description="Include regime analysis")
    confidence_level: Decimal = Field(default=Decimal("0.95"), gt=0, lt=1, description="Confidence level")


class InterpretabilityRequest(BaseModel):
    """Request for model interpretability analysis."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    analysis_type: str = Field(..., description="Analysis type")
    
    # Model-specific options
    include_attention: bool = Field(default=True, description="Include attention analysis")
    include_gradients: bool = Field(default=True, description="Include gradient analysis")
    include_activations: bool = Field(default=False, description="Include activation analysis")
    
    # Tree-based options (for GP)
    max_tree_depth: Optional[int] = Field(None, ge=1, le=20, description="Maximum tree depth to analyze")
    include_node_importance: bool = Field(default=True, description="Include node importance")
    
    # RL-specific options
    include_action_values: bool = Field(default=True, description="Include action value analysis")
    include_policy_analysis: bool = Field(default=True, description="Include policy analysis")


class CounterfactualRequest(BaseModel):
    """Request for counterfactual explanation."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    original_instance: Dict[str, Any] = Field(..., description="Original instance data")
    
    # Counterfactual configuration
    desired_outcome: Optional[Union[str, Decimal]] = Field(None, description="Desired outcome")
    features_to_vary: Optional[List[str]] = Field(None, description="Features allowed to vary")
    max_changes: Optional[int] = Field(None, ge=1, le=10, description="Maximum feature changes")
    
    # Constraints
    feature_ranges: Optional[Dict[str, List[Union[str, Decimal]]]] = Field(None, description="Feature value ranges")
    immutable_features: Optional[List[str]] = Field(None, description="Features that cannot change")


class WhatIfRequest(BaseModel):
    """Request for what-if analysis."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    base_scenario: Dict[str, Any] = Field(..., description="Base scenario data")
    
    # What-if scenarios
    scenarios: List[Dict[str, Any]] = Field(..., min_items=1, max_items=10, description="What-if scenarios")
    scenario_names: Optional[List[str]] = Field(None, description="Scenario names")
    
    # Analysis options
    include_confidence_intervals: bool = Field(default=True, description="Include confidence intervals")
    include_sensitivity_analysis: bool = Field(default=False, description="Include sensitivity analysis")


class AuditTrailEntry(BaseModel):
    """Audit trail entry."""
    
    id: uuid.UUID = Field(..., description="Audit entry ID")
    user_id: uuid.UUID = Field(..., description="User ID")
    action_type: str = Field(..., description="Action type")
    action_category: str = Field(..., description="Action category")
    
    # Target information
    strategy_id: Optional[uuid.UUID] = Field(None, description="Strategy ID")
    explanation_id: Optional[uuid.UUID] = Field(None, description="Explanation ID")
    
    # Request/response
    request_parameters: Optional[Dict[str, Any]] = Field(None, description="Request parameters")
    response_status: str = Field(..., description="Response status")
    execution_time_ms: Optional[int] = Field(None, description="Execution time in milliseconds")
    
    # Compliance
    regulatory_framework: Optional[str] = Field(None, description="Regulatory framework")
    compliance_status: Optional[ComplianceStatus] = Field(None, description="Compliance status")
    privacy_level: Optional[str] = Field(None, description="Privacy level")
    
    # Timestamps
    timestamp: datetime = Field(..., description="Action timestamp")
    retention_until: Optional[datetime] = Field(None, description="Retention until")
    
    class Config:
        from_attributes = True


class BiasDetectionResult(BaseModel):
    """Bias detection analysis result."""
    
    bias_type: str = Field(..., description="Type of bias detected")
    bias_score: Decimal = Field(..., ge=0, le=1, description="Bias score")
    affected_groups: List[str] = Field(..., description="Affected groups")
    
    # Statistical measures
    statistical_parity: Optional[Decimal] = Field(None, description="Statistical parity measure")
    equalized_odds: Optional[Decimal] = Field(None, description="Equalized odds measure")
    demographic_parity: Optional[Decimal] = Field(None, description="Demographic parity measure")
    
    # Recommendations
    mitigation_suggestions: List[str] = Field(..., description="Bias mitigation suggestions")
    severity_level: str = Field(..., description="Severity level")


class ModelValidationReport(BaseModel):
    """Model validation report."""
    
    strategy_id: uuid.UUID = Field(..., description="Strategy ID")
    validation_type: str = Field(..., description="Validation type")
    
    # Validation results
    validation_score: Decimal = Field(..., ge=0, le=1, description="Overall validation score")
    statistical_tests: Dict[str, Any] = Field(..., description="Statistical test results")
    
    # Performance metrics
    accuracy_metrics: Dict[str, Decimal] = Field(..., description="Accuracy metrics")
    stability_metrics: Dict[str, Decimal] = Field(..., description="Stability metrics")
    robustness_metrics: Dict[str, Decimal] = Field(..., description="Robustness metrics")
    
    # Bias analysis
    bias_analysis: Optional[List[BiasDetectionResult]] = Field(None, description="Bias analysis results")
    
    # Recommendations
    validation_status: str = Field(..., description="Validation status")
    recommendations: List[str] = Field(..., description="Validation recommendations")
    
    # Compliance
    regulatory_compliance: Dict[str, ComplianceStatus] = Field(..., description="Regulatory compliance status")
    
    # Timestamps
    validation_date: datetime = Field(..., description="Validation date")
    next_validation_due: Optional[datetime] = Field(None, description="Next validation due date")


class DashboardConfig(BaseModel):
    """Dashboard configuration."""
    
    dashboard_type: str = Field(..., description="Dashboard type")
    strategy_ids: List[uuid.UUID] = Field(..., min_items=1, description="Strategy IDs to include")
    
    # Time configuration
    time_range: str = Field(default="30d", description="Time range")
    refresh_interval: int = Field(default=30, ge=5, le=300, description="Refresh interval in seconds")
    
    # Visualization options
    chart_types: List[str] = Field(..., description="Chart types to include")
    include_real_time: bool = Field(default=True, description="Include real-time updates")
    max_data_points: int = Field(default=1000, ge=100, le=10000, description="Maximum data points")
    
    # Filters
    explanation_types: Optional[List[ExplanationType]] = Field(None, description="Explanation types to show")
    attribution_types: Optional[List[AttributionType]] = Field(None, description="Attribution types to show")
    
    # Export options
    enable_export: bool = Field(default=True, description="Enable data export")
    export_formats: List[str] = Field(default=["PNG", "PDF"], description="Export formats")
