"""
Rate limiting middleware for API protection.
"""

import time
import logging
from typing import Dict, Optional
from collections import defaultdict, deque

from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app, requests_per_minute: int = None, window_size: int = None):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute or settings.RATE_LIMIT_REQUESTS
        self.window_size = window_size or settings.RATE_LIMIT_WINDOW
        
        # In-memory storage for request counts (use Redis in production)
        self.request_counts: Dict[str, deque] = defaultdict(deque)
    
    async def dispatch(self, request: Request, call_next):
        """Process request through rate limiting middleware."""
        
        # Get client identifier (IP address or user ID if authenticated)
        client_id = self._get_client_id(request)
        
        # Check rate limit
        if not self._is_allowed(client_id):
            return self._rate_limit_exceeded_response()
        
        # Record the request
        self._record_request(client_id)
        
        # Continue with the request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = self._get_remaining_requests(client_id)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.window_size)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Use user ID if authenticated, otherwise use IP address
        if hasattr(request.state, "user_id") and request.state.user_id:
            return f"user:{request.state.user_id}"
        
        # Get IP address from headers (considering proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return f"ip:{forwarded_for.split(',')[0].strip()}"
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return f"ip:{real_ip}"
        
        return f"ip:{request.client.host}"
    
    def _is_allowed(self, client_id: str) -> bool:
        """Check if request is allowed based on rate limit."""
        current_time = time.time()
        window_start = current_time - self.window_size
        
        # Get request timestamps for this client
        timestamps = self.request_counts[client_id]
        
        # Remove old timestamps outside the window
        while timestamps and timestamps[0] < window_start:
            timestamps.popleft()
        
        # Check if under the limit
        return len(timestamps) < self.requests_per_minute
    
    def _record_request(self, client_id: str) -> None:
        """Record a request timestamp."""
        current_time = time.time()
        self.request_counts[client_id].append(current_time)
    
    def _get_remaining_requests(self, client_id: str) -> int:
        """Get remaining requests for the client."""
        current_time = time.time()
        window_start = current_time - self.window_size
        
        # Get request timestamps for this client
        timestamps = self.request_counts[client_id]
        
        # Remove old timestamps outside the window
        while timestamps and timestamps[0] < window_start:
            timestamps.popleft()
        
        return max(0, self.requests_per_minute - len(timestamps))
    
    def _rate_limit_exceeded_response(self) -> Response:
        """Return rate limit exceeded response."""
        return Response(
            content='{"error": {"code": 429, "message": "Rate limit exceeded"}}',
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            headers={
                "X-RateLimit-Limit": str(self.requests_per_minute),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(time.time()) + self.window_size),
                "Retry-After": str(self.window_size)
            },
            media_type="application/json"
        )
