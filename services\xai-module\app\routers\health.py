"""
Health check router for XAI Module service.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import psutil
import os

from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "service": "AthenaTrader XAI Module",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "capabilities": [
            "strategy_explainability",
            "shap_explanations",
            "lime_explanations",
            "feature_importance_analysis",
            "performance_attribution",
            "model_interpretability",
            "audit_trail_management",
            "regulatory_compliance",
            "interactive_dashboards",
            "real_time_explanations",
            "bias_detection",
            "counterfactual_explanations"
        ]
    }


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """Detailed health check with system and dependency status."""
    health_status = {
        "status": "healthy",
        "service": "AthenaTrader XAI Module",
        "version": "0.1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # Database connectivity check
    try:
        await db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Check for resource warnings
        warnings = []
        if cpu_percent > 90:
            warnings.append("High CPU usage")
        if memory.percent > 90:
            warnings.append("High memory usage")
        if disk.percent > 90:
            warnings.append("Low disk space")
        
        if warnings:
            health_status["checks"]["system"]["warnings"] = warnings
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "error",
            "message": f"Failed to get system metrics: {str(e)}"
        }
    
    # XAI libraries check
    try:
        xai_libraries = {}
        
        # Check SHAP
        try:
            import shap
            xai_libraries["shap"] = {
                "available": True,
                "version": shap.__version__
            }
        except ImportError:
            xai_libraries["shap"] = {
                "available": False,
                "message": "SHAP not installed"
            }
        
        # Check LIME
        try:
            import lime
            xai_libraries["lime"] = {
                "available": True,
                "version": getattr(lime, '__version__', 'unknown')
            }
        except ImportError:
            xai_libraries["lime"] = {
                "available": False,
                "message": "LIME not installed"
            }
        
        # Check PyTorch/Captum
        try:
            import torch
            import captum
            xai_libraries["pytorch_captum"] = {
                "available": True,
                "torch_version": torch.__version__,
                "captum_version": captum.__version__
            }
        except ImportError:
            xai_libraries["pytorch_captum"] = {
                "available": False,
                "message": "PyTorch/Captum not installed"
            }
        
        # Check Plotly
        try:
            import plotly
            xai_libraries["plotly"] = {
                "available": True,
                "version": plotly.__version__
            }
        except ImportError:
            xai_libraries["plotly"] = {
                "available": False,
                "message": "Plotly not installed"
            }
        
        health_status["checks"]["xai_libraries"] = {
            "status": "healthy",
            "libraries": xai_libraries
        }
        
    except Exception as e:
        health_status["checks"]["xai_libraries"] = {
            "status": "error",
            "message": f"XAI libraries check failed: {str(e)}"
        }
    
    # Storage paths check
    try:
        storage_paths = {
            "explanations": settings.EXPLANATIONS_STORAGE_PATH,
            "visualizations": settings.VISUALIZATIONS_STORAGE_PATH,
            "audit_logs": settings.AUDIT_LOGS_STORAGE_PATH,
            "model_artifacts": settings.MODEL_ARTIFACTS_STORAGE_PATH
        }
        
        path_status = {}
        for name, path in storage_paths.items():
            if os.path.exists(path) and os.access(path, os.W_OK):
                path_status[name] = "accessible"
            else:
                path_status[name] = "not_accessible"
        
        health_status["checks"]["storage_paths"] = {
            "status": "healthy" if all(status == "accessible" for status in path_status.values()) else "warning",
            "paths": path_status
        }
        
    except Exception as e:
        health_status["checks"]["storage_paths"] = {
            "status": "error",
            "message": f"Storage paths check failed: {str(e)}"
        }
    
    # External services check
    try:
        import httpx
        external_services = {
            "data_nexus": settings.DATA_NEXUS_URL,
            "strategy_genesis": settings.STRATEGY_GENESIS_URL,
            "backtesting_engine": settings.BACKTESTING_ENGINE_URL,
            "portfolio_construction": settings.PORTFOLIO_CONSTRUCTION_URL
        }
        
        service_status = {}
        for service_name, service_url in external_services.items():
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(f"{service_url}/health")
                    if response.status_code == 200:
                        service_status[service_name] = "healthy"
                    else:
                        service_status[service_name] = "unhealthy"
                        health_status["status"] = "degraded"
            except Exception:
                service_status[service_name] = "unreachable"
                health_status["status"] = "degraded"
        
        health_status["checks"]["external_services"] = {
            "status": "healthy" if all(s == "healthy" for s in service_status.values()) else "degraded",
            "services": service_status
        }
        
    except Exception as e:
        health_status["checks"]["external_services"] = {
            "status": "error",
            "message": f"External services check failed: {str(e)}"
        }
    
    return health_status


@router.get("/engines")
async def engines_status():
    """Check status of XAI engines."""
    engines_status = {
        "timestamp": datetime.utcnow().isoformat(),
        "engines": {}
    }
    
    # Strategy Explainer
    try:
        from app.engine.explainability import StrategyExplainer
        engines_status["engines"]["strategy_explainer"] = {
            "status": "available",
            "supported_methods": [
                "SHAP",
                "LIME", 
                "FEATURE_IMPORTANCE",
                "PERMUTATION_IMPORTANCE",
                "GRADIENT_ANALYSIS"
            ],
            "config": {
                "shap_sample_size": settings.SHAP_SAMPLE_SIZE,
                "lime_num_features": settings.LIME_NUM_FEATURES,
                "enable_caching": settings.ENABLE_EXPLANATION_CACHING
            }
        }
    except Exception as e:
        engines_status["engines"]["strategy_explainer"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Performance Attributor
    try:
        from app.engine.attribution import PerformanceAttributor
        engines_status["engines"]["performance_attributor"] = {
            "status": "available",
            "supported_methods": [
                "FACTOR_ATTRIBUTION",
                "TRADE_ATTRIBUTION",
                "TEMPORAL_ATTRIBUTION",
                "REGIME_ATTRIBUTION"
            ],
            "config": {
                "attribution_lookback_days": settings.ATTRIBUTION_LOOKBACK_DAYS,
                "factor_models": settings.FACTOR_ATTRIBUTION_MODELS
            }
        }
    except Exception as e:
        engines_status["engines"]["performance_attributor"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Model Interpreter
    try:
        from app.engine.interpretability import ModelInterpreter
        engines_status["engines"]["model_interpreter"] = {
            "status": "available",
            "supported_paradigms": ["GP", "RL", "DL", "HYBRID"],
            "config": {
                "enable_attention_analysis": settings.ENABLE_ATTENTION_VISUALIZATION,
                "enable_gradient_analysis": settings.ENABLE_GRADIENT_ANALYSIS,
                "max_layers_to_analyze": settings.MAX_LAYERS_TO_ANALYZE
            }
        }
    except Exception as e:
        engines_status["engines"]["model_interpreter"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Audit Manager
    try:
        from app.engine.audit import AuditTrailManager
        engines_status["engines"]["audit_manager"] = {
            "status": "available",
            "config": {
                "enable_bias_detection": settings.ENABLE_BIAS_DETECTION,
                "regulatory_frameworks": settings.REGULATORY_FRAMEWORKS,
                "audit_retention_days": settings.AUDIT_RETENTION_DAYS
            }
        }
    except Exception as e:
        engines_status["engines"]["audit_manager"] = {
            "status": "error",
            "message": str(e)
        }
    
    # Visualization Engine
    try:
        from app.engine.visualization import VisualizationEngine
        engines_status["engines"]["visualization_engine"] = {
            "status": "available",
            "config": {
                "enable_interactive_dashboards": settings.ENABLE_INTERACTIVE_DASHBOARDS,
                "chart_export_formats": settings.CHART_EXPORT_FORMATS,
                "max_visualization_points": settings.MAX_VISUALIZATION_POINTS
            }
        }
    except Exception as e:
        engines_status["engines"]["visualization_engine"] = {
            "status": "error",
            "message": str(e)
        }
    
    return engines_status


@router.get("/metrics")
async def get_metrics(db: AsyncSession = Depends(get_db)):
    """Get service metrics and statistics."""
    try:
        from app.models.explanation import StrategyExplanation, FeatureImportance, PerformanceAttribution, AuditTrail
        from sqlalchemy import func, select
        from datetime import timedelta
        
        # Get explanation counts by type
        explanation_counts = await db.execute(
            select(StrategyExplanation.explanation_type, func.count(StrategyExplanation.id))
            .group_by(StrategyExplanation.explanation_type)
        )
        
        # Get recent activity
        recent_explanations = await db.execute(
            select(func.count(StrategyExplanation.id))
            .where(StrategyExplanation.created_at >= datetime.utcnow() - timedelta(days=7))
        )
        
        # Get audit trail statistics
        audit_counts = await db.execute(
            select(AuditTrail.action_type, func.count(AuditTrail.id))
            .group_by(AuditTrail.action_type)
        )
        
        # Get compliance statistics
        compliance_counts = await db.execute(
            select(AuditTrail.compliance_status, func.count(AuditTrail.id))
            .group_by(AuditTrail.compliance_status)
        )
        
        # Get performance statistics
        avg_performance = await db.execute(
            select(
                func.avg(StrategyExplanation.explanation_quality_score),
                func.avg(StrategyExplanation.computation_time_seconds),
                func.avg(StrategyExplanation.memory_usage_mb)
            )
        )
        
        avg_stats = avg_performance.fetchone()
        
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "explanations": {
                "by_type": dict(explanation_counts.fetchall()),
                "recent_count": recent_explanations.scalar() or 0
            },
            "audit_trail": {
                "by_action_type": dict(audit_counts.fetchall()),
                "by_compliance_status": dict(compliance_counts.fetchall())
            },
            "performance": {
                "avg_explanation_quality": float(avg_stats[0]) if avg_stats[0] else None,
                "avg_computation_time_seconds": float(avg_stats[1]) if avg_stats[1] else None,
                "avg_memory_usage_mb": float(avg_stats[2]) if avg_stats[2] else None
            },
            "system": {
                "uptime_seconds": psutil.boot_time(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "max_concurrent_explanations": settings.MAX_CONCURRENT_EXPLANATIONS
            },
            "configuration": {
                "shap_sample_size": settings.SHAP_SAMPLE_SIZE,
                "lime_num_features": settings.LIME_NUM_FEATURES,
                "enable_bias_detection": settings.ENABLE_BIAS_DETECTION,
                "regulatory_frameworks": settings.REGULATORY_FRAMEWORKS,
                "explanation_cache_ttl_hours": settings.EXPLANATION_CACHE_TTL_HOURS
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )
