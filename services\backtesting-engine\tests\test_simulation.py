"""
Tests for simulation engine functionality.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal

from app.engine.simulation import (
    SimulationEngine, MarketFrictionModel, ExecutionEngine, PortfolioManager,
    MarketState, OrderRequest, TradeSide
)
from app.schemas.backtest import BacktestConfig


@pytest.fixture
def sample_market_data():
    """Create sample market data for testing."""
    dates = pd.date_range('2023-01-01', periods=100, freq='H')
    np.random.seed(42)
    
    # Generate realistic OHLCV data
    close_prices = 100 + np.cumsum(np.random.randn(100) * 0.01)
    
    data = pd.DataFrame({
        'time': dates,
        'open': close_prices + np.random.randn(100) * 0.001,
        'high': close_prices + np.abs(np.random.randn(100) * 0.002),
        'low': close_prices - np.abs(np.random.randn(100) * 0.002),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    return data


@pytest.fixture
def backtest_config():
    """Create backtest configuration for testing."""
    return BacktestConfig(
        commission_bps=Decimal("1.0"),
        spread_bps=Decimal("2.0"),
        slippage_bps=Decimal("0.5"),
        market_impact_factor=Decimal("0.1"),
        max_position_size=Decimal("0.2"),
        max_leverage=Decimal("2.0")
    )


class TestMarketFrictionModel:
    """Test market friction modeling."""
    
    def test_friction_model_initialization(self, backtest_config):
        """Test friction model initialization."""
        model = MarketFrictionModel(backtest_config)
        
        assert model.commission_bps == 1.0
        assert model.spread_bps == 2.0
        assert model.slippage_bps == 0.5
        assert model.market_impact_factor == 0.1
    
    def test_spread_calculation(self, backtest_config):
        """Test bid-ask spread calculation."""
        model = MarketFrictionModel(backtest_config)
        
        market_state = MarketState(
            timestamp=datetime.now(),
            price=100.0,
            bid=99.99,
            ask=100.01,
            volume=5000.0,
            volatility=0.02,
            liquidity_score=1.0
        )
        
        bid, ask = model.calculate_spread(market_state, 1000.0)
        
        assert bid < market_state.price
        assert ask > market_state.price
        assert ask > bid
    
    def test_slippage_calculation(self, backtest_config):
        """Test slippage calculation."""
        model = MarketFrictionModel(backtest_config)
        
        market_state = MarketState(
            timestamp=datetime.now(),
            price=100.0,
            bid=99.99,
            ask=100.01,
            volume=5000.0,
            volatility=0.02,
            liquidity_score=1.0
        )
        
        # Buy order should have positive slippage (adverse)
        buy_slippage = model.calculate_slippage(market_state, TradeSide.BUY, 1000.0)
        assert buy_slippage >= 0
        
        # Sell order should have negative slippage (adverse)
        sell_slippage = model.calculate_slippage(market_state, TradeSide.SELL, 1000.0)
        assert sell_slippage <= 0
    
    def test_commission_calculation(self, backtest_config):
        """Test commission calculation."""
        model = MarketFrictionModel(backtest_config)
        
        commission = model.calculate_commission(1000.0, 100.0)
        
        # Commission should be 1 bps of trade value
        expected_commission = 1000.0 * 100.0 * 0.0001
        assert abs(commission - expected_commission) < 0.01


class TestExecutionEngine:
    """Test order execution engine."""
    
    def test_execution_engine_initialization(self, backtest_config):
        """Test execution engine initialization."""
        engine = ExecutionEngine(backtest_config)
        
        assert engine.config == backtest_config
        assert engine.friction_model is not None
    
    def test_latency_simulation(self, backtest_config):
        """Test execution latency simulation."""
        engine = ExecutionEngine(backtest_config)
        
        latency = engine.simulate_execution_latency()
        
        assert latency >= backtest_config.min_execution_latency_ms
        assert latency <= backtest_config.max_execution_latency_ms
    
    def test_order_execution(self, backtest_config):
        """Test order execution."""
        engine = ExecutionEngine(backtest_config)
        
        market_state = MarketState(
            timestamp=datetime.now(),
            price=100.0,
            bid=99.99,
            ask=100.01,
            volume=5000.0,
            volatility=0.02,
            liquidity_score=1.0
        )
        
        order = OrderRequest(
            symbol="EURUSD",
            side=TradeSide.BUY,
            quantity=1000.0
        )
        
        result = engine.execute_order(order, market_state)
        
        assert result.filled_quantity == 1000.0
        assert result.fill_price > 0
        assert result.commission >= 0
        assert result.total_cost >= 0
        assert result.execution_latency_ms >= 0


class TestPortfolioManager:
    """Test portfolio management."""
    
    def test_portfolio_initialization(self, backtest_config):
        """Test portfolio manager initialization."""
        manager = PortfolioManager(100000.0, backtest_config)
        
        assert manager.initial_capital == 100000.0
        assert manager.current_capital == 100000.0
        assert len(manager.positions) == 0
        assert manager.trade_count == 0
    
    def test_position_tracking(self, backtest_config):
        """Test position tracking."""
        manager = PortfolioManager(100000.0, backtest_config)
        
        # Initially no position
        assert manager.get_position("EURUSD") == 0.0
        
        # Set position
        manager.positions["EURUSD"] = 1000.0
        assert manager.get_position("EURUSD") == 1000.0
    
    def test_position_limits(self, backtest_config):
        """Test position limit checking."""
        manager = PortfolioManager(100000.0, backtest_config)
        manager.total_value = 100000.0
        
        # Normal position should be allowed
        assert manager.check_position_limits("EURUSD", 1000.0, 1.0)
        
        # Very large position should be rejected
        assert not manager.check_position_limits("EURUSD", 50000.0, 1.0)


class TestSimulationEngine:
    """Test main simulation engine."""
    
    def test_simulation_initialization(self, backtest_config, sample_market_data):
        """Test simulation engine initialization."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        assert engine.execution_engine is not None
        assert engine.portfolio_manager is not None
        assert engine.market_data is not None
        assert engine.current_bar_index == 0
    
    def test_market_state_generation(self, backtest_config, sample_market_data):
        """Test market state generation."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        market_state = engine.get_current_market_state("EURUSD")
        
        assert market_state.price > 0
        assert market_state.volatility >= 0
        assert 0 <= market_state.liquidity_score <= 2.0
    
    def test_signal_processing(self, backtest_config, sample_market_data):
        """Test signal processing and trade execution."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        # Process a buy signal
        execution_result = engine.process_signal("EURUSD", 0.5, 0.8)
        
        if execution_result:  # Trade might be rejected due to limits
            assert execution_result.filled_quantity != 0
            assert execution_result.fill_price > 0
    
    def test_time_advancement(self, backtest_config, sample_market_data):
        """Test time advancement in simulation."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        initial_index = engine.current_bar_index
        
        # Advance time
        can_continue = engine.advance_time()
        
        assert engine.current_bar_index == initial_index + 1
        assert can_continue  # Should be able to continue with sample data
    
    def test_portfolio_snapshot(self, backtest_config, sample_market_data):
        """Test portfolio snapshot generation."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        snapshot = engine.get_portfolio_snapshot({"EURUSD": 1.0})
        
        assert "total_value" in snapshot
        assert "cash" in snapshot
        assert "positions" in snapshot
        assert "trade_count" in snapshot
        assert snapshot["total_value"] > 0


@pytest.mark.integration
class TestSimulationIntegration:
    """Integration tests for simulation engine."""
    
    def test_full_simulation_cycle(self, backtest_config, sample_market_data):
        """Test complete simulation cycle."""
        engine = SimulationEngine()
        engine.initialize(backtest_config, 100000.0, sample_market_data)
        
        initial_value = engine.portfolio_manager.total_value
        trades_executed = 0
        
        # Run simulation for first 10 bars
        for _ in range(10):
            if not engine.advance_time():
                break
            
            # Generate random signal
            signal = np.random.uniform(-0.5, 0.5)
            if abs(signal) > 0.1:
                result = engine.process_signal("EURUSD", signal, 0.7)
                if result:
                    trades_executed += 1
        
        # Check that simulation ran
        assert engine.current_bar_index > 0
        
        # Portfolio value should have changed if trades were executed
        final_value = engine.portfolio_manager.calculate_portfolio_value({"EURUSD": 1.0})
        if trades_executed > 0:
            assert final_value != initial_value
