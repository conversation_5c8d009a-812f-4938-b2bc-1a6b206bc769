"""
AthenaTrader Backtesting Engine Service

High-fidelity historical simulation service that provides institutional-grade
backtesting capabilities for AI-generated trading strategies with realistic
market friction modeling and comprehensive performance analytics.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import health, backtests, analytics, robustness

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader Backtesting Engine Service...")
    await init_db()
    logger.info("Database initialized")
    
    # Initialize backtesting components
    from app.engine.simulation import SimulationEngine
    from app.engine.performance import PerformanceAnalytics
    from app.engine.robustness import RobustnessTestingFramework
    
    # Store engines in app state
    app.state.simulation_engine = SimulationEngine()
    app.state.performance_analytics = PerformanceAnalytics()
    app.state.robustness_framework = RobustnessTestingFramework()
    
    logger.info("Backtesting engines initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AthenaTrader Backtesting Engine Service...")
    
    # Cleanup engines
    if hasattr(app.state, 'simulation_engine'):
        await app.state.simulation_engine.cleanup()
    if hasattr(app.state, 'performance_analytics'):
        await app.state.performance_analytics.cleanup()
    if hasattr(app.state, 'robustness_framework'):
        await app.state.robustness_framework.cleanup()


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader Backtesting Engine",
    description="High-fidelity historical simulation service for AI-generated trading strategies",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(backtests.router, prefix="/backtests", tags=["backtests"])
app.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
app.include_router(robustness.router, prefix="/robustness", tags=["robustness"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader Backtesting Engine Service",
        "version": "0.1.0",
        "status": "operational",
        "capabilities": [
            "high_fidelity_simulation",
            "market_friction_modeling",
            "performance_analytics",
            "robustness_testing",
            "monte_carlo_simulation"
        ]
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
