# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0

# TimescaleDB
timescaledb==0.0.4

# Data Processing
pandas==2.1.4
numpy==1.25.2
pyarrow==14.0.1

# AI/ML Core
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.1
deap==1.4.1

# Explainable AI
shap==0.43.0
lime==*******

# Technical Analysis
TA-Lib==0.4.28
ta==0.10.2

# API Clients
httpx==0.25.2
websockets==12.0
aiohttp==3.9.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration & Environment
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Async Support
asyncio==3.4.3
aiofiles==23.2.1

# Redis
redis==5.0.1
aioredis==2.0.1

# Message Queue
celery==5.3.4

# Data Validation
cerberus==1.3.5
marshmallow==3.20.1

# Financial Data
yfinance==0.2.28
alpha-vantage==2.3.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
