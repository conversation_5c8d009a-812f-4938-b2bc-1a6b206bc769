import axios, { AxiosResponse } from 'axios';
import { User, LoginRequest, LoginResponse } from '@/types/auth';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class AuthService {
  private token: string | null = null;

  constructor() {
    // Set up axios interceptors
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    axios.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token is invalid, clear it
          this.removeToken();
          localStorage.removeItem('athena_token');
          // Redirect to login page
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async login(username: string, password: string): Promise<{ user: User; token: string }> {
    try {
      const response: AxiosResponse<LoginResponse> = await axios.post(
        `${API_BASE_URL}/auth/login`,
        {
          username,
          password,
        } as LoginRequest
      );

      const { access_token } = response.data;
      this.setToken(access_token);

      // Get user profile
      const user = await this.getCurrentUser();

      return {
        user,
        token: access_token,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.error?.message || 'Login failed';
        throw new Error(message);
      }
      throw new Error('Network error occurred');
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      const response: AxiosResponse<User> = await axios.get(
        `${API_BASE_URL}/auth/me`
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.error?.message || 'Failed to get user profile';
        throw new Error(message);
      }
      throw new Error('Network error occurred');
    }
  }

  async logout(): Promise<void> {
    try {
      await axios.post(`${API_BASE_URL}/auth/logout`);
    } catch (error) {
      // Ignore logout errors, just clear local state
      console.warn('Logout request failed:', error);
    } finally {
      this.removeToken();
    }
  }

  async refreshToken(): Promise<string> {
    try {
      const response: AxiosResponse<LoginResponse> = await axios.post(
        `${API_BASE_URL}/auth/refresh`
      );
      const { access_token } = response.data;
      this.setToken(access_token);
      return access_token;
    } catch (error) {
      this.removeToken();
      throw error;
    }
  }

  setToken(token: string): void {
    this.token = token;
  }

  removeToken(): void {
    this.token = null;
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }
}

export const authService = new AuthService();
