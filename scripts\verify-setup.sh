#!/bin/bash

# AthenaTrader Setup Verification Script

set -e

echo "🔍 Verifying AthenaTrader setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check if Docker is running
echo "Checking Docker..."
docker --version > /dev/null 2>&1
print_status $? "Docker is installed and running"

# Check if Docker Compose is available
docker-compose --version > /dev/null 2>&1
print_status $? "Docker Compose is available"

# Check if .env file exists
if [ -f ".env" ]; then
    print_status 0 ".env file exists"
else
    print_warning ".env file not found. Creating from template..."
    cp .env.example .env
    print_info "Please edit .env file with your API keys"
fi

# Start services
echo ""
echo "🚀 Starting AthenaTrader services..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo ""
echo "🏥 Checking service health..."

# Check PostgreSQL
docker-compose exec -T postgres pg_isready -U athena_user > /dev/null 2>&1
print_status $? "PostgreSQL is ready"

# Check Redis
docker-compose exec -T redis redis-cli ping > /dev/null 2>&1
print_status $? "Redis is ready"

# Check API Gateway
curl -f http://localhost:8000/health > /dev/null 2>&1
print_status $? "API Gateway is healthy"

# Check Data Nexus
curl -f http://localhost:8001/health > /dev/null 2>&1
print_status $? "Data Nexus is healthy"

# Check Frontend
curl -f http://localhost:3000/health > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Frontend is healthy"
else
    print_warning "Frontend health check failed (may still be building)"
fi

# Test database connection
echo ""
echo "🗄️  Testing database..."
docker-compose exec -T postgres psql -U athena_user -d athena_trader -c "SELECT COUNT(*) FROM auth.users;" > /dev/null 2>&1
print_status $? "Database schema is initialized"

# Test API endpoints
echo ""
echo "🔌 Testing API endpoints..."

# Test root endpoint
curl -f http://localhost:8000/ > /dev/null 2>&1
print_status $? "API Gateway root endpoint"

# Test info endpoint
curl -f http://localhost:8000/info > /dev/null 2>&1
print_status $? "API Gateway info endpoint"

# Test Data Nexus instruments endpoint
curl -f http://localhost:8001/instruments/ > /dev/null 2>&1
print_status $? "Data Nexus instruments endpoint"

# Display service URLs
echo ""
echo "🌐 Service URLs:"
echo "   Frontend:        http://localhost:3000"
echo "   API Gateway:     http://localhost:8000"
echo "   API Docs:        http://localhost:8000/docs"
echo "   Data Nexus:      http://localhost:8001"
echo "   Data Nexus Docs: http://localhost:8001/docs"

echo ""
echo "👤 Default Login Credentials:"
echo "   Username: <EMAIL>"
echo "   Password: password"

echo ""
echo "🎉 AthenaTrader setup verification completed successfully!"
echo ""
echo "Next steps:"
echo "1. Visit http://localhost:3000 to access the frontend"
echo "2. Login with the expert credentials"
echo "3. Explore the API documentation at http://localhost:8000/docs"
echo "4. Configure your Alpha Vantage API key in .env for market data"
echo ""
echo "To stop services: docker-compose down"
echo "To view logs: docker-compose logs -f [service-name]"
