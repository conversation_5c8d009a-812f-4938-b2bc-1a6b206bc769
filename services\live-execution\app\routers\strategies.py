"""
Strategy deployment and management router.
"""

import logging
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.schemas.execution import StrategyDeploymentRequest, StrategyDeploymentResponse

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


@router.post("/deploy", response_model=StrategyDeploymentResponse)
async def deploy_strategy(
    deployment_request: StrategyDeploymentRequest,
    background_tasks: BackgroundTasks,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Deploy strategy for live trading."""
    try:
        if not hasattr(request.app.state, 'strategy_deployer'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Strategy deployer not available"
            )
        
        deployer = request.app.state.strategy_deployer
        response = await deployer.deploy_strategy(deployment_request)
        
        return response
        
    except Exception as e:
        logger.error(f"Strategy deployment failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Strategy deployment failed: {str(e)}"
        )


@router.get("/deployments/{deployment_id}")
async def get_deployment(
    deployment_id: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get deployment status."""
    try:
        if not hasattr(request.app.state, 'strategy_deployer'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Strategy deployer not available"
            )
        
        deployer = request.app.state.strategy_deployer
        deployment = await deployer.get_deployment_status(deployment_id)
        
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        return deployment
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Deployment retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment"
        )


@router.delete("/deployments/{deployment_id}")
async def stop_deployment(
    deployment_id: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Stop strategy deployment."""
    try:
        if not hasattr(request.app.state, 'strategy_deployer'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Strategy deployer not available"
            )
        
        deployer = request.app.state.strategy_deployer
        success = await deployer.stop_deployment(deployment_id)
        
        if success:
            return {"message": "Deployment stopped successfully", "deployment_id": deployment_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to stop deployment"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Deployment stop failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop deployment"
        )


@router.get("/deployments")
async def list_deployments(
    strategy_id: uuid.UUID = None,
    status: str = None,
    request: Request = None,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """List strategy deployments."""
    try:
        if not hasattr(request.app.state, 'strategy_deployer'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Strategy deployer not available"
            )
        
        deployer = request.app.state.strategy_deployer
        deployments = await deployer.list_active_deployments()
        
        # Filter by strategy_id if provided
        if strategy_id:
            deployments = [d for d in deployments if d.strategy_id == strategy_id]
        
        # Filter by status if provided
        if status:
            deployments = [d for d in deployments if d.status == status]
        
        return {
            "deployments": deployments,
            "total": len(deployments),
            "filters": {
                "strategy_id": strategy_id,
                "status": status
            }
        }
        
    except Exception as e:
        logger.error(f"Deployment listing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list deployments"
        )
