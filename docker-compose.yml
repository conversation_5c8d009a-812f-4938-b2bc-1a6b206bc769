version: '3.8'

services:
  # Database Services
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: athena-postgres
    environment:
      POSTGRES_DB: athena_trader
      POSTGRES_USER: athena_user
      POSTGRES_PASSWORD: athena_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - athena-network

  redis:
    image: redis:7-alpine
    container_name: athena-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - athena-network

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: athena-api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/api-gateway:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Data Nexus Service
  data-nexus:
    build:
      context: .
      dockerfile: services/data-nexus/Dockerfile
    container_name: athena-data-nexus
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/data-nexus:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Strategy Genesis Service
  strategy-genesis:
    build:
      context: ./services/strategy-genesis
      dockerfile: Dockerfile
    container_name: athena-strategy-genesis
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - MODEL_STORAGE_PATH=/app/models
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
    volumes:
      - ./services/strategy-genesis:/app
      - strategy_models:/app/models
    networks:
      - athena-network

  # Backtesting Engine Service
  backtesting-engine:
    build:
      context: .
      dockerfile: services/backtesting-engine/Dockerfile
    container_name: athena-backtesting-engine
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/backtesting-engine:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: athena-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - athena-network

volumes:
  postgres_data:
  redis_data:
  strategy_models:

networks:
  athena-network:
    driver: bridge
