"""
Order management router for live trading operations.
"""

import logging
from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.schemas.execution import OrderRequest, OrderResponse

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_current_user_id(request: Request) -> uuid.UUID:
    """Get current user ID from request state."""
    if not hasattr(request.state, "user_id"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


@router.post("/", response_model=OrderResponse)
async def submit_order(
    order_request: OrderRequest,
    background_tasks: BackgroundTasks,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Submit order for execution."""
    try:
        if not hasattr(request.app.state, 'order_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Order manager not available"
            )
        
        order_manager = request.app.state.order_manager
        response = await order_manager.submit_order(order_request)
        
        return response
        
    except Exception as e:
        logger.error(f"Order submission failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Order submission failed: {str(e)}"
        )


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Get order by ID."""
    try:
        if not hasattr(request.app.state, 'order_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Order manager not available"
            )
        
        order_manager = request.app.state.order_manager
        order = await order_manager.get_order_status(order_id)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        # TODO: Convert order dict to OrderResponse
        return {"message": "Order retrieval not yet implemented", "order_id": order_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Order retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order"
        )


@router.delete("/{order_id}")
async def cancel_order(
    order_id: str,
    request: Request,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """Cancel order."""
    try:
        if not hasattr(request.app.state, 'order_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Order manager not available"
            )
        
        order_manager = request.app.state.order_manager
        success = await order_manager.cancel_order(order_id)
        
        if success:
            return {"message": "Order cancelled successfully", "order_id": order_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to cancel order"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Order cancellation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel order"
        )


@router.get("/")
async def list_orders(
    strategy_id: Optional[uuid.UUID] = None,
    status: Optional[str] = None,
    request: Request = None,
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db)
):
    """List orders with optional filtering."""
    try:
        if not hasattr(request.app.state, 'order_manager'):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Order manager not available"
            )
        
        order_manager = request.app.state.order_manager
        orders = await order_manager.list_active_orders(strategy_id)
        
        # TODO: Convert orders to proper response format
        return {
            "orders": orders,
            "total": len(orders),
            "filters": {
                "strategy_id": strategy_id,
                "status": status
            }
        }
        
    except Exception as e:
        logger.error(f"Order listing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list orders"
        )
