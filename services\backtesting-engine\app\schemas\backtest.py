"""
Pydantic schemas for backtest operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from enum import Enum

from pydantic import BaseModel, Field, validator


class BacktestStatus(str, Enum):
    """Backtest status enumeration."""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class TradeSide(str, Enum):
    """Trade side enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class FillType(str, Enum):
    """Fill type enumeration."""
    FULL = "FULL"
    PARTIAL = "PARTIAL"


class PeriodType(str, Enum):
    """Analytics period type enumeration."""
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    YEARLY = "YEARLY"
    FULL = "FULL"


class BacktestConfig(BaseModel):
    """Backtest configuration schema."""
    
    # Simulation Parameters
    commission_bps: Decimal = Field(default=Decimal("1.0"), ge=0, le=1000, description="Commission in basis points")
    spread_bps: Decimal = Field(default=Decimal("2.0"), ge=0, le=1000, description="Spread in basis points")
    slippage_bps: Decimal = Field(default=Decimal("0.5"), ge=0, le=1000, description="Slippage in basis points")
    market_impact_factor: Decimal = Field(default=Decimal("0.1"), ge=0, le=10, description="Market impact factor")
    
    # Risk Management
    max_position_size: Decimal = Field(default=Decimal("0.2"), gt=0, le=1, description="Maximum position size as fraction of portfolio")
    stop_loss_pct: Optional[Decimal] = Field(None, gt=0, le=1, description="Stop loss percentage")
    take_profit_pct: Optional[Decimal] = Field(None, gt=0, le=10, description="Take profit percentage")
    max_leverage: Decimal = Field(default=Decimal("1.0"), ge=1, le=100, description="Maximum leverage")
    
    # Execution Settings
    enable_latency_simulation: bool = Field(default=True, description="Enable execution latency simulation")
    min_execution_latency_ms: int = Field(default=10, ge=0, le=10000, description="Minimum execution latency in milliseconds")
    max_execution_latency_ms: int = Field(default=100, ge=0, le=10000, description="Maximum execution latency in milliseconds")
    
    # Advanced Settings
    enable_partial_fills: bool = Field(default=False, description="Enable partial fill simulation")
    liquidity_impact: bool = Field(default=True, description="Include liquidity impact in execution")
    weekend_trading: bool = Field(default=False, description="Allow trading on weekends")
    
    # Custom Parameters
    custom_parameters: Dict[str, Any] = Field(default_factory=dict, description="Custom strategy-specific parameters")


class BacktestCreate(BaseModel):
    """Schema for creating a new backtest."""
    
    user_id: uuid.UUID = Field(..., description="User ID who owns the backtest")
    strategy_id: uuid.UUID = Field(..., description="Strategy ID to backtest")
    name: str = Field(..., min_length=1, max_length=255, description="Backtest name")
    description: Optional[str] = Field(None, description="Backtest description")
    
    # Time Period
    start_date: datetime = Field(..., description="Backtest start date")
    end_date: datetime = Field(..., description="Backtest end date")
    timeframe: str = Field(default="1h", description="Data timeframe")
    
    # Market Data
    instruments: List[uuid.UUID] = Field(..., min_items=1, description="List of instrument IDs")
    benchmark_symbol: Optional[str] = Field("SPY", description="Benchmark symbol for comparison")
    
    # Capital and Configuration
    initial_capital: Decimal = Field(default=Decimal("100000.00"), gt=0, description="Initial capital")
    config: BacktestConfig = Field(default_factory=BacktestConfig, description="Backtest configuration")
    
    @validator("end_date")
    def validate_end_date(cls, v, values):
        """Validate end date is after start date."""
        if "start_date" in values and v <= values["start_date"]:
            raise ValueError("End date must be after start date")
        return v
    
    @validator("timeframe")
    def validate_timeframe(cls, v):
        """Validate timeframe is supported."""
        from app.core.config import settings
        if v not in settings.SUPPORTED_TIMEFRAMES:
            raise ValueError(f"Timeframe must be one of: {settings.SUPPORTED_TIMEFRAMES}")
        return v


class BacktestUpdate(BaseModel):
    """Schema for updating backtest information."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[BacktestStatus] = None


class PerformanceMetrics(BaseModel):
    """Performance metrics schema."""
    total_return: Optional[Decimal] = None
    annualized_return: Optional[Decimal] = None
    volatility: Optional[Decimal] = None
    sharpe_ratio: Optional[Decimal] = None
    sortino_ratio: Optional[Decimal] = None
    calmar_ratio: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    max_drawdown_duration_days: Optional[int] = None
    
    # Trade Statistics
    total_trades: Optional[int] = None
    winning_trades: Optional[int] = None
    losing_trades: Optional[int] = None
    win_rate: Optional[Decimal] = None
    profit_factor: Optional[Decimal] = None
    avg_trade_return: Optional[Decimal] = None
    avg_trade_duration_hours: Optional[Decimal] = None
    
    # Risk Metrics
    var_95: Optional[Decimal] = None
    cvar_95: Optional[Decimal] = None
    beta: Optional[Decimal] = None
    alpha: Optional[Decimal] = None
    information_ratio: Optional[Decimal] = None
    treynor_ratio: Optional[Decimal] = None


class Backtest(BaseModel):
    """Public backtest schema."""
    id: uuid.UUID
    user_id: uuid.UUID
    strategy_id: uuid.UUID
    name: str
    description: Optional[str]
    
    # Configuration
    start_date: datetime
    end_date: datetime
    timeframe: str
    instruments: List[uuid.UUID]
    benchmark_symbol: Optional[str]
    initial_capital: Decimal
    config: Dict[str, Any]
    
    # Status
    status: BacktestStatus
    progress_pct: Decimal
    
    # Performance
    performance_metrics: PerformanceMetrics
    
    # Execution Info
    bars_processed: Optional[int]
    execution_time_seconds: Optional[int]
    memory_usage_mb: Optional[int]
    error_message: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class BacktestTrade(BaseModel):
    """Backtest trade schema."""
    id: uuid.UUID
    backtest_id: uuid.UUID
    trade_id: str
    instrument_id: uuid.UUID
    symbol: str
    
    # Trade Details
    side: TradeSide
    quantity: Decimal
    price: Decimal
    timestamp: datetime
    
    # Execution Details
    signal_strength: Optional[Decimal]
    intended_quantity: Optional[Decimal]
    fill_type: FillType
    
    # Costs
    commission: Decimal
    spread_cost: Decimal
    slippage_cost: Decimal
    market_impact_cost: Decimal
    total_cost: Decimal
    
    # Position Information
    position_before: Decimal
    position_after: Decimal
    portfolio_value_before: Decimal
    portfolio_value_after: Decimal
    
    # Trade Performance
    entry_price: Optional[Decimal]
    exit_price: Optional[Decimal]
    entry_timestamp: Optional[datetime]
    exit_timestamp: Optional[datetime]
    pnl: Optional[Decimal]
    pnl_pct: Optional[Decimal]
    duration_hours: Optional[Decimal]
    
    # Risk Management
    stop_loss_triggered: bool
    take_profit_triggered: bool
    risk_limit_triggered: bool
    
    # Market Context
    market_price: Optional[Decimal]
    bid_price: Optional[Decimal]
    ask_price: Optional[Decimal]
    volatility: Optional[Decimal]
    
    created_at: datetime
    
    class Config:
        from_attributes = True


class BacktestAnalytics(BaseModel):
    """Backtest analytics schema."""
    id: uuid.UUID
    backtest_id: uuid.UUID
    
    # Period
    period_start: datetime
    period_end: datetime
    period_type: PeriodType
    
    # Performance
    period_return: Optional[Decimal]
    cumulative_return: Optional[Decimal]
    benchmark_return: Optional[Decimal]
    excess_return: Optional[Decimal]
    
    # Risk
    volatility: Optional[Decimal]
    downside_volatility: Optional[Decimal]
    tracking_error: Optional[Decimal]
    
    # Ratios
    sharpe_ratio: Optional[Decimal]
    sortino_ratio: Optional[Decimal]
    information_ratio: Optional[Decimal]
    calmar_ratio: Optional[Decimal]
    
    # Drawdown
    max_drawdown: Optional[Decimal]
    drawdown_duration_days: Optional[int]
    recovery_time_days: Optional[int]
    
    # Trades
    trade_count: Optional[int]
    win_count: Optional[int]
    loss_count: Optional[int]
    win_rate: Optional[Decimal]
    avg_win: Optional[Decimal]
    avg_loss: Optional[Decimal]
    profit_factor: Optional[Decimal]
    
    detailed_metrics: Dict[str, Any]
    created_at: datetime
    
    class Config:
        from_attributes = True


class BacktestProgress(BaseModel):
    """Backtest progress update schema."""
    backtest_id: uuid.UUID
    progress_pct: Decimal
    bars_processed: int
    current_date: datetime
    current_portfolio_value: Decimal
    trades_executed: int
    estimated_completion: Optional[datetime]


class MonteCarloConfig(BaseModel):
    """Monte Carlo simulation configuration."""
    simulation_runs: int = Field(default=1000, ge=100, le=10000, description="Number of simulation runs")
    confidence_intervals: List[float] = Field(default=[0.05, 0.25, 0.5, 0.75, 0.95], description="Confidence intervals")
    randomize_parameters: bool = Field(default=True, description="Randomize strategy parameters")
    randomize_returns: bool = Field(default=True, description="Randomize return sequences")
    bootstrap_method: str = Field(default="block", description="Bootstrap method (block, stationary)")
    block_size: int = Field(default=20, ge=1, le=100, description="Block size for block bootstrap")


class WalkForwardConfig(BaseModel):
    """Walk-forward analysis configuration."""
    training_window_days: int = Field(default=252, ge=30, le=1825, description="Training window in days")
    test_window_days: int = Field(default=63, ge=7, le=365, description="Test window in days")
    step_size_days: int = Field(default=21, ge=1, le=365, description="Step size in days")
    reoptimize_parameters: bool = Field(default=True, description="Reoptimize parameters each step")
    min_trades_threshold: int = Field(default=10, ge=1, description="Minimum trades for valid period")


class StressTestConfig(BaseModel):
    """Stress testing configuration."""
    scenarios: List[str] = Field(default=["market_crash", "flash_crash", "high_volatility"], description="Stress test scenarios")
    custom_scenarios: Dict[str, Dict[str, float]] = Field(default_factory=dict, description="Custom stress scenarios")
    apply_to_returns: bool = Field(default=True, description="Apply stress to returns")
    apply_to_volatility: bool = Field(default=True, description="Apply stress to volatility")
    correlation_breakdown: bool = Field(default=False, description="Simulate correlation breakdown")
