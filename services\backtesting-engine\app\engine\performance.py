"""
Performance analytics engine for comprehensive backtest analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
import scipy.stats as stats
from dataclasses import dataclass

from app.core.config import settings
from app.schemas.backtest import PerformanceMetrics

logger = logging.getLogger(__name__)


@dataclass
class TradeAnalysis:
    """Individual trade analysis results."""
    entry_time: datetime
    exit_time: datetime
    symbol: str
    side: str
    quantity: float
    entry_price: float
    exit_price: float
    pnl: float
    pnl_pct: float
    duration_hours: float
    commission: float
    slippage: float
    market_impact: float


@dataclass
class DrawdownPeriod:
    """Drawdown period analysis."""
    start_date: datetime
    end_date: datetime
    peak_value: float
    trough_value: float
    drawdown_pct: float
    duration_days: int
    recovery_date: Optional[datetime] = None
    recovery_days: Optional[int] = None


class PerformanceCalculator:
    """Core performance metrics calculator."""
    
    def __init__(self, risk_free_rate: float = 0.02):
        """Initialize performance calculator."""
        self.risk_free_rate = risk_free_rate
        self.trading_days_per_year = settings.TRADING_DAYS_PER_YEAR
    
    def calculate_returns(self, equity_curve: pd.Series) -> pd.Series:
        """Calculate returns from equity curve."""
        return equity_curve.pct_change().dropna()
    
    def calculate_total_return(self, initial_value: float, final_value: float) -> float:
        """Calculate total return."""
        return (final_value - initial_value) / initial_value
    
    def calculate_annualized_return(self, total_return: float, days: int) -> float:
        """Calculate annualized return."""
        if days <= 0:
            return 0.0
        years = days / 365.25
        return (1 + total_return) ** (1 / years) - 1
    
    def calculate_volatility(self, returns: pd.Series, annualized: bool = True) -> float:
        """Calculate volatility (standard deviation of returns)."""
        vol = returns.std()
        if annualized:
            vol *= np.sqrt(self.trading_days_per_year)
        return vol
    
    def calculate_downside_volatility(self, returns: pd.Series, target_return: float = 0.0, annualized: bool = True) -> float:
        """Calculate downside volatility (standard deviation of negative returns)."""
        downside_returns = returns[returns < target_return]
        if len(downside_returns) == 0:
            return 0.0
        
        downside_vol = downside_returns.std()
        if annualized:
            downside_vol *= np.sqrt(self.trading_days_per_year)
        return downside_vol
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: Optional[float] = None) -> float:
        """Calculate Sharpe ratio."""
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate
        
        excess_returns = returns - (risk_free_rate / self.trading_days_per_year)
        if excess_returns.std() == 0:
            return 0.0
        
        return (excess_returns.mean() / excess_returns.std()) * np.sqrt(self.trading_days_per_year)
    
    def calculate_sortino_ratio(self, returns: pd.Series, target_return: float = 0.0, risk_free_rate: Optional[float] = None) -> float:
        """Calculate Sortino ratio."""
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate
        
        excess_returns = returns - (risk_free_rate / self.trading_days_per_year)
        downside_vol = self.calculate_downside_volatility(returns, target_return, annualized=True)
        
        if downside_vol == 0:
            return 0.0
        
        return (excess_returns.mean() * self.trading_days_per_year) / downside_vol
    
    def calculate_calmar_ratio(self, annualized_return: float, max_drawdown: float) -> float:
        """Calculate Calmar ratio."""
        if max_drawdown == 0:
            return float('inf') if annualized_return > 0 else 0.0
        return annualized_return / abs(max_drawdown)
    
    def calculate_information_ratio(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate Information ratio."""
        excess_returns = returns - benchmark_returns
        tracking_error = excess_returns.std() * np.sqrt(self.trading_days_per_year)
        
        if tracking_error == 0:
            return 0.0
        
        return (excess_returns.mean() * self.trading_days_per_year) / tracking_error
    
    def calculate_treynor_ratio(self, returns: pd.Series, benchmark_returns: pd.Series, risk_free_rate: Optional[float] = None) -> float:
        """Calculate Treynor ratio."""
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate
        
        beta = self.calculate_beta(returns, benchmark_returns)
        if beta == 0:
            return 0.0
        
        excess_return = returns.mean() * self.trading_days_per_year - risk_free_rate
        return excess_return / beta
    
    def calculate_beta(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate beta relative to benchmark."""
        if len(returns) != len(benchmark_returns) or len(returns) < 2:
            return 0.0
        
        covariance = np.cov(returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            return 0.0
        
        return covariance / benchmark_variance
    
    def calculate_alpha(self, returns: pd.Series, benchmark_returns: pd.Series, risk_free_rate: Optional[float] = None) -> float:
        """Calculate alpha (Jensen's alpha)."""
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate
        
        beta = self.calculate_beta(returns, benchmark_returns)
        
        portfolio_return = returns.mean() * self.trading_days_per_year
        benchmark_return = benchmark_returns.mean() * self.trading_days_per_year
        
        expected_return = risk_free_rate + beta * (benchmark_return - risk_free_rate)
        return portfolio_return - expected_return


class DrawdownAnalyzer:
    """Drawdown analysis and calculation."""
    
    def calculate_drawdowns(self, equity_curve: pd.Series) -> Tuple[pd.Series, List[DrawdownPeriod]]:
        """Calculate drawdown series and identify drawdown periods."""
        # Calculate running maximum (peak)
        peak = equity_curve.expanding().max()
        
        # Calculate drawdown as percentage from peak
        drawdown = (equity_curve - peak) / peak
        
        # Identify drawdown periods
        drawdown_periods = []
        in_drawdown = False
        current_period = None
        
        for i, (date, dd_value) in enumerate(drawdown.items()):
            if dd_value < -0.001 and not in_drawdown:  # Start of drawdown (0.1% threshold)
                in_drawdown = True
                current_period = {
                    'start_date': date,
                    'start_index': i,
                    'peak_value': peak.iloc[i],
                    'min_drawdown': dd_value,
                    'trough_value': equity_curve.iloc[i]
                }
            elif dd_value < current_period['min_drawdown'] if in_drawdown else False:
                # Update trough
                current_period['min_drawdown'] = dd_value
                current_period['trough_value'] = equity_curve.iloc[i]
            elif dd_value >= -0.001 and in_drawdown:  # End of drawdown
                in_drawdown = False
                current_period['end_date'] = date
                current_period['recovery_date'] = date
                
                # Calculate duration
                duration = (current_period['end_date'] - current_period['start_date']).days
                recovery_days = duration
                
                drawdown_period = DrawdownPeriod(
                    start_date=current_period['start_date'],
                    end_date=current_period['end_date'],
                    peak_value=current_period['peak_value'],
                    trough_value=current_period['trough_value'],
                    drawdown_pct=abs(current_period['min_drawdown']),
                    duration_days=duration,
                    recovery_date=current_period['recovery_date'],
                    recovery_days=recovery_days
                )
                drawdown_periods.append(drawdown_period)
                current_period = None
        
        # Handle ongoing drawdown
        if in_drawdown and current_period:
            drawdown_period = DrawdownPeriod(
                start_date=current_period['start_date'],
                end_date=equity_curve.index[-1],
                peak_value=current_period['peak_value'],
                trough_value=current_period['trough_value'],
                drawdown_pct=abs(current_period['min_drawdown']),
                duration_days=(equity_curve.index[-1] - current_period['start_date']).days,
                recovery_date=None,
                recovery_days=None
            )
            drawdown_periods.append(drawdown_period)
        
        return drawdown, drawdown_periods
    
    def get_max_drawdown(self, equity_curve: pd.Series) -> Tuple[float, int]:
        """Get maximum drawdown and its duration."""
        drawdown, drawdown_periods = self.calculate_drawdowns(equity_curve)
        
        max_dd = abs(drawdown.min())
        
        # Find longest drawdown duration
        max_duration = 0
        if drawdown_periods:
            max_duration = max(period.duration_days for period in drawdown_periods)
        
        return max_dd, max_duration


class RiskAnalyzer:
    """Risk metrics calculation."""
    
    def __init__(self, confidence_levels: List[float] = None):
        """Initialize risk analyzer."""
        self.confidence_levels = confidence_levels or settings.CONFIDENCE_LEVELS
    
    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.95) -> float:
        """Calculate Value at Risk."""
        if len(returns) == 0:
            return 0.0
        
        return np.percentile(returns, (1 - confidence_level) * 100)
    
    def calculate_cvar(self, returns: pd.Series, confidence_level: float = 0.95) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)."""
        var = self.calculate_var(returns, confidence_level)
        tail_returns = returns[returns <= var]
        
        if len(tail_returns) == 0:
            return var
        
        return tail_returns.mean()
    
    def calculate_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate comprehensive risk metrics."""
        metrics = {}
        
        for confidence_level in self.confidence_levels:
            var = self.calculate_var(returns, confidence_level)
            cvar = self.calculate_cvar(returns, confidence_level)
            
            metrics[f'var_{int(confidence_level*100)}'] = var
            metrics[f'cvar_{int(confidence_level*100)}'] = cvar
        
        return metrics


class TradeAnalyzer:
    """Trade-level analysis and statistics."""
    
    def analyze_trades(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze individual trades and calculate trade statistics."""
        if not trades:
            return {}
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(trades)
        
        # Basic statistics
        total_trades = len(df)
        winning_trades = len(df[df['pnl'] > 0])
        losing_trades = len(df[df['pnl'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # P&L statistics
        total_pnl = df['pnl'].sum()
        avg_pnl = df['pnl'].mean()
        avg_win = df[df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # Profit factor
        gross_profit = df[df['pnl'] > 0]['pnl'].sum()
        gross_loss = abs(df[df['pnl'] < 0]['pnl'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Duration statistics
        avg_duration = df['duration_hours'].mean() if 'duration_hours' in df.columns else 0
        
        # Consecutive wins/losses
        consecutive_wins = self._calculate_consecutive_runs(df['pnl'] > 0)
        consecutive_losses = self._calculate_consecutive_runs(df['pnl'] < 0)
        
        # Largest win/loss
        largest_win = df['pnl'].max()
        largest_loss = df['pnl'].min()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_pnl': avg_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_duration_hours': avg_duration,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'gross_profit': gross_profit,
            'gross_loss': gross_loss
        }
    
    def _calculate_consecutive_runs(self, boolean_series: pd.Series) -> int:
        """Calculate maximum consecutive runs of True values."""
        if len(boolean_series) == 0:
            return 0
        
        max_run = 0
        current_run = 0
        
        for value in boolean_series:
            if value:
                current_run += 1
                max_run = max(max_run, current_run)
            else:
                current_run = 0
        
        return max_run


class PerformanceAnalytics:
    """Main performance analytics engine."""
    
    def __init__(self):
        """Initialize performance analytics."""
        self.calculator = PerformanceCalculator()
        self.drawdown_analyzer = DrawdownAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.trade_analyzer = TradeAnalyzer()
    
    async def cleanup(self):
        """Cleanup performance analytics resources."""
        pass
    
    def calculate_comprehensive_metrics(
        self,
        equity_curve: pd.Series,
        trades: List[Dict[str, Any]],
        benchmark_returns: Optional[pd.Series] = None,
        initial_capital: float = 100000.0
    ) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics."""
        
        # Basic return calculations
        returns = self.calculator.calculate_returns(equity_curve)
        total_return = self.calculator.calculate_total_return(initial_capital, equity_curve.iloc[-1])
        
        # Time-based calculations
        days = (equity_curve.index[-1] - equity_curve.index[0]).days
        annualized_return = self.calculator.calculate_annualized_return(total_return, days)
        
        # Volatility metrics
        volatility = self.calculator.calculate_volatility(returns)
        downside_volatility = self.calculator.calculate_downside_volatility(returns)
        
        # Risk-adjusted returns
        sharpe_ratio = self.calculator.calculate_sharpe_ratio(returns)
        sortino_ratio = self.calculator.calculate_sortino_ratio(returns)
        
        # Drawdown analysis
        max_drawdown, max_dd_duration = self.drawdown_analyzer.get_max_drawdown(equity_curve)
        calmar_ratio = self.calculator.calculate_calmar_ratio(annualized_return, max_drawdown)
        
        # Risk metrics
        risk_metrics = self.risk_analyzer.calculate_risk_metrics(returns)
        
        # Trade analysis
        trade_stats = self.trade_analyzer.analyze_trades(trades)
        
        # Benchmark comparison (if available)
        beta = None
        alpha = None
        information_ratio = None
        treynor_ratio = None
        
        if benchmark_returns is not None and len(benchmark_returns) == len(returns):
            beta = self.calculator.calculate_beta(returns, benchmark_returns)
            alpha = self.calculator.calculate_alpha(returns, benchmark_returns)
            information_ratio = self.calculator.calculate_information_ratio(returns, benchmark_returns)
            treynor_ratio = self.calculator.calculate_treynor_ratio(returns, benchmark_returns)
        
        return PerformanceMetrics(
            total_return=Decimal(str(total_return)),
            annualized_return=Decimal(str(annualized_return)),
            volatility=Decimal(str(volatility)),
            sharpe_ratio=Decimal(str(sharpe_ratio)),
            sortino_ratio=Decimal(str(sortino_ratio)),
            calmar_ratio=Decimal(str(calmar_ratio)),
            max_drawdown=Decimal(str(max_drawdown)),
            max_drawdown_duration_days=max_dd_duration,
            
            # Trade statistics
            total_trades=trade_stats.get('total_trades'),
            winning_trades=trade_stats.get('winning_trades'),
            losing_trades=trade_stats.get('losing_trades'),
            win_rate=Decimal(str(trade_stats.get('win_rate', 0))),
            profit_factor=Decimal(str(trade_stats.get('profit_factor', 0))),
            avg_trade_return=Decimal(str(trade_stats.get('avg_pnl', 0))),
            avg_trade_duration_hours=Decimal(str(trade_stats.get('avg_duration_hours', 0))),
            
            # Risk metrics
            var_95=Decimal(str(risk_metrics.get('var_95', 0))),
            cvar_95=Decimal(str(risk_metrics.get('cvar_95', 0))),
            beta=Decimal(str(beta)) if beta is not None else None,
            alpha=Decimal(str(alpha)) if alpha is not None else None,
            information_ratio=Decimal(str(information_ratio)) if information_ratio is not None else None,
            treynor_ratio=Decimal(str(treynor_ratio)) if treynor_ratio is not None else None
        )
    
    def generate_period_analytics(
        self,
        equity_curve: pd.Series,
        trades: List[Dict[str, Any]],
        period_type: str = "MONTHLY"
    ) -> List[Dict[str, Any]]:
        """Generate period-by-period analytics."""
        
        if period_type == "DAILY":
            freq = 'D'
        elif period_type == "WEEKLY":
            freq = 'W'
        elif period_type == "MONTHLY":
            freq = 'M'
        elif period_type == "YEARLY":
            freq = 'Y'
        else:
            freq = 'M'  # Default to monthly
        
        # Resample equity curve by period
        period_equity = equity_curve.resample(freq).last()
        period_returns = period_equity.pct_change().dropna()
        
        analytics = []
        cumulative_return = 0.0
        
        for i, (date, period_return) in enumerate(period_returns.items()):
            period_start = period_equity.index[i] if i > 0 else equity_curve.index[0]
            period_end = date
            
            # Calculate cumulative return
            cumulative_return = (1 + cumulative_return) * (1 + period_return) - 1
            
            # Filter trades for this period
            period_trades = [
                trade for trade in trades
                if period_start <= pd.to_datetime(trade.get('exit_timestamp', trade.get('timestamp'))) <= period_end
            ]
            
            trade_stats = self.trade_analyzer.analyze_trades(period_trades)
            
            analytics.append({
                'period_start': period_start,
                'period_end': period_end,
                'period_type': period_type,
                'period_return': period_return,
                'cumulative_return': cumulative_return,
                'trade_count': trade_stats.get('total_trades', 0),
                'win_rate': trade_stats.get('win_rate', 0),
                'profit_factor': trade_stats.get('profit_factor', 0)
            })
        
        return analytics
