"""
Deep Learning module for trading strategy development using TensorFlow/Keras.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, List, Callable
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models, optimizers, callbacks
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import pickle
import os
from datetime import datetime
import talib

from app.core.config import settings
from app.schemas.strategy import DeepLearningConfig, PerformanceMetrics

logger = logging.getLogger(__name__)

# Set TensorFlow logging level
tf.get_logger().setLevel('WARNING')


class DataPreprocessor:
    """Data preprocessing for deep learning models."""

    def __init__(self, sequence_length: int = 60):
        """
        Initialize data preprocessor.

        Args:
            sequence_length: Length of input sequences
        """
        self.sequence_length = sequence_length
        self.feature_scaler = StandardScaler()
        self.target_scaler = MinMaxScaler(feature_range=(-1, 1))
        self.feature_columns = []

    def create_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicators and features."""
        features = pd.DataFrame(index=data.index)

        # Price data
        close = data['close'].values
        high = data['high'].values
        low = data['low'].values
        volume = data['volume'].values

        # Basic price features
        features['close'] = close
        features['high'] = high
        features['low'] = low
        features['volume'] = volume

        # Returns
        features['returns'] = np.log(close / np.roll(close, 1))
        features['returns_5'] = np.log(close / np.roll(close, 5))
        features['returns_10'] = np.log(close / np.roll(close, 10))

        # Moving averages
        features['sma_5'] = talib.SMA(close, timeperiod=5)
        features['sma_10'] = talib.SMA(close, timeperiod=10)
        features['sma_20'] = talib.SMA(close, timeperiod=20)
        features['sma_50'] = talib.SMA(close, timeperiod=50)
        features['ema_10'] = talib.EMA(close, timeperiod=10)
        features['ema_20'] = talib.EMA(close, timeperiod=20)

        # Technical indicators
        features['rsi'] = talib.RSI(close, timeperiod=14)
        features['atr'] = talib.ATR(high, low, close, timeperiod=14)
        features['adx'] = talib.ADX(high, low, close, timeperiod=14)
        features['cci'] = talib.CCI(high, low, close, timeperiod=14)
        features['williams_r'] = talib.WILLR(high, low, close, timeperiod=14)

        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close)
        features['macd'] = macd
        features['macd_signal'] = macd_signal
        features['macd_hist'] = macd_hist

        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20)
        features['bb_upper'] = bb_upper
        features['bb_middle'] = bb_middle
        features['bb_lower'] = bb_lower
        features['bb_width'] = (bb_upper - bb_lower) / bb_middle
        features['bb_position'] = (close - bb_lower) / (bb_upper - bb_lower)

        # Stochastic
        stoch_k, stoch_d = talib.STOCH(high, low, close)
        features['stoch_k'] = stoch_k
        features['stoch_d'] = stoch_d

        # Volume indicators
        features['volume_sma'] = talib.SMA(volume, timeperiod=20)
        features['volume_ratio'] = volume / features['volume_sma']

        # Price patterns
        features['doji'] = talib.CDLDOJI(data['open'], high, low, close)
        features['hammer'] = talib.CDLHAMMER(data['open'], high, low, close)
        features['engulfing'] = talib.CDLENGULFING(data['open'], high, low, close)

        # Volatility
        features['volatility'] = features['returns'].rolling(window=20).std()

        # Momentum
        features['momentum'] = talib.MOM(close, timeperiod=10)
        features['roc'] = talib.ROC(close, timeperiod=10)

        # Fill NaN values
        features.fillna(method='ffill', inplace=True)
        features.fillna(0, inplace=True)

        return features

    def create_sequences(self, features: pd.DataFrame, targets: pd.Series) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for LSTM/GRU models."""
        X, y = [], []

        for i in range(self.sequence_length, len(features)):
            X.append(features.iloc[i-self.sequence_length:i].values)
            y.append(targets.iloc[i])

        return np.array(X), np.array(y)

    def prepare_data(self, data: pd.DataFrame, target_column: str = 'future_return') -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for training."""
        # Create features
        features = self.create_features(data)

        # Create target (future returns)
        if target_column not in data.columns:
            # Create future return target
            future_periods = 5  # Predict 5 periods ahead
            targets = np.log(data['close'].shift(-future_periods) / data['close'])
            targets = targets.fillna(0)
        else:
            targets = data[target_column]

        # Store feature columns
        self.feature_columns = features.columns.tolist()

        # Scale features
        features_scaled = self.feature_scaler.fit_transform(features)
        features_scaled = pd.DataFrame(features_scaled, columns=features.columns, index=features.index)

        # Scale targets
        targets_scaled = self.target_scaler.fit_transform(targets.values.reshape(-1, 1)).flatten()
        targets_scaled = pd.Series(targets_scaled, index=targets.index)

        # Create sequences
        X, y = self.create_sequences(features_scaled, targets_scaled)

        return X, y

    def transform_features(self, data: pd.DataFrame) -> np.ndarray:
        """Transform new data using fitted scalers."""
        features = self.create_features(data)
        features = features[self.feature_columns]  # Ensure same feature order
        features_scaled = self.feature_scaler.transform(features)

        # Create sequence for the latest data point
        if len(features_scaled) >= self.sequence_length:
            sequence = features_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
            return sequence
        else:
            # Pad with zeros if not enough data
            padded = np.zeros((self.sequence_length, len(self.feature_columns)))
            padded[-len(features_scaled):] = features_scaled
            return padded.reshape(1, self.sequence_length, -1)

    def inverse_transform_target(self, scaled_target: np.ndarray) -> np.ndarray:
        """Inverse transform scaled targets."""
        return self.target_scaler.inverse_transform(scaled_target.reshape(-1, 1)).flatten()


class AttentionLayer(layers.Layer):
    """Custom attention layer for time series."""

    def __init__(self, units: int, **kwargs):
        super(AttentionLayer, self).__init__(**kwargs)
        self.units = units
        self.W = None
        self.b = None
        self.u = None

    def build(self, input_shape):
        self.W = self.add_weight(
            shape=(input_shape[-1], self.units),
            initializer='glorot_uniform',
            trainable=True,
            name='attention_W'
        )
        self.b = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            trainable=True,
            name='attention_b'
        )
        self.u = self.add_weight(
            shape=(self.units,),
            initializer='glorot_uniform',
            trainable=True,
            name='attention_u'
        )
        super(AttentionLayer, self).build(input_shape)

    def call(self, inputs):
        # inputs shape: (batch_size, time_steps, features)
        uit = tf.tanh(tf.tensordot(inputs, self.W, axes=1) + self.b)
        ait = tf.tensordot(uit, self.u, axes=1)
        ait = tf.nn.softmax(ait, axis=1)
        ait = tf.expand_dims(ait, axis=-1)
        weighted_input = inputs * ait
        output = tf.reduce_sum(weighted_input, axis=1)
        return output

    def get_config(self):
        config = super(AttentionLayer, self).get_config()
        config.update({'units': self.units})
        return config


class DeepLearningEngine:
    """Deep Learning engine for trading strategy development."""

    def __init__(self, config: Optional[DeepLearningConfig] = None):
        """Initialize DL engine."""
        self.config = config or DeepLearningConfig()
        self.model = None
        self.preprocessor = None
        self.training_history = None

        # Set random seeds for reproducibility
        tf.random.set_seed(42)
        np.random.seed(42)

    async def cleanup(self):
        """Cleanup resources."""
        if self.model:
            del self.model
        tf.keras.backend.clear_session()

    def create_lstm_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Create LSTM-based model."""
        model = models.Sequential([
            layers.LSTM(
                self.config.lstm_units,
                return_sequences=True,
                input_shape=input_shape,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.LSTM(
                self.config.lstm_units // 2,
                return_sequences=True,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.LSTM(
                self.config.lstm_units // 4,
                return_sequences=False,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.Dense(64, activation='relu'),
            layers.Dropout(self.config.dropout_rate),
            layers.Dense(32, activation='relu'),
            layers.Dropout(self.config.dropout_rate),
            layers.Dense(1, activation='tanh')  # Output in [-1, 1] range
        ])

        return model

    def create_gru_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Create GRU-based model."""
        model = models.Sequential([
            layers.GRU(
                self.config.lstm_units,
                return_sequences=True,
                input_shape=input_shape,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.GRU(
                self.config.lstm_units // 2,
                return_sequences=True,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.GRU(
                self.config.lstm_units // 4,
                return_sequences=False,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.dropout_rate
            ),
            layers.Dense(64, activation='relu'),
            layers.Dropout(self.config.dropout_rate),
            layers.Dense(32, activation='relu'),
            layers.Dropout(self.config.dropout_rate),
            layers.Dense(1, activation='tanh')
        ])

        return model

    def create_transformer_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Create Transformer-based model with attention."""
        inputs = layers.Input(shape=input_shape)

        # Multi-head attention
        attention_output = layers.MultiHeadAttention(
            num_heads=self.config.attention_heads,
            key_dim=self.config.lstm_units // self.config.attention_heads
        )(inputs, inputs)

        # Add & Norm
        attention_output = layers.Dropout(self.config.dropout_rate)(attention_output)
        attention_output = layers.LayerNormalization()(inputs + attention_output)

        # Feed forward
        ffn_output = layers.Dense(self.config.lstm_units * 2, activation='relu')(attention_output)
        ffn_output = layers.Dropout(self.config.dropout_rate)(ffn_output)
        ffn_output = layers.Dense(input_shape[-1])(ffn_output)

        # Add & Norm
        ffn_output = layers.LayerNormalization()(attention_output + ffn_output)

        # Global average pooling
        pooled = layers.GlobalAveragePooling1D()(ffn_output)

        # Final layers
        x = layers.Dense(64, activation='relu')(pooled)
        x = layers.Dropout(self.config.dropout_rate)(x)
        x = layers.Dense(32, activation='relu')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        outputs = layers.Dense(1, activation='tanh')(x)

        model = models.Model(inputs=inputs, outputs=outputs)
        return model

    def create_ensemble_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Create ensemble model combining LSTM, GRU, and attention."""
        inputs = layers.Input(shape=input_shape)

        # LSTM branch
        lstm_out = layers.LSTM(
            self.config.lstm_units // 2,
            return_sequences=False,
            dropout=self.config.dropout_rate
        )(inputs)

        # GRU branch
        gru_out = layers.GRU(
            self.config.lstm_units // 2,
            return_sequences=False,
            dropout=self.config.dropout_rate
        )(inputs)

        # Attention branch
        attention_out = AttentionLayer(self.config.lstm_units // 2)(inputs)

        # Combine branches
        combined = layers.Concatenate()([lstm_out, gru_out, attention_out])

        # Final layers
        x = layers.Dense(64, activation='relu')(combined)
        x = layers.Dropout(self.config.dropout_rate)(x)
        x = layers.Dense(32, activation='relu')(x)
        x = layers.Dropout(self.config.dropout_rate)(x)
        outputs = layers.Dense(1, activation='tanh')(x)

        model = models.Model(inputs=inputs, outputs=outputs)
        return model

    def create_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """Create model based on configuration."""
        if self.config.model_type == "LSTM":
            model = self.create_lstm_model(input_shape)
        elif self.config.model_type == "GRU":
            model = self.create_gru_model(input_shape)
        elif self.config.model_type == "Transformer":
            model = self.create_transformer_model(input_shape)
        elif self.config.model_type == "Ensemble":
            model = self.create_ensemble_model(input_shape)
        else:
            raise ValueError(f"Unsupported model type: {self.config.model_type}")

        # Compile model
        optimizer = optimizers.Adam(learning_rate=self.config.learning_rate)
        model.compile(
            optimizer=optimizer,
            loss='mse',
            metrics=['mae', 'mse']
        )

        return model

    def train_strategy(self, training_data: pd.DataFrame,
                      progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Train a trading strategy using deep learning.

        Args:
            training_data: Historical market data for training
            progress_callback: Optional callback for progress updates

        Returns:
            Dict containing training results and performance metrics
        """
        logger.info(f"Starting DL training with {self.config.model_type} for {self.config.epochs} epochs")

        try:
            # Initialize preprocessor
            self.preprocessor = DataPreprocessor(sequence_length=self.config.sequence_length)

            # Prepare data
            X, y = self.preprocessor.prepare_data(training_data)

            # Split data
            from sklearn.model_selection import train_test_split
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )

            logger.info(f"Training data shape: {X_train.shape}, Validation data shape: {X_val.shape}")

            # Create model
            input_shape = (X_train.shape[1], X_train.shape[2])
            self.model = self.create_model(input_shape)

            logger.info(f"Model created with {self.model.count_params()} parameters")

            # Callbacks
            callback_list = [
                callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=20,
                    restore_best_weights=True
                ),
                callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7
                ),
                callbacks.ModelCheckpoint(
                    filepath=os.path.join(settings.MODEL_STORAGE_PATH, 'best_model.h5'),
                    monitor='val_loss',
                    save_best_only=True,
                    save_weights_only=False
                )
            ]

            # Custom callback for progress reporting
            if progress_callback:
                class ProgressCallback(callbacks.Callback):
                    def __init__(self, callback_fn, total_epochs):
                        super().__init__()
                        self.callback_fn = callback_fn
                        self.total_epochs = total_epochs

                    def on_epoch_end(self, epoch, logs=None):
                        self.callback_fn(epoch + 1, self.total_epochs, logs or {})

                callback_list.append(ProgressCallback(progress_callback, self.config.epochs))

            # Train model
            self.training_history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=self.config.epochs,
                batch_size=self.config.batch_size,
                callbacks=callback_list,
                verbose=1
            )

            # Evaluate model
            train_loss = self.model.evaluate(X_train, y_train, verbose=0)
            val_loss = self.model.evaluate(X_val, y_val, verbose=0)

            # Generate predictions for performance evaluation
            y_pred_val = self.model.predict(X_val, verbose=0)

            # Calculate performance metrics
            performance = self._calculate_performance(
                y_val, y_pred_val, training_data.iloc[-len(y_val):]
            )

            return {
                'model_type': self.config.model_type,
                'epochs_trained': len(self.training_history.history['loss']),
                'final_train_loss': float(train_loss[0]) if isinstance(train_loss, list) else float(train_loss),
                'final_val_loss': float(val_loss[0]) if isinstance(val_loss, list) else float(val_loss),
                'performance_metrics': performance.model_dump(),
                'training_history': {
                    'loss': [float(x) for x in self.training_history.history['loss']],
                    'val_loss': [float(x) for x in self.training_history.history['val_loss']],
                    'mae': [float(x) for x in self.training_history.history.get('mae', [])],
                    'val_mae': [float(x) for x in self.training_history.history.get('val_mae', [])]
                },
                'model_params': self.model.count_params(),
                'config': self.config.model_dump()
            }

        except Exception as e:
            logger.error(f"Error during DL training: {e}")
            raise

    def _calculate_performance(self, y_true: np.ndarray, y_pred: np.ndarray,
                             price_data: pd.DataFrame) -> PerformanceMetrics:
        """Calculate performance metrics from predictions."""
        # Convert predictions to trading signals
        signals = np.tanh(y_pred.flatten())  # Ensure signals are in [-1, 1]

        # Simulate trading
        initial_capital = 10000.0
        capital = initial_capital
        position = 0.0
        equity_curve = [initial_capital]

        for i, signal in enumerate(signals[:-1]):  # Exclude last signal
            if i >= len(price_data) - 1:
                break

            current_price = price_data.iloc[i]['close']
            next_price = price_data.iloc[i + 1]['close']

            # Calculate position change
            target_position = signal * 0.1  # Max 10% position
            position_change = target_position - position

            # Execute trade
            if abs(position_change) > 0.01:
                trade_cost = abs(position_change) * capital * 0.001  # 0.1% cost
                capital -= trade_cost
                position = target_position

            # Calculate equity
            position_value = position * capital * next_price
            total_equity = capital + position_value
            equity_curve.append(total_equity)

        # Calculate metrics
        if len(equity_curve) < 2:
            return PerformanceMetrics()

        returns = np.diff(equity_curve) / equity_curve[:-1]
        total_return = (equity_curve[-1] - equity_curve[0]) / equity_curve[0]
        volatility = np.std(returns) * np.sqrt(252)
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0

        # Maximum drawdown
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak
        max_drawdown = np.min(drawdown)

        return PerformanceMetrics(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=abs(max_drawdown),
            volatility=volatility
        )

    def predict_signal(self, current_data: pd.DataFrame) -> float:
        """Generate trading signal using trained model."""
        if not self.model or not self.preprocessor:
            logger.warning("No trained model available for prediction")
            return 0.0

        try:
            # Preprocess data
            X = self.preprocessor.transform_features(current_data)

            # Generate prediction
            prediction = self.model.predict(X, verbose=0)
            signal = float(prediction[0, 0])

            # Ensure signal is in valid range
            signal = np.clip(signal, -1.0, 1.0)

            return signal

        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return 0.0

    def save_strategy(self, strategy_data: Dict[str, Any], filepath: str):
        """Save trained strategy to file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model
        model_path = filepath.replace('.pkl', '_model.h5')
        if self.model:
            self.model.save(model_path)

        # Save preprocessor and metadata
        with open(filepath, 'wb') as f:
            pickle.dump({
                'strategy_data': strategy_data,
                'config': self.config,
                'preprocessor': self.preprocessor,
                'model_path': model_path,
                'training_history': self.training_history.history if self.training_history else None
            }, f)

        logger.info(f"DL strategy saved to {filepath}")

    def load_strategy(self, filepath: str) -> Dict[str, Any]:
        """Load trained strategy from file."""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)

        self.config = data['config']
        self.preprocessor = data['preprocessor']

        # Load model
        model_path = data['model_path']
        if os.path.exists(model_path):
            # Register custom objects
            custom_objects = {'AttentionLayer': AttentionLayer}
            self.model = keras.models.load_model(model_path, custom_objects=custom_objects)

        logger.info(f"DL strategy loaded from {filepath}")
        return data['strategy_data']