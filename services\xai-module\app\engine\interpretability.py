"""
Model interpretability engine for AI paradigm-specific analysis.
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
import warnings

# Deep learning interpretability
try:
    import torch
    import torch.nn as nn
    from captum.attr import IntegratedGradients, GradientShap, DeepLift
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch/Captum not available - install for deep learning interpretability")

# Tree visualization
try:
    from sklearn.tree import export_text, plot_tree
    import graphviz
    TREE_VIZ_AVAILABLE = True
except ImportError:
    TREE_VIZ_AVAILABLE = False
    logging.warning("Tree visualization libraries not available")

from app.core.config import settings
from app.schemas.explanation import AIParadigm

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=UserWarning)


class DeepLearningInterpreter:
    """Interpretability analysis for deep learning models."""
    
    def __init__(self):
        """Initialize deep learning interpreter."""
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available - deep learning interpretability limited")
        
        self.attribution_methods = {
            "integrated_gradients": self._integrated_gradients,
            "gradient_shap": self._gradient_shap,
            "deeplift": self._deeplift,
            "guided_backprop": self._guided_backprop
        }
    
    def analyze_model_interpretability(
        self,
        model: Any,
        input_data: np.ndarray,
        feature_names: List[str],
        analysis_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze deep learning model interpretability."""
        
        try:
            config = analysis_config or {}
            
            results = {
                "model_type": "deep_learning",
                "analysis_methods": [],
                "feature_names": feature_names,
                "input_shape": list(input_data.shape)
            }
            
            if not TORCH_AVAILABLE:
                results["error"] = "PyTorch not available for deep learning analysis"
                return results
            
            # Convert to torch tensor if needed
            if isinstance(input_data, np.ndarray):
                input_tensor = torch.FloatTensor(input_data)
            else:
                input_tensor = input_data
            
            # Gradient-based attribution
            if config.get("include_gradients", True):
                gradient_results = self._analyze_gradients(model, input_tensor, config)
                results["gradient_analysis"] = gradient_results
                results["analysis_methods"].append("gradient_analysis")
            
            # Attention analysis (if applicable)
            if config.get("include_attention", True) and hasattr(model, 'attention'):
                attention_results = self._analyze_attention(model, input_tensor, config)
                results["attention_analysis"] = attention_results
                results["analysis_methods"].append("attention_analysis")
            
            # Layer-wise analysis
            if config.get("include_layer_analysis", True):
                layer_results = self._analyze_layers(model, input_tensor, config)
                results["layer_analysis"] = layer_results
                results["analysis_methods"].append("layer_analysis")
            
            # Activation analysis
            if config.get("include_activations", False):
                activation_results = self._analyze_activations(model, input_tensor, config)
                results["activation_analysis"] = activation_results
                results["analysis_methods"].append("activation_analysis")
            
            return results
            
        except Exception as e:
            logger.error(f"Deep learning interpretability analysis failed: {e}")
            return {
                "error": str(e),
                "model_type": "deep_learning",
                "analysis_methods": []
            }
    
    def _analyze_gradients(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze gradients for feature importance."""
        
        try:
            gradient_method = config.get("gradient_method", settings.DL_GRADIENT_METHOD)
            
            if gradient_method not in self.attribution_methods:
                gradient_method = "integrated_gradients"
            
            # Run gradient analysis
            attribution_results = self.attribution_methods[gradient_method](
                model, input_tensor, config
            )
            
            return {
                "method": gradient_method,
                "attributions": attribution_results,
                "gradient_norm": float(torch.norm(attribution_results).item()) if torch.is_tensor(attribution_results) else 0.0
            }
            
        except Exception as e:
            logger.error(f"Gradient analysis failed: {e}")
            return {"error": str(e), "method": "gradient_analysis"}
    
    def _integrated_gradients(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """Integrated gradients attribution."""
        
        ig = IntegratedGradients(model)
        
        # Create baseline (zeros)
        baseline = torch.zeros_like(input_tensor)
        
        # Calculate attributions
        attributions = ig.attribute(
            input_tensor,
            baseline,
            n_steps=config.get("n_steps", 50)
        )
        
        return attributions
    
    def _gradient_shap(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """Gradient SHAP attribution."""
        
        gs = GradientShap(model)
        
        # Create baseline distribution
        baseline_dist = torch.randn_like(input_tensor.unsqueeze(0).repeat(10, 1))
        
        # Calculate attributions
        attributions = gs.attribute(
            input_tensor,
            baseline_dist,
            n_samples=config.get("n_samples", 50)
        )
        
        return attributions
    
    def _deeplift(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """DeepLIFT attribution."""
        
        dl = DeepLift(model)
        
        # Create baseline
        baseline = torch.zeros_like(input_tensor)
        
        # Calculate attributions
        attributions = dl.attribute(input_tensor, baseline)
        
        return attributions
    
    def _guided_backprop(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> torch.Tensor:
        """Guided backpropagation."""
        
        # Simple gradient calculation (placeholder for guided backprop)
        input_tensor.requires_grad_(True)
        output = model(input_tensor)
        
        if output.dim() > 1:
            output = output.sum()
        
        gradients = torch.autograd.grad(output, input_tensor)[0]
        
        return gradients
    
    def _analyze_attention(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze attention mechanisms."""
        
        try:
            attention_weights = []
            attention_heads = config.get("attention_heads", settings.DL_ATTENTION_HEADS_TO_ANALYZE)
            
            # Hook to capture attention weights
            def attention_hook(module, input, output):
                if hasattr(output, 'attention_weights'):
                    attention_weights.append(output.attention_weights.detach().cpu().numpy())
                elif isinstance(output, tuple) and len(output) > 1:
                    # Assume second element is attention weights
                    attention_weights.append(output[1].detach().cpu().numpy())
            
            # Register hooks
            hooks = []
            for name, module in model.named_modules():
                if 'attention' in name.lower():
                    hook = module.register_forward_hook(attention_hook)
                    hooks.append(hook)
            
            # Forward pass
            with torch.no_grad():
                _ = model(input_tensor)
            
            # Remove hooks
            for hook in hooks:
                hook.remove()
            
            # Process attention weights
            processed_attention = []
            for i, weights in enumerate(attention_weights[:attention_heads]):
                if weights.ndim > 2:
                    # Average over heads/layers
                    avg_weights = np.mean(weights, axis=tuple(range(weights.ndim - 2)))
                else:
                    avg_weights = weights
                
                processed_attention.append({
                    "layer": i,
                    "weights": avg_weights.tolist(),
                    "max_attention": float(np.max(avg_weights)),
                    "attention_entropy": float(-np.sum(avg_weights * np.log(avg_weights + 1e-10)))
                })
            
            return {
                "attention_layers": len(attention_weights),
                "attention_patterns": processed_attention,
                "total_attention_heads": attention_heads
            }
            
        except Exception as e:
            logger.error(f"Attention analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_layers(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze layer-wise representations."""
        
        try:
            layer_outputs = []
            max_layers = config.get("max_layers", settings.MAX_LAYERS_TO_ANALYZE)
            
            # Hook to capture layer outputs
            def layer_hook(module, input, output):
                if torch.is_tensor(output):
                    layer_outputs.append(output.detach().cpu().numpy())
                elif isinstance(output, (list, tuple)):
                    layer_outputs.append(output[0].detach().cpu().numpy())
            
            # Register hooks on key layers
            hooks = []
            layer_count = 0
            for name, module in model.named_modules():
                if layer_count >= max_layers:
                    break
                
                if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d, nn.LSTM, nn.GRU)):
                    hook = module.register_forward_hook(layer_hook)
                    hooks.append(hook)
                    layer_count += 1
            
            # Forward pass
            with torch.no_grad():
                _ = model(input_tensor)
            
            # Remove hooks
            for hook in hooks:
                hook.remove()
            
            # Analyze layer outputs
            layer_analysis = []
            for i, output in enumerate(layer_outputs):
                analysis = {
                    "layer": i,
                    "shape": list(output.shape),
                    "mean_activation": float(np.mean(output)),
                    "std_activation": float(np.std(output)),
                    "sparsity": float(np.mean(output == 0)),
                    "max_activation": float(np.max(output)),
                    "min_activation": float(np.min(output))
                }
                layer_analysis.append(analysis)
            
            return {
                "layers_analyzed": len(layer_outputs),
                "layer_statistics": layer_analysis
            }
            
        except Exception as e:
            logger.error(f"Layer analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_activations(
        self,
        model: Any,
        input_tensor: torch.Tensor,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze activation patterns."""
        
        try:
            activations = {}
            
            # Hook to capture activations
            def activation_hook(name):
                def hook(module, input, output):
                    if torch.is_tensor(output):
                        activations[name] = output.detach().cpu().numpy()
                return hook
            
            # Register hooks
            hooks = []
            for name, module in model.named_modules():
                if isinstance(module, (nn.ReLU, nn.Sigmoid, nn.Tanh, nn.LeakyReLU)):
                    hook = module.register_forward_hook(activation_hook(name))
                    hooks.append(hook)
            
            # Forward pass
            with torch.no_grad():
                _ = model(input_tensor)
            
            # Remove hooks
            for hook in hooks:
                hook.remove()
            
            # Analyze activations
            activation_analysis = {}
            for name, activation in activations.items():
                activation_analysis[name] = {
                    "mean": float(np.mean(activation)),
                    "std": float(np.std(activation)),
                    "sparsity": float(np.mean(activation == 0)),
                    "saturation": float(np.mean(activation >= 0.99)) if "relu" not in name.lower() else 0.0
                }
            
            return {
                "activation_functions": list(activations.keys()),
                "activation_statistics": activation_analysis
            }
            
        except Exception as e:
            logger.error(f"Activation analysis failed: {e}")
            return {"error": str(e)}


class GeneticProgrammingInterpreter:
    """Interpretability analysis for genetic programming models."""
    
    def __init__(self):
        """Initialize GP interpreter."""
        pass
    
    def analyze_model_interpretability(
        self,
        model: Any,
        feature_names: List[str],
        analysis_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze genetic programming model interpretability."""
        
        try:
            config = analysis_config or {}
            
            results = {
                "model_type": "genetic_programming",
                "analysis_methods": [],
                "feature_names": feature_names
            }
            
            # Tree structure analysis
            if hasattr(model, 'tree') or hasattr(model, 'program'):
                tree_results = self._analyze_tree_structure(model, feature_names, config)
                results["tree_analysis"] = tree_results
                results["analysis_methods"].append("tree_analysis")
            
            # Feature usage analysis
            feature_usage = self._analyze_feature_usage(model, feature_names, config)
            results["feature_usage"] = feature_usage
            results["analysis_methods"].append("feature_usage")
            
            # Complexity analysis
            complexity_results = self._analyze_complexity(model, config)
            results["complexity_analysis"] = complexity_results
            results["analysis_methods"].append("complexity_analysis")
            
            return results
            
        except Exception as e:
            logger.error(f"GP interpretability analysis failed: {e}")
            return {
                "error": str(e),
                "model_type": "genetic_programming",
                "analysis_methods": []
            }
    
    def _analyze_tree_structure(
        self,
        model: Any,
        feature_names: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze tree structure of GP model."""
        
        try:
            # Extract tree representation
            if hasattr(model, 'program'):
                program = model.program
            elif hasattr(model, 'tree'):
                program = model.tree
            else:
                return {"error": "No tree structure found"}
            
            # Convert to string representation
            tree_str = str(program)
            
            # Analyze tree properties
            max_depth = config.get("max_depth", settings.GP_TREE_VISUALIZATION_DEPTH)
            
            # Count nodes and operations
            node_count = len(str(program).split())
            
            # Extract operations
            operations = []
            if hasattr(program, 'program'):
                for node in program.program:
                    if hasattr(node, 'name'):
                        operations.append(node.name)
            
            # Feature frequency analysis
            feature_frequency = {}
            for feature in feature_names:
                feature_frequency[feature] = tree_str.count(feature)
            
            return {
                "tree_string": tree_str,
                "node_count": node_count,
                "max_depth": max_depth,
                "operations": operations,
                "feature_frequency": feature_frequency,
                "most_used_features": sorted(
                    feature_frequency.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:10]
            }
            
        except Exception as e:
            logger.error(f"Tree structure analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_feature_usage(
        self,
        model: Any,
        feature_names: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze feature usage patterns in GP model."""
        
        try:
            # Get feature importance based on frequency
            importance_method = config.get("importance_method", settings.GP_FEATURE_IMPORTANCE_METHOD)
            
            if importance_method == "frequency":
                # Count feature occurrences in tree
                tree_str = str(model)
                feature_counts = {}
                
                for feature in feature_names:
                    count = tree_str.count(feature)
                    feature_counts[feature] = count
                
                # Normalize to get importance scores
                total_count = sum(feature_counts.values())
                feature_importance = {}
                
                if total_count > 0:
                    for feature, count in feature_counts.items():
                        feature_importance[feature] = count / total_count
                else:
                    feature_importance = {feature: 0.0 for feature in feature_names}
            
            else:
                # Default to equal importance
                feature_importance = {feature: 1.0 / len(feature_names) for feature in feature_names}
            
            # Sort by importance
            sorted_features = sorted(
                feature_importance.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            return {
                "importance_method": importance_method,
                "feature_importance": feature_importance,
                "ranked_features": sorted_features,
                "used_features": [f for f, imp in feature_importance.items() if imp > 0],
                "unused_features": [f for f, imp in feature_importance.items() if imp == 0]
            }
            
        except Exception as e:
            logger.error(f"Feature usage analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_complexity(
        self,
        model: Any,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze model complexity."""
        
        try:
            tree_str = str(model)
            
            # Basic complexity metrics
            complexity_metrics = {
                "tree_length": len(tree_str),
                "node_count": len(tree_str.split()),
                "operator_count": tree_str.count('+') + tree_str.count('-') + tree_str.count('*') + tree_str.count('/'),
                "parentheses_depth": self._calculate_parentheses_depth(tree_str)
            }
            
            # Estimate computational complexity
            complexity_metrics["estimated_complexity"] = (
                complexity_metrics["node_count"] * 0.3 +
                complexity_metrics["operator_count"] * 0.5 +
                complexity_metrics["parentheses_depth"] * 0.2
            )
            
            return complexity_metrics
            
        except Exception as e:
            logger.error(f"Complexity analysis failed: {e}")
            return {"error": str(e)}
    
    def _calculate_parentheses_depth(self, tree_str: str) -> int:
        """Calculate maximum parentheses depth."""
        max_depth = 0
        current_depth = 0
        
        for char in tree_str:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        
        return max_depth


class ReinforcementLearningInterpreter:
    """Interpretability analysis for reinforcement learning models."""
    
    def __init__(self):
        """Initialize RL interpreter."""
        pass
    
    def analyze_model_interpretability(
        self,
        model: Any,
        state_names: List[str],
        action_names: List[str],
        analysis_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze reinforcement learning model interpretability."""
        
        try:
            config = analysis_config or {}
            
            results = {
                "model_type": "reinforcement_learning",
                "analysis_methods": [],
                "state_names": state_names,
                "action_names": action_names
            }
            
            # Action value analysis
            if config.get("include_action_values", True):
                action_results = self._analyze_action_values(model, state_names, action_names, config)
                results["action_value_analysis"] = action_results
                results["analysis_methods"].append("action_value_analysis")
            
            # State importance analysis
            if config.get("include_state_importance", True):
                state_results = self._analyze_state_importance(model, state_names, config)
                results["state_importance"] = state_results
                results["analysis_methods"].append("state_importance")
            
            # Policy analysis
            if config.get("include_policy_analysis", True):
                policy_results = self._analyze_policy(model, state_names, action_names, config)
                results["policy_analysis"] = policy_results
                results["analysis_methods"].append("policy_analysis")
            
            return results
            
        except Exception as e:
            logger.error(f"RL interpretability analysis failed: {e}")
            return {
                "error": str(e),
                "model_type": "reinforcement_learning",
                "analysis_methods": []
            }
    
    def _analyze_action_values(
        self,
        model: Any,
        state_names: List[str],
        action_names: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze action value patterns."""
        
        try:
            # This is a simplified analysis - would need actual RL model interface
            action_analysis = {
                "action_preferences": {},
                "state_action_values": {},
                "action_frequency": {}
            }
            
            # Placeholder analysis
            for action in action_names:
                action_analysis["action_preferences"][action] = np.random.random()
                action_analysis["action_frequency"][action] = np.random.randint(0, 100)
            
            return action_analysis
            
        except Exception as e:
            logger.error(f"Action value analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_state_importance(
        self,
        model: Any,
        state_names: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze state importance for decision making."""
        
        try:
            importance_method = config.get("importance_method", settings.RL_STATE_IMPORTANCE_METHOD)
            
            # Placeholder state importance analysis
            state_importance = {}
            for state in state_names:
                state_importance[state] = np.random.random()
            
            # Sort by importance
            sorted_states = sorted(
                state_importance.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            return {
                "importance_method": importance_method,
                "state_importance": state_importance,
                "ranked_states": sorted_states
            }
            
        except Exception as e:
            logger.error(f"State importance analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_policy(
        self,
        model: Any,
        state_names: List[str],
        action_names: List[str],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze policy patterns."""
        
        try:
            # Placeholder policy analysis
            policy_analysis = {
                "policy_entropy": np.random.random(),
                "action_distribution": {},
                "state_action_preferences": {}
            }
            
            for action in action_names:
                policy_analysis["action_distribution"][action] = np.random.random()
            
            return policy_analysis
            
        except Exception as e:
            logger.error(f"Policy analysis failed: {e}")
            return {"error": str(e)}


class ModelInterpreter:
    """Main model interpreter coordinating paradigm-specific analysis."""
    
    def __init__(self):
        """Initialize model interpreter."""
        self.dl_interpreter = DeepLearningInterpreter()
        self.gp_interpreter = GeneticProgrammingInterpreter()
        self.rl_interpreter = ReinforcementLearningInterpreter()
    
    async def cleanup(self):
        """Cleanup interpreter resources."""
        pass
    
    async def interpret_model(
        self,
        model: Any,
        ai_paradigm: AIParadigm,
        feature_names: List[str],
        input_data: Optional[np.ndarray] = None,
        analysis_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Interpret model based on AI paradigm."""
        
        try:
            config = analysis_config or {}
            
            # Route to appropriate interpreter
            if ai_paradigm == AIParadigm.DL:
                if input_data is None:
                    return {"error": "Input data required for deep learning interpretation"}
                
                results = self.dl_interpreter.analyze_model_interpretability(
                    model, input_data, feature_names, config
                )
            
            elif ai_paradigm == AIParadigm.GP:
                results = self.gp_interpreter.analyze_model_interpretability(
                    model, feature_names, config
                )
            
            elif ai_paradigm == AIParadigm.RL:
                action_names = config.get("action_names", ["BUY", "SELL", "HOLD"])
                results = self.rl_interpreter.analyze_model_interpretability(
                    model, feature_names, action_names, config
                )
            
            else:
                results = {"error": f"AI paradigm {ai_paradigm} not supported"}
            
            # Add common metadata
            results.update({
                "ai_paradigm": ai_paradigm.value,
                "interpretation_timestamp": datetime.utcnow().isoformat(),
                "feature_count": len(feature_names)
            })
            
            return results
            
        except Exception as e:
            logger.error(f"Model interpretation failed: {e}")
            return {
                "error": str(e),
                "ai_paradigm": ai_paradigm.value
            }
