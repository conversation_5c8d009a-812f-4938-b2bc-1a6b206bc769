"""
Authentication router for login, logout, and token management.
"""

import logging
from datetime import timed<PERSON><PERSON>
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON>er, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.auth import verify_password, create_access_token, get_user_id_from_token
from app.core.config import settings
from app.models.user import User
from app.schemas.user import UserLogin, Token, UserProfile

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=Token)
async def login(
    user_credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user and return access token.
    
    Args:
        user_credentials: User login credentials
        db: Database session
        
    Returns:
        Token: Access token and metadata
        
    Raises:
        HTTPException: If authentication fails
    """
    # Find user by username or email
    stmt = select(User).where(
        (User.username == user_credentials.username) | 
        (User.email == user_credentials.username)
    )
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        logger.warning(f"Login attempt with invalid username: {user_credentials.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Verify password
    if not verify_password(user_credentials.password, user.hashed_password):
        logger.warning(f"Login attempt with invalid password for user: {user.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Check if user is active
    if not user.is_active:
        logger.warning(f"Login attempt for inactive user: {user.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires
    )
    
    logger.info(f"User logged in successfully: {user.username}")
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/logout")
async def logout():
    """
    Logout user (client-side token removal).
    
    Note: In a stateless JWT implementation, logout is handled client-side
    by removing the token. For enhanced security, implement token blacklisting.
    """
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserProfile)
async def get_current_user(
    token: str = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Get current authenticated user profile.
    
    Args:
        token: Bearer token
        db: Database session
        
    Returns:
        UserProfile: Current user profile
        
    Raises:
        HTTPException: If user not found or token invalid
    """
    # Extract user ID from token
    user_id = get_user_id_from_token(token.credentials)
    
    # Get user from database
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is inactive"
        )
    
    return UserProfile.from_orm(user)


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token: str = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token.
    
    Args:
        token: Current bearer token
        db: Database session
        
    Returns:
        Token: New access token
        
    Raises:
        HTTPException: If token invalid or user not found
    """
    # Extract user ID from current token
    user_id = get_user_id_from_token(token.credentials)
    
    # Verify user still exists and is active
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new access token
    access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires
    )
    
    logger.info(f"Token refreshed for user: {user.username}")
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }
