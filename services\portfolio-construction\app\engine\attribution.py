"""
Performance attribution and analytics engine for portfolio analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal
from sklearn.linear_model import LinearRegression
from sklearn.decomposition import PCA
import warnings

from app.core.config import settings

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=UserWarning)


class StrategyContributionAnalyzer:
    """Analyze strategy-level contribution to portfolio returns."""
    
    def __init__(self):
        """Initialize strategy contribution analyzer."""
        pass
    
    def calculate_strategy_contributions(
        self,
        portfolio_returns: pd.Series,
        strategy_returns: Dict[str, pd.Series],
        weights: Dict[str, pd.Series]
    ) -> Dict[str, Dict[str, float]]:
        """Calculate strategy contributions to portfolio performance."""
        
        contributions = {}
        
        # Align all series to common dates
        common_dates = portfolio_returns.index
        for strategy in strategy_returns:
            common_dates = common_dates.intersection(strategy_returns[strategy].index)
            if strategy in weights:
                common_dates = common_dates.intersection(weights[strategy].index)
        
        if len(common_dates) < 2:
            return {}
        
        # Calculate contributions for each strategy
        for strategy in strategy_returns:
            if strategy not in weights:
                continue
            
            strategy_rets = strategy_returns[strategy].reindex(common_dates)
            strategy_weights = weights[strategy].reindex(common_dates)
            
            # Strategy contribution = weight * strategy return
            strategy_contribution = strategy_weights * strategy_rets
            
            # Performance metrics
            total_contribution = strategy_contribution.sum()
            avg_contribution = strategy_contribution.mean()
            contribution_volatility = strategy_contribution.std()
            
            # Risk-adjusted contribution
            if contribution_volatility > 0:
                contribution_sharpe = avg_contribution / contribution_volatility * np.sqrt(252)
            else:
                contribution_sharpe = 0.0
            
            # Correlation with portfolio
            portfolio_aligned = portfolio_returns.reindex(common_dates)
            correlation = strategy_contribution.corr(portfolio_aligned)
            
            contributions[strategy] = {
                "total_contribution": total_contribution,
                "avg_contribution": avg_contribution,
                "contribution_volatility": contribution_volatility,
                "contribution_sharpe": contribution_sharpe,
                "correlation_with_portfolio": correlation if not np.isnan(correlation) else 0.0,
                "contribution_percentage": total_contribution / portfolio_returns.sum() if portfolio_returns.sum() != 0 else 0.0
            }
        
        return contributions
    
    def calculate_marginal_contributions(
        self,
        strategy_returns: Dict[str, pd.Series],
        weights: Dict[str, float],
        covariance_matrix: np.ndarray,
        strategy_names: List[str]
    ) -> Dict[str, float]:
        """Calculate marginal contribution to portfolio risk."""
        
        # Convert weights to array
        weight_array = np.array([weights.get(name, 0.0) for name in strategy_names])
        
        # Portfolio variance
        portfolio_variance = np.dot(weight_array, np.dot(covariance_matrix, weight_array))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        if portfolio_volatility == 0:
            return {name: 0.0 for name in strategy_names}
        
        # Marginal contribution to risk
        marginal_contributions = np.dot(covariance_matrix, weight_array) / portfolio_volatility
        
        return {
            strategy_names[i]: marginal_contributions[i]
            for i in range(len(strategy_names))
        }


class BrinsonFachlerAttribution:
    """Brinson-Fachler attribution analysis for allocation vs selection effects."""
    
    def __init__(self):
        """Initialize Brinson-Fachler attribution analyzer."""
        pass
    
    def calculate_attribution(
        self,
        portfolio_weights: Dict[str, float],
        benchmark_weights: Dict[str, float],
        portfolio_returns: Dict[str, float],
        benchmark_returns: Dict[str, float]
    ) -> Dict[str, Any]:
        """Calculate Brinson-Fachler attribution effects."""
        
        # Ensure all strategies are represented
        all_strategies = set(portfolio_weights.keys()) | set(benchmark_weights.keys())
        
        allocation_effect = 0.0
        selection_effect = 0.0
        interaction_effect = 0.0
        
        strategy_attribution = {}
        
        for strategy in all_strategies:
            wp = portfolio_weights.get(strategy, 0.0)  # Portfolio weight
            wb = benchmark_weights.get(strategy, 0.0)  # Benchmark weight
            rp = portfolio_returns.get(strategy, 0.0)  # Portfolio return
            rb = benchmark_returns.get(strategy, 0.0)  # Benchmark return
            
            # Allocation effect: (wp - wb) * rb
            alloc_effect = (wp - wb) * rb
            
            # Selection effect: wb * (rp - rb)
            select_effect = wb * (rp - rb)
            
            # Interaction effect: (wp - wb) * (rp - rb)
            interact_effect = (wp - wb) * (rp - rb)
            
            strategy_attribution[strategy] = {
                "allocation_effect": alloc_effect,
                "selection_effect": select_effect,
                "interaction_effect": interact_effect,
                "total_effect": alloc_effect + select_effect + interact_effect
            }
            
            allocation_effect += alloc_effect
            selection_effect += select_effect
            interaction_effect += interact_effect
        
        total_attribution = allocation_effect + selection_effect + interaction_effect
        
        return {
            "allocation_effect": allocation_effect,
            "selection_effect": selection_effect,
            "interaction_effect": interaction_effect,
            "total_attribution": total_attribution,
            "strategy_attribution": strategy_attribution
        }


class RiskFactorDecomposition:
    """Risk factor decomposition and style analysis."""
    
    def __init__(self):
        """Initialize risk factor decomposition analyzer."""
        self.factor_models = {
            "CAPM": self._capm_model,
            "FAMA_FRENCH_3": self._fama_french_3_model,
            "CUSTOM": self._custom_factor_model
        }
    
    def decompose_portfolio_risk(
        self,
        portfolio_returns: pd.Series,
        factor_returns: Dict[str, pd.Series],
        model_type: str = "FAMA_FRENCH_3"
    ) -> Dict[str, Any]:
        """Decompose portfolio risk into factor exposures."""
        
        if model_type not in self.factor_models:
            model_type = "CAPM"
        
        return self.factor_models[model_type](portfolio_returns, factor_returns)
    
    def _capm_model(
        self,
        portfolio_returns: pd.Series,
        factor_returns: Dict[str, pd.Series]
    ) -> Dict[str, Any]:
        """Single-factor CAPM model."""
        
        market_returns = factor_returns.get("MARKET", pd.Series())
        risk_free_rate = factor_returns.get("RISK_FREE", pd.Series())
        
        if len(market_returns) == 0:
            return {"error": "Market returns not available for CAPM model"}
        
        # Align data
        common_dates = portfolio_returns.index.intersection(market_returns.index)
        if len(risk_free_rate) > 0:
            common_dates = common_dates.intersection(risk_free_rate.index)
        
        if len(common_dates) < 10:
            return {"error": "Insufficient data for CAPM model"}
        
        portfolio_excess = portfolio_returns.reindex(common_dates)
        market_excess = market_returns.reindex(common_dates)
        
        if len(risk_free_rate) > 0:
            rf = risk_free_rate.reindex(common_dates)
            portfolio_excess = portfolio_excess - rf
            market_excess = market_excess - rf
        
        # Linear regression
        X = market_excess.values.reshape(-1, 1)
        y = portfolio_excess.values
        
        model = LinearRegression()
        model.fit(X, y)
        
        beta = model.coef_[0]
        alpha = model.intercept_
        r_squared = model.score(X, y)
        
        # Calculate residual risk
        predicted = model.predict(X)
        residuals = y - predicted
        residual_volatility = np.std(residuals) * np.sqrt(252)
        
        return {
            "model": "CAPM",
            "alpha": alpha * 252,  # Annualized
            "beta": beta,
            "r_squared": r_squared,
            "residual_volatility": residual_volatility,
            "systematic_risk": beta * np.std(market_excess) * np.sqrt(252),
            "idiosyncratic_risk": residual_volatility
        }
    
    def _fama_french_3_model(
        self,
        portfolio_returns: pd.Series,
        factor_returns: Dict[str, pd.Series]
    ) -> Dict[str, Any]:
        """Three-factor Fama-French model."""
        
        required_factors = ["MARKET", "SMB", "HML"]
        available_factors = [f for f in required_factors if f in factor_returns]
        
        if len(available_factors) < 3:
            # Fall back to CAPM if not all factors available
            return self._capm_model(portfolio_returns, factor_returns)
        
        # Align data
        common_dates = portfolio_returns.index
        for factor in required_factors:
            common_dates = common_dates.intersection(factor_returns[factor].index)
        
        if len(common_dates) < 20:
            return {"error": "Insufficient data for Fama-French model"}
        
        portfolio_excess = portfolio_returns.reindex(common_dates)
        
        # Risk-free rate adjustment
        if "RISK_FREE" in factor_returns:
            rf = factor_returns["RISK_FREE"].reindex(common_dates)
            portfolio_excess = portfolio_excess - rf
        
        # Prepare factor matrix
        X = np.column_stack([
            factor_returns[factor].reindex(common_dates).values
            for factor in required_factors
        ])
        y = portfolio_excess.values
        
        # Linear regression
        model = LinearRegression()
        model.fit(X, y)
        
        coefficients = model.coef_
        alpha = model.intercept_
        r_squared = model.score(X, y)
        
        # Calculate factor contributions
        factor_contributions = {}
        for i, factor in enumerate(required_factors):
            factor_vol = np.std(factor_returns[factor].reindex(common_dates)) * np.sqrt(252)
            factor_contributions[factor] = {
                "loading": coefficients[i],
                "contribution_to_risk": abs(coefficients[i]) * factor_vol
            }
        
        # Residual analysis
        predicted = model.predict(X)
        residuals = y - predicted
        residual_volatility = np.std(residuals) * np.sqrt(252)
        
        return {
            "model": "FAMA_FRENCH_3",
            "alpha": alpha * 252,  # Annualized
            "r_squared": r_squared,
            "factor_loadings": {
                "MARKET": coefficients[0],
                "SMB": coefficients[1],
                "HML": coefficients[2]
            },
            "factor_contributions": factor_contributions,
            "residual_volatility": residual_volatility
        }
    
    def _custom_factor_model(
        self,
        portfolio_returns: pd.Series,
        factor_returns: Dict[str, pd.Series]
    ) -> Dict[str, Any]:
        """Custom factor model using available factors."""
        
        if len(factor_returns) == 0:
            return {"error": "No factors available for custom model"}
        
        # Use all available factors
        common_dates = portfolio_returns.index
        for factor_series in factor_returns.values():
            common_dates = common_dates.intersection(factor_series.index)
        
        if len(common_dates) < 10:
            return {"error": "Insufficient data for custom factor model"}
        
        portfolio_excess = portfolio_returns.reindex(common_dates)
        
        # Prepare factor matrix
        factor_names = list(factor_returns.keys())
        X = np.column_stack([
            factor_returns[factor].reindex(common_dates).values
            for factor in factor_names
        ])
        y = portfolio_excess.values
        
        # Linear regression
        model = LinearRegression()
        model.fit(X, y)
        
        coefficients = model.coef_
        alpha = model.intercept_
        r_squared = model.score(X, y)
        
        # Factor analysis
        factor_loadings = {factor_names[i]: coefficients[i] for i in range(len(factor_names))}
        
        return {
            "model": "CUSTOM",
            "alpha": alpha * 252,
            "r_squared": r_squared,
            "factor_loadings": factor_loadings,
            "num_factors": len(factor_names)
        }


class CorrelationAnalyzer:
    """Analyze correlation and diversification benefits."""
    
    def __init__(self):
        """Initialize correlation analyzer."""
        pass
    
    def analyze_diversification_benefits(
        self,
        strategy_returns: Dict[str, pd.Series],
        weights: Dict[str, float]
    ) -> Dict[str, Any]:
        """Analyze portfolio diversification benefits."""
        
        # Align returns data
        returns_df = pd.DataFrame(strategy_returns)
        returns_df = returns_df.dropna()
        
        if len(returns_df) < 10:
            return {"error": "Insufficient data for diversification analysis"}
        
        # Calculate correlation matrix
        correlation_matrix = returns_df.corr()
        
        # Portfolio weights
        weight_vector = np.array([weights.get(col, 0.0) for col in returns_df.columns])
        
        # Individual strategy volatilities
        individual_vols = returns_df.std() * np.sqrt(252)
        
        # Weighted average volatility
        weighted_avg_vol = np.dot(weight_vector, individual_vols)
        
        # Portfolio volatility
        portfolio_returns = (returns_df * weight_vector).sum(axis=1)
        portfolio_vol = portfolio_returns.std() * np.sqrt(252)
        
        # Diversification ratio
        diversification_ratio = weighted_avg_vol / portfolio_vol if portfolio_vol > 0 else 0
        
        # Effective number of strategies
        herfindahl_index = np.sum(weight_vector ** 2)
        effective_strategies = 1.0 / herfindahl_index if herfindahl_index > 0 else 0
        
        # Average correlation
        n = len(correlation_matrix)
        if n > 1:
            avg_correlation = (correlation_matrix.sum().sum() - n) / (n * (n - 1))
        else:
            avg_correlation = 0.0
        
        # Correlation contribution to risk
        correlation_contribution = 1 - (portfolio_vol / weighted_avg_vol) ** 2 if weighted_avg_vol > 0 else 0
        
        return {
            "diversification_ratio": diversification_ratio,
            "effective_strategies": effective_strategies,
            "average_correlation": avg_correlation,
            "correlation_contribution_to_risk": correlation_contribution,
            "portfolio_volatility": portfolio_vol,
            "weighted_average_volatility": weighted_avg_vol,
            "volatility_reduction": (weighted_avg_vol - portfolio_vol) / weighted_avg_vol if weighted_avg_vol > 0 else 0
        }
    
    def calculate_rolling_correlations(
        self,
        strategy_returns: Dict[str, pd.Series],
        window: int = 60
    ) -> Dict[str, pd.Series]:
        """Calculate rolling correlations between strategies."""
        
        returns_df = pd.DataFrame(strategy_returns)
        returns_df = returns_df.dropna()
        
        rolling_correlations = {}
        strategies = list(returns_df.columns)
        
        for i in range(len(strategies)):
            for j in range(i + 1, len(strategies)):
                pair_name = f"{strategies[i]}_{strategies[j]}"
                rolling_corr = returns_df[strategies[i]].rolling(window).corr(returns_df[strategies[j]])
                rolling_correlations[pair_name] = rolling_corr
        
        return rolling_correlations


class PerformanceAttributor:
    """Main performance attribution coordinator."""
    
    def __init__(self):
        """Initialize performance attributor."""
        self.strategy_analyzer = StrategyContributionAnalyzer()
        self.brinson_fachler = BrinsonFachlerAttribution()
        self.factor_decomposer = RiskFactorDecomposition()
        self.correlation_analyzer = CorrelationAnalyzer()
    
    async def cleanup(self):
        """Cleanup performance attributor resources."""
        pass
    
    def generate_comprehensive_attribution(
        self,
        portfolio_data: Dict[str, Any],
        benchmark_data: Optional[Dict[str, Any]] = None,
        factor_data: Optional[Dict[str, pd.Series]] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive performance attribution analysis."""
        
        try:
            attribution_report = {
                "timestamp": datetime.utcnow().isoformat(),
                "portfolio_id": portfolio_data.get("portfolio_id"),
                "analysis_period": portfolio_data.get("analysis_period", {}),
                "strategy_contributions": {},
                "brinson_fachler": {},
                "factor_decomposition": {},
                "diversification_analysis": {},
                "summary": {}
            }
            
            # Extract data
            portfolio_returns = portfolio_data.get("portfolio_returns", pd.Series())
            strategy_returns = portfolio_data.get("strategy_returns", {})
            weights = portfolio_data.get("weights", {})
            
            if len(portfolio_returns) == 0 or len(strategy_returns) == 0:
                attribution_report["error"] = "Insufficient data for attribution analysis"
                return attribution_report
            
            # 1. Strategy contribution analysis
            if isinstance(weights, dict):
                # Convert static weights to time series
                weight_series = {
                    strategy: pd.Series(weight, index=portfolio_returns.index)
                    for strategy, weight in weights.items()
                }
            else:
                weight_series = weights
            
            strategy_contributions = self.strategy_analyzer.calculate_strategy_contributions(
                portfolio_returns, strategy_returns, weight_series
            )
            attribution_report["strategy_contributions"] = strategy_contributions
            
            # 2. Brinson-Fachler attribution (if benchmark available)
            if benchmark_data:
                benchmark_weights = benchmark_data.get("weights", {})
                benchmark_returns = benchmark_data.get("strategy_returns", {})
                
                # Calculate period returns for each strategy
                portfolio_period_returns = {
                    strategy: strategy_returns[strategy].sum()
                    for strategy in strategy_returns
                }
                benchmark_period_returns = {
                    strategy: benchmark_returns.get(strategy, pd.Series()).sum()
                    for strategy in strategy_returns
                }
                
                brinson_attribution = self.brinson_fachler.calculate_attribution(
                    weights, benchmark_weights,
                    portfolio_period_returns, benchmark_period_returns
                )
                attribution_report["brinson_fachler"] = brinson_attribution
            
            # 3. Factor decomposition (if factor data available)
            if factor_data:
                factor_analysis = self.factor_decomposer.decompose_portfolio_risk(
                    portfolio_returns, factor_data
                )
                attribution_report["factor_decomposition"] = factor_analysis
            
            # 4. Diversification analysis
            if isinstance(weights, dict):
                diversification_analysis = self.correlation_analyzer.analyze_diversification_benefits(
                    strategy_returns, weights
                )
                attribution_report["diversification_analysis"] = diversification_analysis
            
            # 5. Summary metrics
            total_return = portfolio_returns.sum()
            portfolio_vol = portfolio_returns.std() * np.sqrt(252)
            sharpe_ratio = total_return / portfolio_vol if portfolio_vol > 0 else 0
            
            attribution_report["summary"] = {
                "total_return": total_return,
                "portfolio_volatility": portfolio_vol,
                "sharpe_ratio": sharpe_ratio,
                "number_of_strategies": len([w for w in weights.values() if w > 0.001]) if isinstance(weights, dict) else 0,
                "analysis_period_days": len(portfolio_returns)
            }
            
            return attribution_report
            
        except Exception as e:
            logger.error(f"Error generating performance attribution: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
