# AthenaTrader

AI-native trading platform with primary focus on FOREX markets - Expert trader private platform

## Overview

AthenaTrader is a sophisticated AI-driven trading platform designed for expert algorithmic traders. The platform provides comprehensive tools for strategy development, backtesting, portfolio optimization, and live execution with explainable AI at its core.

### Key Features

- **Multi-Paradigm AI**: Genetic Programming, Reinforcement Learning, Deep Learning, and Hybrid approaches
- **Explainable AI (XAI)**: Comprehensive explainability framework for regulatory compliance and user trust
- **Advanced Backtesting**: High-fidelity historical simulations with realistic market friction modeling
- **Portfolio Optimization**: Sophisticated multi-strategy portfolio construction and risk management
- **Live Execution**: Multi-broker execution gateway with advanced risk controls
- **FOREX Focus**: Primary focus on FOREX markets with support for other financial instruments

## Architecture

AthenaTrader follows a modular microservice architecture with 7 core services:

1. **Data Nexus**: Central data management and processing
2. **Strategy Genesis**: AI-driven strategy creation and optimization
3. **Backtesting Engine**: Comprehensive strategy validation and simulation
4. **Portfolio Construction**: Multi-strategy portfolio optimization
5. **XAI Module**: AI explainability and trust framework
6. **Execution Engine**: Live trading execution and monitoring
7. **Learning Hub**: Knowledge management and collaboration

## Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Frontend**: React 18+ with TypeScript
- **Database**: PostgreSQL with TimescaleDB extension
- **AI/ML**: scikit-learn, DEAP, TensorFlow, SHAP/LIME
- **Deployment**: Docker, Kubernetes
- **Cloud**: AWS (ECS, RDS, S3, ElastiCache)

## Getting Started

### Prerequisites

- Python 3.11+
- Node.js 18+
- Docker and Docker Compose
- PostgreSQL 15+ with TimescaleDB

### Installation

```bash
# Clone the repository
git clone https://github.com/Foxernn/athena-trader.git
cd athena-trader

# Set up development environment
./scripts/setup/dev-setup.sh

# Start services
docker-compose up -d
```

## Project Structure

```
athena-trader/
├── services/                 # Backend microservices
│   ├── data-nexus/          # Data management service
│   ├── strategy-genesis/    # AI strategy creation
│   ├── backtesting-engine/  # Strategy validation
│   ├── portfolio-optimization/ # Portfolio construction
│   ├── xai/                 # AI explainability
│   ├── execution-engine/    # Live trading execution
│   ├── learning-hub/        # Knowledge management
│   └── api-gateway/         # API gateway service
├── libs/                    # Shared libraries
├── frontend/                # React frontend application
├── infra/                   # Infrastructure as code
├── scripts/                 # Utility scripts
└── tests/                   # End-to-end tests
```

## Development

### Running Tests

```bash
# Run all tests
pytest

# Run specific service tests
pytest services/data-nexus/tests/

# Run frontend tests
cd frontend && npm test
```

### Code Quality

- **Test Coverage**: >75% for core functionality
- **Type Hints**: Required for all Python code
- **Linting**: Black, isort, flake8 for Python; ESLint, Prettier for TypeScript
- **Documentation**: Comprehensive API documentation with OpenAPI/Swagger

## License

Private - Expert Trader Platform

## Contact

For questions or support, please contact the development team.
